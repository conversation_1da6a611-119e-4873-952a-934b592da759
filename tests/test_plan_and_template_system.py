# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
测试plan和模板系统的完整功能
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'deer-flow'))

from src.graph_v2.enhanced_models import (
    EnhancedPlan, 
    EnhancedStep, 
    ExecutionContext, 
    StepType,
    RetryConfig
)
from src.graph_v2.template_models import (
    PlanTemplate, 
    StepTemplate, 
    ParameterSchema, 
    ParameterType
)
from src.graph_v2.plan_converter import (
    convert_legacy_plan_to_enhanced,
    ensure_enhanced_plan,
    validate_enhanced_plan
)
from src.graph_v2.parameter_resolver import ParameterResolver, ParameterResolverFactory
from src.tools.template_tools import (
    recommend_template,
    create_plan_from_template,
    validate_template_params,
    register_template
)
from src.templates.builtin_templates import create_ai_parody_video_template


class TestEnhancedPlanModel:
    """测试增强计划模型"""
    
    def test_create_enhanced_plan(self):
        """测试创建增强计划"""
        # 创建执行上下文
        context = ExecutionContext(
            plan_id="test_plan_001",
            total_steps=2
        )
        
        # 创建步骤
        steps = [
            EnhancedStep(
                step_id="collect_materials",
                name="收集素材",
                description="收集哪吒相关图片素材",
                tool_to_use="visual_expert",
                step_type=StepType.DATA_COLLECTION,
                inputs={"character": "哪吒", "count": 10}
            ),
            EnhancedStep(
                step_id="create_video",
                name="创建视频",
                description="基于素材创建鬼畜视频",
                tool_to_use="video_expert",
                step_type=StepType.CONTENT_CREATION,
                dependencies=["collect_materials"],
                inputs={"source_materials": "{{collect_materials.assets.images}}"}
            )
        ]
        
        # 创建计划
        plan = EnhancedPlan(
            plan_id="test_plan_001",
            original_task="制作哪吒鬼畜视频",
            steps=steps,
            execution_context=context,
            is_from_template=False
        )
        
        assert plan.plan_id == "test_plan_001"
        assert len(plan.steps) == 2
        assert not plan.is_complete()
        assert not plan.has_failed()
        
        # 测试获取下一个可执行步骤
        next_steps = plan.get_next_executable_steps()
        assert len(next_steps) == 1
        assert next_steps[0].step_id == "collect_materials"
    
    def test_step_execution_flow(self):
        """测试步骤执行流程"""
        context = ExecutionContext(plan_id="test_plan_002", total_steps=1)
        
        step = EnhancedStep(
            step_id="test_step",
            name="测试步骤",
            description="测试步骤执行",
            tool_to_use="test_tool",
            step_type=StepType.CONTENT_CREATION
        )
        
        plan = EnhancedPlan(
            plan_id="test_plan_002",
            original_task="测试任务",
            steps=[step],
            execution_context=context
        )
        
        # 初始状态
        assert step.status == "pending"
        assert step.can_execute(set())
        
        # 开始执行
        step.status = "in_progress"
        step.started_at = datetime.now()
        
        # 完成执行
        step.status = "completed"
        step.completed_at = datetime.now()
        step.result = {"success": True, "output": "test result"}
        
        assert step.get_execution_duration() is not None
        assert plan.is_complete()


class TestTemplateSystem:
    """测试模板系统"""
    
    def test_create_template(self):
        """测试创建模板"""
        template = PlanTemplate(
            template_id="test_template",
            name="测试模板",
            description="用于测试的模板",
            category="test",
            parameters={
                "character": ParameterSchema(
                    type=ParameterType.STRING,
                    required=True,
                    description="角色名称"
                ),
                "duration": ParameterSchema(
                    type=ParameterType.INTEGER,
                    default=30,
                    min_value=10,
                    max_value=120,
                    description="时长"
                )
            },
            step_templates=[
                StepTemplate(
                    template_step_id="step1",
                    name="步骤1",
                    description_template="处理{character}",
                    tool_to_use="test_tool",
                    input_template={"character": "{character}", "duration": "{duration}"}
                )
            ]
        )
        
        assert template.template_id == "test_template"
        assert len(template.parameters) == 2
        assert len(template.step_templates) == 1
    
    def test_parameter_validation(self):
        """测试参数验证"""
        template = PlanTemplate(
            template_id="validation_test",
            name="验证测试",
            description="测试参数验证",
            category="test",
            parameters={
                "required_param": ParameterSchema(
                    type=ParameterType.STRING,
                    required=True
                ),
                "optional_param": ParameterSchema(
                    type=ParameterType.INTEGER,
                    default=10,
                    min_value=1,
                    max_value=100
                )
            },
            step_templates=[]
        )
        
        # 测试成功验证
        valid_params = {"required_param": "test"}
        validated = template.validate_parameters(valid_params)
        assert validated["required_param"] == "test"
        assert validated["optional_param"] == 10  # 默认值
        
        # 测试失败验证 - 缺少必需参数
        try:
            template.validate_parameters({})
            assert False, "应该抛出ValueError"
        except ValueError as e:
            assert "Required parameter" in str(e)

        # 测试失败验证 - 超出范围
        try:
            template.validate_parameters({
                "required_param": "test",
                "optional_param": 150
            })
            assert False, "应该抛出ValueError"
        except ValueError as e:
            assert "above maximum" in str(e)
    
    def test_template_instantiation(self):
        """测试模板实例化"""
        template = create_ai_parody_video_template()
        
        params = {
            "character": "哪吒",
            "duration": 30,
            "style": "modern",
            "effect_intensity": "medium"
        }
        
        plan = template.instantiate(params, "制作哪吒鬼畜视频")
        
        assert isinstance(plan, EnhancedPlan)
        assert plan.is_from_template
        assert plan.source_template == "ai_parody_video"
        assert plan.template_params["character"] == "哪吒"
        assert len(plan.steps) == 5  # AI鬼畜视频模板有5个步骤
        
        # 检查参数替换
        first_step = plan.steps[0]
        assert "哪吒" in first_step.description
        assert first_step.inputs["character_name"] == "哪吒"


class TestParameterResolver:
    """测试参数解析器"""
    
    def test_step_reference_resolution(self):
        """测试步骤引用解析"""
        # 创建带有步骤输出的计划
        context = ExecutionContext(plan_id="test_plan", total_steps=2)
        context.step_outputs["step1"] = {
            "assets": {
                "images": ["image1.jpg", "image2.jpg"]
            },
            "metadata": {"count": 2}
        }
        
        plan = EnhancedPlan(
            plan_id="test_plan",
            original_task="测试",
            steps=[],
            execution_context=context
        )
        
        resolver = ParameterResolver(plan)
        
        # 测试简单引用
        result = resolver.resolve("{{step1.assets.images}}")
        assert result == ["image1.jpg", "image2.jpg"]
        
        # 测试嵌套引用
        result = resolver.resolve("{{step1.metadata.count}}")
        assert result == 2
        
        # 测试字典中的引用
        input_dict = {
            "source_images": "{{step1.assets.images}}",
            "count": "{{step1.metadata.count}}"
        }
        resolved = resolver.resolve(input_dict)
        assert resolved["source_images"] == ["image1.jpg", "image2.jpg"]
        assert resolved["count"] == 2
    
    def test_template_parameter_resolution(self):
        """测试模板参数解析"""
        context = ExecutionContext(
            plan_id="test_plan",
            template_params={"character": "哪吒", "duration": 30}
        )
        
        plan = EnhancedPlan(
            plan_id="test_plan",
            original_task="测试",
            steps=[],
            execution_context=context
        )
        
        resolver = ParameterResolver(plan)
        
        # 测试模板参数引用
        result = resolver.resolve("处理{character}，时长{duration}秒")
        assert result == "处理哪吒，时长30秒"
    
    def test_mixed_references(self):
        """测试混合引用"""
        context = ExecutionContext(
            plan_id="test_plan",
            template_params={"character": "哪吒"},
            shared_variables={"quality": "high"}
        )
        context.step_outputs["collect"] = {"assets": {"images": ["img1.jpg"]}}
        
        plan = EnhancedPlan(
            plan_id="test_plan",
            original_task="测试",
            steps=[],
            execution_context=context
        )
        
        resolver = ParameterResolver(plan)
        
        # 测试混合引用
        template = "为{character}创建${quality}质量视频，使用素材{{collect.assets.images}}"
        result = resolver.resolve(template)
        expected = "为哪吒创建high质量视频，使用素材['img1.jpg']"
        assert result == expected


class TestTemplateTools:
    """测试模板工具"""
    
    def test_template_recommendation(self):
        """测试模板推荐"""
        # 测试鬼畜视频推荐
        result = recommend_template("制作一个哪吒鬼畜视频")
        
        assert result["recommended_template"] == "ai_parody_video"
        assert result["confidence"] > 0.8
        assert "哪吒" in result["suggested_params"].get("character", "")
    
    def test_create_plan_from_template(self):
        """测试从模板创建计划"""
        # 注册测试模板
        template = create_ai_parody_video_template()
        register_template(template)
        
        params = {
            "character": "孙悟空",
            "duration": 45,
            "style": "cartoon"
        }
        
        result = create_plan_from_template(
            template_id="ai_parody_video",
            params=params,
            user_context="制作孙悟空鬼畜视频"
        )
        
        assert result["success"] is True
        assert isinstance(result["plan"], EnhancedPlan)
        
        plan = result["plan"]
        assert plan.template_params["character"] == "孙悟空"
        assert plan.is_from_template
        assert len(plan.steps) > 0


if __name__ == "__main__":
    # 运行基本测试
    test_enhanced = TestEnhancedPlanModel()
    test_enhanced.test_create_enhanced_plan()
    test_enhanced.test_step_execution_flow()
    
    test_template = TestTemplateSystem()
    test_template.test_create_template()
    test_template.test_parameter_validation()
    test_template.test_template_instantiation()
    
    test_resolver = TestParameterResolver()
    test_resolver.test_step_reference_resolution()
    test_resolver.test_template_parameter_resolution()
    test_resolver.test_mixed_references()
    
    test_tools = TestTemplateTools()
    test_tools.test_template_recommendation()
    test_tools.test_create_plan_from_template()
    
    print("✅ 所有测试通过！Plan和模板系统运行正常。")
