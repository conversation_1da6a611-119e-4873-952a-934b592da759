#!/usr/bin/env python3
"""
测试修复后的异步工具

用于验证之前返回 "This tool can only be run asynchronously." 的工具现在能否正常工作
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append('deer-flow')

from src.config.configuration import Configuration
from src.tools import (
    get_text_to_voice_tool,
    get_text_to_video_tool, 
    get_image_to_video_tool,
    get_video_synthesis_tool
)

def test_tool_sync_call(tool, tool_name, test_args):
    """测试工具的同步调用"""
    print(f"\n🔧 测试工具: {tool_name}")
    print(f"📋 测试参数: {test_args}")
    
    try:
        # 测试同步调用
        result = tool.func(**test_args)
        
        # 检查是否还返回错误消息
        if isinstance(result, str) and "This tool can only be run asynchronously" in result:
            print(f"❌ {tool_name} 仍然返回异步错误")
            return False
        elif isinstance(result, str) and "同步执行错误" in result:
            print(f"⚠️  {tool_name} 同步包装器执行出错: {result}")
            return True  # 这是预期的，表示包装器正常工作
        else:
            print(f"✅ {tool_name} 同步调用成功")
            print(f"📄 结果类型: {type(result)}")
            return True
            
    except Exception as e:
        print(f"❌ {tool_name} 调用异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的异步工具...")
    
    # 创建配置（可能会因为缺少API密钥而失败，这是正常的）
    try:
        config = Configuration.from_runnable_config()
    except Exception as e:
        print(f"⚠️  配置创建失败: {e}")
        print("   这通常是因为缺少API密钥，但我们仍可以测试工具结构")
        config = None
    
    # 测试用例
    test_cases = [
        {
            "factory": get_text_to_voice_tool,
            "name": "design_voice_from_prompt", 
            "args": {
                "prompt": "测试语音描述",
                "preview_text": "测试文本"
            }
        },
        {
            "factory": get_text_to_video_tool,
            "name": "generate_video_from_text_idea",
            "args": {
                "prompt": "测试视频描述"
            }
        },
        {
            "factory": get_image_to_video_tool,
            "name": "generate_video_from_image", 
            "args": {
                "image_path": "test.jpg",
                "prompt": "测试动画描述"
            }
        },
        {
            "factory": get_video_synthesis_tool,
            "name": "professional_video_editor",
            "args": {
                "clips": [{"path": "test1.mp4"}, {"path": "test2.mp4"}],
                "output_filename": "test_output.mp4"
            }
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        try:
            # 创建工具实例
            tool = test_case["factory"](config) if config else None
            
            if tool is None:
                print(f"\n⚠️  工具 {test_case['name']} 无法创建（可能缺少API密钥）")
                continue
                
            # 测试同步调用
            success = test_tool_sync_call(tool, test_case["name"], test_case["args"])
            results.append((test_case["name"], success))
            
        except Exception as e:
            print(f"\n❌ 测试 {test_case['name']} 时出错: {e}")
            results.append((test_case["name"], False))
    
    # 总结结果
    print(f"\n📊 测试结果总结:")
    print("=" * 50)
    
    success_count = 0
    for tool_name, success in results:
        status = "✅ 修复成功" if success else "❌ 仍有问题"
        print(f"{status}: {tool_name}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 工具修复成功")
    
    if success_count == len(results):
        print("🎉 所有工具都已成功修复！")
    else:
        print("⚠️  部分工具仍需要修复")

if __name__ == "__main__":
    main()