# 蛙蛙多模态 Graph V2 开发者完全指南

> 一个AI Agent系统，采用极简双节点架构，实现复杂多模态创作任务的智能执行

## 📋 目录

### 🚀 [第一部分：快速上手](#第一部分快速上手)
- [30秒了解Graph V2](#30秒了解graph-v2)
- [5分钟验证系统](#5分钟验证系统)
- [核心概念速览](#核心概念速览)

### 🏗️ [第二部分：架构深度解析](#第二部分架构深度解析)
- [整体架构设计](#整体架构设计)
- [智能路由机制](#智能路由机制)
- [双节点协作模式](#双节点协作模式)

### 🔄 [第三部分：执行流程详解](#第三部分执行流程详解)
- [简单任务执行流程](#简单任务执行流程)
- [复杂任务规划流程](#复杂任务规划流程)
- [模板驱动执行流程](#模板驱动执行流程)

### 🤖 [第四部分：专家Agent体系](#第四部分专家agent体系)
- [Agent-as-Tool设计理念](#agent-as-tool设计理念)
- [Visual Expert详解](#visual-expert详解)
- [Audio Expert详解](#audio-expert详解)
- [Video Expert详解](#video-expert详解)

### 💻 [第五部分：核心模块详解](#第五部分核心模块详解)
- [模块职责矩阵](#模块职责矩阵)
- [State状态管理](#state状态管理)
- [Plan计划系统](#plan计划系统)
- [工具生态体系](#工具生态体系)

### 🎯 [第六部分：开发实战指南](#第六部分开发实战指南)
- [添加新专家工具](#添加新专家工具)
- [创建自定义模板](#创建自定义模板)
- [扩展状态管理](#扩展状态管理)

### 🔧 [第七部分：调试与运维](#第七部分调试与运维)
- [调试工具链](#调试工具链)
- [性能监控](#性能监控)
- [常见问题诊断](#常见问题诊断)

### 📚 [第八部分：最佳实践](#第八部分最佳实践)
- [开发规范](#开发规范)
- [架构原则](#架构原则)
- [性能优化](#性能优化)

---

# 第一部分：快速上手

## 30秒了解Graph V2

Graph V2是一个**极简但强大**的AI Agent系统，核心设计理念：

```
用户输入 → Master Agent → 智能路由 → 执行引擎（可选）→ 结果输出
```

**关键特点**：
- **只有2个节点**：`master_agent` + `execution_engine`
- **智能路由**：基于计划特征自动选择执行路径
- **统一入口**：所有请求都从Master Agent开始处理

## 5分钟验证系统

```python
# 测试1：简单任务（直接由Master Agent处理）
def test_simple():
    from src.graph_v2.builder import GraphBuilder

    builder = GraphBuilder()
    graph = builder.build(checkpointer=None)

    result = graph.invoke({
        "messages": [HumanMessage(content="画一只猫")]
    })
    print("✅ 简单任务测试通过")

# 测试2：复杂任务（执行引擎驱动）
def test_complex():
    result = graph.invoke({
        "messages": [HumanMessage(content="制作一个哪吒鬼畜视频")]
    })
    print("✅ 复杂任务测试通过")
```

## 核心概念速览

| 概念 | 说明 | 关键特性 |
|------|------|----------|
| **Master Agent** | 系统的大脑，负责推理和决策 | ReAct模式，工具驱动 |
| **执行引擎** | 多步任务的自动驾驶仪 | 循环驱动，状态感知 |
| **智能路由** | 基于计划特征的路径选择 | 自动判断，无需配置 |
| **Agent-as-Tool** | 专家能力的工具化封装 | 统一接口，高度模块化 |
| **模板系统** | 预定义的最佳实践流程 | 参数化，快速实例化 |

---

# 第二部分：架构深度解析

## 整体架构设计

### 核心架构图

```mermaid
graph TB
    subgraph "Graph V2 核心架构"
        A[用户输入] --> B[master_agent_node]
        B --> C{should_continue路由判断}
        C -->|should_use_execution_engine=true<br/>且plan未完成| D[execution_engine_node]
        C -->|AIMessage且无tool_calls| E[END]
        C -->|其他情况| B
        D --> F[循环调用Master Agent]
        F --> G[更新步骤状态]
        G --> H{计划完成?}
        H -->|否| F
        H -->|是| E
    end

    subgraph "should_use_execution_engine判断规则"
        I[无计划 → False]
        J[单步计划≤1 → False]
        K[模板计划 → True]
        L[多步计划>2 → True]
    end

    style B fill:#e1f5fe
    style D fill:#e8f5e8
    style C fill:#fff3e0
    style E fill:#c8e6c9
```

## 智能路由机制

### should_use_execution_engine判断逻辑

```mermaid
flowchart TD
    A[检查state.plan] --> B{计划是否存在?}
    B -->|否| C[返回False<br/>使用Master Agent]
    B -->|是| D[检查计划步骤数]
    D --> E{步骤数 ≤ 1?}
    E -->|是| C
    E -->|否| F{是否来自模板?}
    F -->|是| G[返回True<br/>使用执行引擎]
    F -->|否| H{步骤数 > 2?}
    H -->|是| G
    H -->|否| C

    style C fill:#ffcdd2
    style G fill:#c8e6c9
```

### 路由决策规则

| 条件 | 判断逻辑 | 路由结果 | 原因 |
|------|----------|----------|------|
| 无计划 | `state.plan is None` | Master Agent | 单步任务或对话 |
| 单步计划 | `len(plan.steps) ≤ 1` | Master Agent | 简单任务无需循环 |
| 模板计划 | `plan.is_from_template = True` | 执行引擎 | 预定义多步流程 |
| 多步计划 | `len(plan.steps) > 2` | 执行引擎 | 复杂任务需要循环驱动 |

## 双节点协作模式

### Master Agent工具体系架构

```mermaid
graph TB
    subgraph "Master Agent核心"
        MA[Master Agent<br/>ReAct模式]
    end

    subgraph "专家工具层 (Agent-as-Tool)"
        VE[Visual Expert<br/>图像生成/编辑]
        AE[Audio Expert<br/>音频生成/TTS]
        VDE[Video Expert<br/>视频生成/合成]
    end

    subgraph "状态管理工具"
        GCP[get_current_plan<br/>获取计划状态]
        GPS[get_next_pending_step<br/>获取下一步骤]
        USS[update_step_status<br/>更新步骤状态]
        RSI[resolve_step_inputs<br/>解析步骤输入]
    end

    subgraph "模板系统工具"
        RT[recommend_template<br/>推荐模板]
        CPT[create_plan_from_template<br/>从模板创建计划]
        GAT[get_available_templates<br/>获取可用模板]
        VTP[validate_template_params<br/>验证模板参数]
    end

    subgraph "规划工具"
        PT[planner_tool<br/>创建自定义计划]
        RVT[reviser_tool<br/>修订计划]
    end

    subgraph "理解工具"
        MU[multimodal_understanding<br/>多模态理解]
        IU[image_understanding<br/>图像理解]
        VU[video_understanding<br/>视频理解]
    end

    MA --> VE
    MA --> AE
    MA --> VDE
    MA --> GCP
    MA --> GPS
    MA --> USS
    MA --> RSI
    MA --> RT
    MA --> CPT
    MA --> GAT
    MA --> VTP
    MA --> PT
    MA --> RVT
    MA --> MU
    MA --> IU
    MA --> VU

    style MA fill:#f3e5f5
    style VE fill:#e8f5e8
    style AE fill:#fff3e0
    style VDE fill:#fce4ec
```

### 核心组件

#### 1. GraphBuilder - 图构建器
```python
class GraphBuilder:
    def __init__(self):
        # 只创建2个节点
        self.builder.add_node("master_agent", master_agent_node)
        self.builder.add_node("execution_engine", execution_engine_node)
        
        # 统一入口
        self.builder.set_entry_point("master_agent")
```

#### 2. should_continue - 智能路由器
```python
def should_continue(state: State) -> str:
    # 路由逻辑：
    # 1. 检查是否需要执行引擎
    if should_use_execution_engine(state):
        if plan and not plan.is_complete():
            return "execution_engine"
    
    # 2. 检查是否结束对话
    if isinstance(last_message, AIMessage) and not last_message.tool_calls:
        return END
    
    # 3. 继续Master Agent处理
    return "master_agent"
```

#### 3. should_use_execution_engine - 执行引擎判断
```python
def should_use_execution_engine(state: State) -> bool:
    """
    判断规则：
    1. 无计划 → False
    2. 单步计划(≤1) → False (Master Agent可处理)
    3. 模板计划 → True (预定义多步流程)
    4. 多步计划(>2) → True (需要循环执行)
    """
```

---

# 第三部分：执行流程详解

## 简单任务执行流程

### 示例：画一只猫

```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant VE as Visual Expert
    participant SM as 状态管理

    U->>MA: "画一只猫"
    MA->>SM: get_current_plan()
    SM-->>MA: "No plan available"

    Note over MA: 分析：单一视觉任务，无需规划

    MA->>VE: visual_expert("画一只可爱的猫")
    VE-->>MA: 返回猫咪图片URL
    MA-->>U: 展示生成的猫咪图片

    Note over MA: should_continue → END (AIMessage无tool_calls)
```

**详细步骤解析**：
```
1. 用户输入："画一只猫"
   ↓
2. master_agent_node
   - 调用get_current_plan() → "No plan available"
   - Master Agent分析：这是单一视觉任务，无需创建计划
   - 直接调用visual_expert工具
   - 参数：task_description="画一只可爱的猫"
   ↓
3. Visual Expert执行
   - 内部调用visual_creator Agent
   - 使用图像生成工具(如FLUX)
   - 返回结构化结果：{success: true, content: "...", assets: {...}}
   ↓
4. should_continue路由判断
   - should_use_execution_engine() → False (无计划)
   - last_message是AIMessage且无tool_calls → END
   ↓
5. 返回结果给用户
```

## 复杂任务规划流程

### 示例：画北京、上海、成都三张海报

```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant PT as Planner Tool
    participant SM as 状态管理
    participant EE as 执行引擎

    U->>MA: "画北京、上海、成都三张海报"
    MA->>SM: get_current_plan()
    SM-->>MA: "No plan available"

    Note over MA: 分析：系列化任务，需要规划

    MA->>PT: planner_tool("画北京、上海、成都三张海报")
    PT-->>MA: 返回3步计划
    MA->>SM: 保存计划到state

    Note over MA,EE: should_continue → execution_engine

    EE->>EE: 获取步骤1: create_beijing_poster
    EE->>MA: "执行步骤1：创建北京海报"
    MA->>MA: visual_expert("创建北京主题海报")
    MA->>SM: update_step_status(create_beijing_poster, completed)

    EE->>EE: 获取步骤2: create_shanghai_poster
    EE->>MA: "执行步骤2：创建上海海报"
    MA->>MA: visual_expert("创建上海主题海报")
    MA->>SM: update_step_status(create_shanghai_poster, completed)

    EE->>EE: 获取步骤3: create_chengdu_poster
    EE->>MA: "执行步骤3：创建成都海报"
    MA->>MA: visual_expert("创建成都主题海报")
    MA->>SM: update_step_status(create_chengdu_poster, completed)

    EE-->>U: 🎉 三张城市海报制作完成！
```

## 模板驱动执行流程

### 示例：制作哪吒鬼畜视频

```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant TT as 模板工具
    participant EE as 执行引擎
    participant VE as Visual Expert
    participant AE as Audio Expert
    participant VDE as Video Expert
    participant SM as 状态管理

    U->>MA: "制作一个哪吒鬼畜视频"
    MA->>SM: get_current_plan()
    SM-->>MA: "No plan available"

    MA->>TT: recommend_template("哪吒鬼畜视频")
    TT-->>MA: 推荐ai_parody_video模板(置信度0.9)

    MA->>TT: create_plan_from_template(ai_parody_video, {character: "哪吒"})
    TT-->>MA: 返回5步增强计划

    Note over MA,EE: should_continue → execution_engine (模板计划)

    EE->>EE: 步骤1: collect_materials
    EE->>MA: "执行步骤1：收集哪吒素材"
    MA->>SM: resolve_step_inputs("collect_materials")
    MA->>VE: visual_expert("收集哪吒图片素材，现代风格")
    VE-->>MA: 返回12张哪吒图片
    MA->>SM: update_step_status(collect_materials, completed)

    EE->>EE: 步骤2: create_base_character
    EE->>MA: "执行步骤2：设计角色形象"
    MA->>VE: visual_expert("设计哪吒标准形象")
    VE-->>MA: 返回角色设计图
    MA->>SM: update_step_status(create_base_character, completed)

    EE->>EE: 步骤3: create_parody_effects
    EE->>MA: "执行步骤3：制作鬼畜特效"
    MA->>VE: visual_expert("创建鬼畜特效")
    VE-->>MA: 返回特效素材
    MA->>SM: update_step_status(create_parody_effects, completed)

    EE->>EE: 步骤4: generate_background_music
    EE->>MA: "执行步骤4：生成背景音乐"
    MA->>AE: audio_expert("生成鬼畜背景音乐")
    AE-->>MA: 返回音频文件
    MA->>SM: update_step_status(generate_background_music, completed)

    EE->>EE: 步骤5: synthesize_final_video
    EE->>MA: "执行步骤5：合成最终视频"
    MA->>VDE: video_expert("合成鬼畜视频")
    VDE-->>MA: 返回最终视频
    MA->>SM: update_step_status(synthesize_final_video, completed)

    EE-->>U: 🎉 哪吒鬼畜视频制作完成！
```

---

# 第四部分：专家Agent体系

Graph V2采用创新的**Agent-as-Tool**设计模式，将复杂的专业Agent封装为简单的工具接口：

```mermaid
graph TB
    subgraph "传统设计 vs Agent-as-Tool设计"
        subgraph "传统方式"
            T1[Master Agent] --> T2[直接调用底层API]
            T2 --> T3[复杂的参数处理]
            T3 --> T4[错误处理逻辑]
        end

        subgraph "Agent-as-Tool方式"
            A1[Master Agent] --> A2[调用Expert Tool]
            A2 --> A3[Expert Agent内部处理]
            A3 --> A4[返回结构化结果]
        end
    end

    style A1 fill:#e1f5fe
    style A2 fill:#e8f5e8
    style A3 fill:#fff3e0
    style A4 fill:#c8e6c9
```

### 工具体系详解

```mermaid
graph TB
    subgraph "专家工具层 (Agent-as-Tool设计)"
        subgraph "Visual Expert"
            VE[visual_expert]
            VT1[jmeng_image_generation]
            VT2[flux_image_edit]
            VT3[multi_image_flux_edit]
            VE --> VT1
            VE --> VT2
            VE --> VT3
        end

        subgraph "Audio Expert"
            AE[audio_expert]
            AT1[suno_music_generation]
            AT2[text_to_speech]
            AT3[voice_clone]
            AT4[multi_speaker_tts]
            AE --> AT1
            AE --> AT2
            AE --> AT3
            AE --> AT4
        end

        subgraph "Video Expert"
            VDE[video_expert]
            VDT1[image_to_video]
            VDT2[text_to_video]
            VDT3[video_synthesis]
            VDE --> VDT1
            VDE --> VDT2
            VDE --> VDT3
        end
    end

    subgraph "状态管理工具"
        SM1[get_current_plan]
        SM2[get_next_pending_step]
        SM3[update_step_status]
        SM4[resolve_step_inputs]
        SM5[get_step_context]
    end

    subgraph "模板系统工具"
        TM1[recommend_template]
        TM2[create_plan_from_template]
        TM3[get_available_templates]
        TM4[validate_template_params]
    end

    subgraph "规划工具"
        PL1[planner_tool]
        PL2[reviser_tool]
    end

    subgraph "理解工具"
        UT1[multimodal_understanding]
        UT2[image_understanding]
        UT3[video_understanding]
    end

    style VE fill:#e8f5e8
    style AE fill:#fff3e0
    style VDE fill:#fce4ec
```

**工具集成代码**：
```python
# Master Agent集成的完整工具集
all_tools = (
    expert_tools +           # [visual_expert, audio_expert, video_expert]
    planning_tools +         # [planner_tool, reviser_tool]
    state_management_tools + # [get_current_plan, update_step_status, ...]
    all_template_tools +     # [recommend_template, create_plan_from_template, ...]
    understanding_tools      # [multimodal_understanding, image_understanding, ...]
)
```

## Visual Expert详解

**架构设计**：

```mermaid
graph TB
    subgraph "传统设计 vs Agent-as-Tool设计"
        subgraph "传统方式"
            T1[Master Agent] --> T2[直接调用底层API]
            T2 --> T3[复杂的参数处理]
            T3 --> T4[错误处理逻辑]
        end

        subgraph "Agent-as-Tool方式"
            A1[Master Agent] --> A2[调用Expert Tool]
            A2 --> A3[Expert Agent内部处理]
            A3 --> A4[返回结构化结果]
        end
    end

    style A1 fill:#e1f5fe
    style A2 fill:#e8f5e8
    style A3 fill:#fff3e0
    style A4 fill:#c8e6c9
```

### Visual Expert详解

**架构设计**：
```mermaid
graph TB
    subgraph "Visual Expert内部架构"
        VE[Visual Expert Tool] --> VA[Visual Creator Agent]
        VA --> VT1[jmeng_image_generation<br/>图像生成]
        VA --> VT2[flux_image_edit<br/>图像编辑]
        VA --> VT3[multi_image_flux_edit<br/>批量图像编辑]

        VA --> VP[Visual Creator Prompt<br/>专业视觉创作提示词]

        VT1 --> API1[JMENG API]
        VT2 --> API2[FLUX API]
        VT3 --> API3[FLUX Batch API]
    end

    style VE fill:#e8f5e8
    style VA fill:#c8e6c9
```

**实际代码实现**：
```python
def get_visual_expert_tool(tools_list: list) -> StructuredTool:
    # 1. 创建Visual Creator Agent
    visual_agent_instance = create_agent(
        agent_name="visual_expert_agent",
        agent_type="visual_creator",
        tools=tools_list,  # [jmeng_image_generation, flux_image_edit, ...]
        prompt_template_name="visual_creator_prompt"
    )

    # 2. 封装为工具接口
    def run_visual_expert(task_description: str, context: Optional[str] = None,
                         step_inputs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:

        # 构建Agent输入
        agent_input = {
            "messages": [HumanMessage(content=task_description)],
            "context": context,
            "step_inputs": step_inputs
        }

        # 调用Agent执行
        result = visual_agent_instance.invoke(agent_input)

        # 解析并返回结构化结果
        return {
            "success": True,
            "content": result.content,
            "assets": extract_assets(result),
            "metadata": extract_metadata(result)
        }

    return StructuredTool.from_function(func=run_visual_expert, ...)
```

### Audio Expert详解

**功能矩阵**：
```mermaid
graph TB
    subgraph "Audio Expert能力矩阵"
        AE[Audio Expert] --> AG1[音乐生成<br/>suno_music_generation]
        AE --> AG2[语音合成<br/>text_to_speech]
        AE --> AG3[声音克隆<br/>voice_clone]
        AE --> AG4[多说话人TTS<br/>multi_speaker_tts]

        AG1 --> AU1[背景音乐<br/>BGM生成]
        AG1 --> AU2[主题音乐<br/>创作]

        AG2 --> AU3[单人配音]
        AG2 --> AU4[旁白生成]

        AG3 --> AU5[特定声音<br/>模拟]

        AG4 --> AU6[对话场景<br/>多角色配音]
    end

    style AE fill:#fff3e0
    style AG1 fill:#e8f5e8
    style AG2 fill:#e8f5e8
    style AG3 fill:#e8f5e8
    style AG4 fill:#e8f5e8
```

**使用示例**：
```python
# 音乐生成示例
audio_result = audio_expert(
    task_description="为鬼畜视频生成欢快的背景音乐，30秒，电子风格",
    step_inputs={
        "duration": 30,
        "style": "electronic",
        "mood": "upbeat",
        "use_case": "background_music"
    }
)

# 多说话人TTS示例
dialogue_result = audio_expert(
    task_description="生成哪吒和龙王的对话配音",
    step_inputs={
        "speakers": [
            {"name": "哪吒", "voice_type": "young_male", "text": "我要闹海！"},
            {"name": "龙王", "voice_type": "deep_male", "text": "你这小子！"}
        ]
    }
)
```

### Video Expert详解

**处理流程**：
```mermaid
graph TB
    subgraph "Video Expert处理流程"
        VDE[Video Expert] --> VD1{输入类型判断}

        VD1 -->|图片输入| VD2[image_to_video<br/>图片转视频]
        VD1 -->|文本描述| VD3[text_to_video<br/>文本生成视频]
        VD1 -->|多素材| VD4[video_synthesis<br/>视频合成]

        VD2 --> VD5[KLING API调用]
        VD3 --> VD6[视频生成API]
        VD4 --> VD7[FFmpeg处理]

        VD5 --> VD8[后处理优化]
        VD6 --> VD8
        VD7 --> VD8

        VD8 --> VD9[返回视频URL]
    end

    style VDE fill:#fce4ec
    style VD8 fill:#c8e6c9
```

**复杂视频合成示例**：
```python
# 鬼畜视频合成
video_result = video_expert(
    task_description="将角色图片、特效素材和背景音乐合成为鬼畜视频",
    step_inputs={
        "character_images": ["{{create_base_character.assets.images}}"],
        "effect_materials": ["{{create_parody_effects.assets.images}}"],
        "background_music": "{{generate_background_music.assets.audio}}",
        "duration": 30,
        "effect_intensity": "high",
        "output_format": "mp4"
    }
)
```

### 专家工具的统一接口

所有专家工具都遵循统一的输入输出格式：

**输入格式**：
```python
class ExpertInput(BaseModel):
    task_description: str = Field(description="任务描述")
    context: Optional[str] = Field(description="上下文信息")
    step_inputs: Optional[Dict[str, Any]] = Field(description="结构化输入参数")
```

**输出格式**：
```python
{
    "success": bool,           # 执行是否成功
    "content": str,           # 主要文本内容
    "assets": {               # 生成的资源
        "images": [url1, url2, ...],
        "audio": [url1, url2, ...],
        "videos": [url1, url2, ...]
    },
    "metadata": {             # 元数据信息
        "execution_time": float,
        "model_used": str,
        "parameters": dict
    },
    "error": Optional[str]    # 错误信息（如果有）
}
```

---

# 第五部分：核心模块详解

## 模块职责矩阵

| 文件 | 核心类/函数 | 职责 | 关键特性 |
|------|------------|------|----------|
| `builder.py` | `GraphBuilder` | 构建LangGraph | 2节点架构 |
| `nodes.py` | `master_agent_node` | Master Agent实现 | 集成所有工具 |
| `execution_engine.py` | `execution_engine_node` | 多步执行驱动 | 循环调用Master Agent |
| `types.py` | `State` | 系统状态定义 | messages + plan + template字段 |
| `models.py` | `Plan`, `Step` | 传统计划模型 | 简单结构 |
| `enhanced_models.py` | `EnhancedPlan` | 增强计划模型 | 支持重试、并行等 |
| `plan_converter.py` | 转换函数 | 模型兼容性 | Legacy ↔ Enhanced |

## State状态管理

```python
class State(TypedDict):
    messages: Annotated[list, add_messages]  # 消息历史
    plan: Optional[EnhancedPlan]             # 执行计划

    # 模板系统
    template_id: Optional[str]
    template_params: Optional[Dict[str, Any]]
    template_mode: bool
    execution_mode: str

    # 执行引擎
    current_step: Optional[str]  # 当前执行步骤ID
```

## Plan计划系统

### 双模型架构

```mermaid
graph TB
    subgraph "计划模型体系"
        A[Legacy Plan] --> C[plan_converter]
        B[Enhanced Plan] --> C
        C --> D[统一的Enhanced Plan]

        subgraph "Legacy Plan特性"
            E[数字ID]
            F[简单依赖]
            G[基础状态]
        end

        subgraph "Enhanced Plan特性"
            H[语义化ID]
            I[复杂依赖]
            J[重试机制]
            K[并行执行]
        end
    end

    style D fill:#c8e6c9
```

## 工具生态体系

```mermaid
graph TB
    subgraph "工具分层架构"
        subgraph "专家工具层"
            VE[Visual Expert]
            AE[Audio Expert]
            VDE[Video Expert]
        end

        subgraph "状态管理工具"
            SM1[get_current_plan]
            SM2[update_step_status]
            SM3[resolve_step_inputs]
        end

        subgraph "模板系统工具"
            TM1[recommend_template]
            TM2[create_plan_from_template]
        end

        subgraph "规划工具"
            PL1[planner_tool]
            PL2[reviser_tool]
        end
    end

    style VE fill:#e8f5e8
    style AE fill:#fff3e0
    style VDE fill:#fce4ec
```

---

# 第六部分：开发实战指南

## 添加新专家工具

### 示例：Document Expert完整开发流程

**完整执行流程**：
```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant AE as Audio Expert
    participant SM as 状态管理

    U->>MA: "生成一首关于春天的歌"
    MA->>SM: get_current_plan()
    SM-->>MA: "No plan available"

    Note over MA: 分析：单一音频任务，直接调用专家

    MA->>AE: audio_expert("生成一首关于春天的歌")

    Note over AE: Audio Expert内部处理
    AE->>AE: 分析任务：音乐生成
    AE->>AE: 调用suno_music_generation
    AE->>AE: 参数：theme="spring", duration=180, style="pop"

    AE-->>MA: 返回音频结果
    MA-->>U: 🎵 春天主题歌曲生成完成
```

**实际代码执行**：
```python
# 1. Master Agent接收任务
task = "生成一首关于春天的歌"

# 2. 检查当前计划
current_plan = get_current_plan(state)  # 返回 "No plan available"

# 3. Master Agent分析并调用Audio Expert
audio_result = audio_expert(
    task_description="生成一首关于春天的歌",
    context=None,
    step_inputs=None
)

# 4. Audio Expert内部执行
# - 创建Audio Creator Agent
# - Agent分析任务，选择suno_music_generation工具
# - 生成参数：{"theme": "spring", "duration": 180, "style": "pop"}
# - 调用Suno API生成音乐
# - 返回结构化结果

# 5. 返回结果
{
    "success": True,
    "content": "已生成一首关于春天的流行歌曲，时长3分钟",
    "assets": {
        "audio": ["https://example.com/spring_song.mp3"]
    },
    "metadata": {
        "execution_time": 45.2,
        "model_used": "suno-v3",
        "parameters": {"theme": "spring", "duration": 180}
    }
}
```

### 例子2：Master Agent创建计划详解 - "制作公司年会宣传视频"

**任务分析流程**：
```mermaid
graph TB
    A[用户输入：制作公司年会宣传视频] --> B[Master Agent分析]
    B --> C{任务复杂度判断}
    C -->|多步骤任务| D[调用planner_tool]
    D --> E[生成执行计划]
    E --> F[保存到state.plan]
    F --> G[触发执行引擎]

    subgraph "计划生成过程"
        D --> D1[分析任务需求]
        D1 --> D2[识别关键步骤]
        D2 --> D3[确定工具依赖]
        D3 --> D4[生成步骤序列]
    end
```

**详细执行序列**：
```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant PT as Planner Tool
    participant SM as 状态管理
    participant EE as 执行引擎
    participant VE as Visual Expert
    participant AE as Audio Expert
    participant VDE as Video Expert

    U->>MA: "制作公司年会宣传视频"
    MA->>SM: get_current_plan()
    SM-->>MA: "No plan available"

    Note over MA: 分析：复杂多媒体任务，需要规划

    MA->>PT: planner_tool("制作公司年会宣传视频")

    Note over PT: Planner分析任务
    PT->>PT: 识别步骤：素材收集→脚本设计→视觉制作→音频制作→视频合成
    PT-->>MA: 返回5步计划

    MA->>SM: 保存计划到state

    Note over MA,EE: should_continue → execution_engine (多步计划>2)

    EE->>EE: 步骤1: collect_reference_materials
    EE->>MA: "执行步骤1：收集年会相关素材"
    MA->>VE: visual_expert("收集公司年会相关图片素材")
    VE-->>MA: 返回公司logo、办公场景等图片
    MA->>SM: update_step_status(collect_reference_materials, completed)

    EE->>EE: 步骤2: design_video_script
    EE->>MA: "执行步骤2：设计视频脚本"
    MA->>MA: 内部分析，生成视频脚本大纲
    MA->>SM: update_step_status(design_video_script, completed)

    EE->>EE: 步骤3: create_visual_elements
    EE->>MA: "执行步骤3：创建视觉元素"
    MA->>VE: visual_expert("创建年会宣传视频的视觉元素")
    VE-->>MA: 返回标题卡片、背景图等
    MA->>SM: update_step_status(create_visual_elements, completed)

    EE->>EE: 步骤4: generate_background_music
    EE->>MA: "执行步骤4：生成背景音乐"
    MA->>AE: audio_expert("生成企业宣传视频背景音乐")
    AE-->>MA: 返回专业背景音乐
    MA->>SM: update_step_status(generate_background_music, completed)

    EE->>EE: 步骤5: synthesize_promotional_video
    EE->>MA: "执行步骤5：合成宣传视频"
    MA->>VDE: video_expert("合成公司年会宣传视频")
    VDE-->>MA: 返回最终宣传视频
    MA->>SM: update_step_status(synthesize_promotional_video, completed)

    EE-->>U: 🎬 公司年会宣传视频制作完成！
```

### 例子3：模板系统详解 - "AI鬼畜视频模板"

**模板推荐流程**：
```mermaid
graph TB
    A[用户输入包含关键词] --> B[recommend_template分析]
    B --> C[关键词匹配]
    C --> D[计算置信度]
    D --> E{置信度>0.7?}
    E -->|是| F[推荐模板]
    E -->|否| G[建议自定义计划]
    F --> H[create_plan_from_template]
    H --> I[参数化实例化]
    I --> J[生成增强计划]
```

**AI鬼畜视频模板结构**：
```python
# 模板定义
ai_parody_video_template = PlanTemplate(
    template_id="ai_parody_video",
    name="AI鬼畜视频制作",
    description="制作具有鬼畜效果的AI视频",
    category="video_creation",
    tags=["ai", "parody", "video", "entertainment", "鬼畜"],

    parameters={
        "character": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            description="主角色名称（如：哪吒、孙悟空）"
        ),
        "duration": ParameterSchema(
            type=ParameterType.INTEGER,
            default=30,
            min_value=15,
            max_value=120,
            description="视频时长（秒）"
        ),
        "effect_intensity": ParameterSchema(
            type=ParameterType.STRING,
            default="medium",
            allowed_values=["low", "medium", "high"],
            description="特效强度"
        )
    },

    step_templates=[
        StepTemplate(
            template_step_id="collect_materials",
            name="收集素材",
            description_template="收集{character}相关图片素材",
            tool_to_use="visual_expert",
            step_type=StepType.DATA_COLLECTION
        ),
        StepTemplate(
            template_step_id="create_base_character",
            name="设计角色形象",
            description_template="设计{character}的标准形象",
            tool_to_use="visual_expert",
            step_type=StepType.CONTENT_CREATION,
            dependencies=["collect_materials"]
        ),
        # ... 更多步骤
    ]
)
```

**模板实例化过程**：
```python
# 1. 用户输入触发模板推荐
user_input = "制作一个哪吒鬼畜视频"

# 2. 模板推荐
recommendation = recommend_template(user_input)
# 返回：{"template_id": "ai_parody_video", "confidence": 0.95, "reason": "关键词匹配：鬼畜视频"}

# 3. 参数提取
extracted_params = {
    "character": "哪吒",  # 从用户输入提取
    "duration": 30,      # 使用默认值
    "effect_intensity": "medium"  # 使用默认值
}

# 4. 创建计划
enhanced_plan = create_plan_from_template(
    template_id="ai_parody_video",
    parameters=extracted_params
)

# 5. 生成的增强计划
{
    "plan_id": "ai_parody_video_20241201_001",
    "original_task": "制作一个哪吒鬼畜视频",
    "source_template": "ai_parody_video",
    "is_from_template": True,
    "steps": [
        {
            "step_id": "collect_materials",
            "name": "收集素材",
            "description": "收集哪吒相关图片素材",
            "tool_to_use": "visual_expert",
            "step_type": "data_collection",
            "status": "pending"
        },
        # ... 其他步骤
    ]
}
```

### 例子4：添加新的专家工具 - Document Expert

**完整实现流程**：

```mermaid
graph TB
    subgraph "Document Expert开发流程"
        A[1. 定义底层工具] --> B[2. 创建专家Agent]
        B --> C[3. 封装为Expert Tool]
        C --> D[4. 集成到Master Agent]
        D --> E[5. 测试验证]
    end

    subgraph "Document Expert内部架构"
        F[Document Expert Tool] --> G[Document Creator Agent]
        G --> H1[pdf_generation]
        G --> H2[word_processing]
        G --> H3[markdown_conversion]
        G --> H4[document_analysis]
    end
```

**步骤1：定义底层工具**
```python
# src/tools/document_tools.py
@tool
def pdf_generation(content: str, template: str = "default") -> str:
    """生成PDF文档"""
    # 实现PDF生成逻辑
    return pdf_url

@tool
def word_processing(action: str, content: str, **kwargs) -> str:
    """Word文档处理"""
    # 实现Word处理逻辑
    return result

@tool
def markdown_conversion(source_format: str, target_format: str, content: str) -> str:
    """文档格式转换"""
    # 实现格式转换逻辑
    return converted_content

# 工具列表
document_tools = [pdf_generation, word_processing, markdown_conversion]
```

**步骤2：创建专家Agent**
```python
# src/prompts/document_creator_prompt.md
"""
你是一个专业的文档处理专家，擅长：
1. PDF文档生成和编辑
2. Word文档处理
3. 文档格式转换
4. 文档内容分析

可用工具：
- pdf_generation: 生成PDF文档
- word_processing: 处理Word文档
- markdown_conversion: 文档格式转换

请根据用户需求选择合适的工具完成任务。
"""
```

**步骤3：封装为Expert Tool**
```python
# src/tools/experts.py
def get_document_expert_tool(tools_list: list) -> StructuredTool:
    """创建Document Expert工具"""

    # 1. 创建Document Creator Agent
    document_agent_instance = create_agent(
        agent_name="document_expert_agent",
        agent_type="document_creator",
        tools=tools_list,  # document_tools
        prompt_template_name="document_creator_prompt"
    )

    # 2. 定义工具函数
    def run_document_expert(
        task_description: str,
        context: Optional[str] = None,
        step_inputs: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Document Expert工具函数

        Args:
            task_description: 任务描述，如"生成项目报告PDF"
            context: 上下文信息
            step_inputs: 结构化输入参数

        Returns:
            标准化的专家工具输出格式
        """
        try:
            # 构建Agent输入
            agent_input = {
                "messages": [HumanMessage(content=task_description)]
            }

            # 添加上下文信息
            if context:
                agent_input["context"] = context
            if step_inputs:
                agent_input["step_inputs"] = step_inputs

            # 调用Document Creator Agent
            result = document_agent_instance.invoke(agent_input)

            # 解析结果
            assets = extract_assets_from_result(result)
            metadata = extract_metadata_from_result(result)

            return {
                "success": True,
                "content": result.content,
                "assets": {
                    "documents": assets.get("documents", []),
                    "files": assets.get("files", [])
                },
                "metadata": {
                    "execution_time": metadata.get("execution_time", 0),
                    "model_used": "document_creator_agent",
                    "tools_used": metadata.get("tools_used", []),
                    "parameters": step_inputs or {}
                }
            }

        except Exception as e:
            return {
                "success": False,
                "content": f"Document Expert执行失败: {str(e)}",
                "assets": {},
                "metadata": {},
                "error": str(e)
            }

    # 3. 创建StructuredTool
    return StructuredTool.from_function(
        func=run_document_expert,
        name="document_expert",
        description="""
        专业的文档处理工具，能够：
        1. 生成各种格式的文档（PDF、Word、Markdown等）
        2. 进行文档格式转换
        3. 分析和处理现有文档
        4. 创建专业报告和演示文稿

        适用场景：文档生成、格式转换、内容分析等任务
        """,
        args_schema=ExpertToolInput  # 统一的输入模式
    )

# 4. 获取工具实例
document_expert_tool = get_document_expert_tool(document_tools)
```

**步骤4：集成到Master Agent**
```python
# src/graph_v2/nodes.py
def master_agent_node(state: State, config: RunnableConfig):
    # 扩展专家工具列表
    expert_tools = [
        get_visual_expert_tool(tools_list=_visual_tools),
        get_audio_expert_tool(tools_list=_audio_tools),
        get_video_expert_tool(tools_list=_video_tools),
        get_document_expert_tool(tools_list=_document_tools),  # 新增
    ]

    # 其他工具保持不变
    all_tools = (
        expert_tools +
        planning_tools +
        state_management_tools +
        all_template_tools +
        understanding_tools
    )

    # 创建Master Agent
    master_agent = create_react_agent(
        name="master_agent",
        model=llm,
        tools=all_tools,
        prompt=lambda state: apply_prompt_template("master_agent_prompt", state),
    )

    return master_agent.invoke(state, config)
```

**步骤5：测试验证**
```python
# 测试Document Expert
def test_document_expert():
    """测试Document Expert功能"""

    # 测试1：简单文档生成
    result1 = document_expert(
        task_description="生成一份项目进度报告PDF",
        step_inputs={
            "project_name": "DeerFlow开发",
            "progress": "80%",
            "template": "professional"
        }
    )

    assert result1["success"] == True
    assert "documents" in result1["assets"]
    print("✅ 文档生成测试通过")

    # 测试2：格式转换
    result2 = document_expert(
        task_description="将Markdown文档转换为PDF格式",
        step_inputs={
            "source_content": "# 标题\n\n这是内容...",
            "source_format": "markdown",
            "target_format": "pdf"
        }
    )

    assert result2["success"] == True
    print("✅ 格式转换测试通过")

# 运行测试
test_document_expert()
```

**使用示例**：
```python
# 在实际任务中使用Document Expert
user_input = "帮我生成一份年度总结报告"

# Master Agent会自动调用Document Expert
result = graph.invoke({
    "messages": [HumanMessage(content=user_input)]
})

# Document Expert内部处理：
# 1. 分析任务：需要生成报告文档
# 2. 选择工具：pdf_generation
# 3. 生成参数：{"template": "annual_report", "sections": ["summary", "achievements", "plans"]}
# 4. 调用PDF生成API
# 5. 返回结构化结果
```

### 例子2：创建新模板

```python
def create_blog_article_template():
    return PlanTemplate(
        template_id="blog_article",
        name="博客文章生成",
        description="生成高质量博客文章",
        category="content_creation",
        
        parameters={
            "topic": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="文章主题"
            ),
            "word_count": ParameterSchema(
                type=ParameterType.INTEGER,
                default=1500,
                min_value=500,
                max_value=5000,
                description="文章字数"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="research_topic",
                name="主题研究",
                description_template="研究{topic}相关信息",
                tool_to_use="web_search_expert",
                step_type=StepType.DATA_COLLECTION
            ),
            StepTemplate(
                template_step_id="write_article",
                name="撰写文章",
                description_template="撰写关于{topic}的{word_count}字文章",
                tool_to_use="document_expert",
                step_type=StepType.CONTENT_CREATION,
                dependencies=["research_topic"]
            )
        ]
    )

# 注册模板
register_template(create_blog_article_template())
```

### 例子3：调试执行流程

```python
def debug_execution():
    """调试执行引擎"""
    
    # 1. 检查计划状态
    plan = state.get("plan")
    if plan:
        print(f"计划步骤: {len(plan.steps)}")
        print(f"步骤状态: {[s.status for s in plan.steps]}")
        print(f"是否完成: {plan.is_complete()}")
    
    # 2. 检查路由判断
    use_engine = should_use_execution_engine(state)
    print(f"使用执行引擎: {use_engine}")
    
    # 3. 检查当前步骤
    current_step = state.get("current_step")
    if current_step:
        print(f"当前执行步骤: {current_step}")
```

## 创建自定义模板

### 示例：博客文章生成模板

```python
def create_blog_article_template():
    return PlanTemplate(
        template_id="blog_article",
        name="博客文章生成",
        description="生成高质量博客文章",
        category="content_creation",

        parameters={
            "topic": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="文章主题"
            ),
            "word_count": ParameterSchema(
                type=ParameterType.INTEGER,
                default=1500,
                min_value=500,
                max_value=5000,
                description="文章字数"
            )
        },

        step_templates=[
            StepTemplate(
                template_step_id="research_topic",
                name="主题研究",
                description_template="研究{topic}相关信息",
                tool_to_use="web_search_expert",
                step_type=StepType.DATA_COLLECTION
            ),
            StepTemplate(
                template_step_id="write_article",
                name="撰写文章",
                description_template="撰写关于{topic}的{word_count}字文章",
                tool_to_use="document_expert",
                step_type=StepType.CONTENT_CREATION,
                dependencies=["research_topic"]
            )
        ]
    )

# 注册模板
register_template(create_blog_article_template())
```

## 扩展状态管理

### 添加新的状态管理工具

```python
@tool
def get_execution_metrics(state: State) -> str:
    """获取执行指标"""
    plan = state.get("plan")
    if not plan:
        return "无执行计划"

    metrics = {
        "total_steps": len(plan.steps),
        "completed": len([s for s in plan.steps if s.status == "completed"]),
        "failed": len([s for s in plan.steps if s.status == "failed"])
    }
    return json.dumps(metrics, ensure_ascii=False)

# 添加到state_management_tools列表
```

---

# 第七部分：调试与运维

## 调试工具链

### 环境设置
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件，填入API密钥

# 3. 验证安装
python -c "from src.graph_v2.builder import GraphBuilder; print('✅ 安装成功')"
```

### 常用开发任务

#### 修改Master Agent提示词
```python
# 编辑 src/prompts/master_agent_prompt.md
# 提示词会通过apply_prompt_template自动应用
```

#### 添加新的状态管理工具
```python
@tool
def get_execution_metrics(state: State) -> str:
    """获取执行指标"""
    plan = state.get("plan")
    if not plan:
        return "无执行计划"
    
    metrics = {
        "total_steps": len(plan.steps),
        "completed": len([s for s in plan.steps if s.status == "completed"]),
        "failed": len([s for s in plan.steps if s.status == "failed"])
    }
    return json.dumps(metrics, ensure_ascii=False)

# 添加到state_management_tools列表
```

#### 扩展执行引擎功能
```python
# 在execution_engine.py中修改主循环
def execution_engine_node(state: State) -> State:
    # 添加自定义逻辑
    if custom_condition:
        # 处理特殊情况
        pass
    
    # 原有循环逻辑...
```

## 性能监控

### 执行流程可视化调试

```mermaid
graph TB
    subgraph "调试工具链"
        A[日志系统] --> B[状态监控]
        B --> C[执行追踪]
        C --> D[性能分析]
        D --> E[错误诊断]
    end

    subgraph "调试检查点"
        F[Master Agent入口]
        G[工具调用前]
        H[工具调用后]
        I[路由判断点]
        J[执行引擎循环]
    end
```

## 常见问题诊断

#### 1. 执行引擎无限循环
**症状**: 执行引擎一直循环不结束
**原因**: Master Agent没有调用`update_step_status`
**解决**: 
```python
# 检查Master Agent的工具调用
print(f"最后一条消息: {state['messages'][-1]}")
print(f"是否有工具调用: {hasattr(state['messages'][-1], 'tool_calls')}")
```

#### 2. 计划转换失败
**症状**: `ensure_enhanced_plan`报错
**原因**: Legacy Plan格式不兼容
**解决**:
```python
try:
    enhanced_plan = ensure_enhanced_plan(plan)
except Exception as e:
    print(f"转换失败: {e}")
    # 重新创建计划
```

#### 3. 模板实例化失败
**症状**: `create_plan_from_template`返回错误
**原因**: 参数验证不通过
**解决**:
```python
# 先验证参数
validation = template.validate_parameters(params)
if not validation.is_valid:
    print(f"参数错误: {validation.error_message}")
```

### 调试技巧和工具

#### 1. 执行流程可视化调试

```mermaid
graph TB
    subgraph "调试工具链"
        A[日志系统] --> B[状态监控]
        B --> C[执行追踪]
        C --> D[性能分析]
        D --> E[错误诊断]
    end

    subgraph "调试检查点"
        F[Master Agent入口]
        G[工具调用前]
        H[工具调用后]
        I[路由判断点]
        J[执行引擎循环]
    end
```

```python
# 1. 启用详细日志系统
import logging
from src.utils.debug_logger import setup_debug_logging

# 配置调试日志
setup_debug_logging(
    level=logging.DEBUG,
    log_file="graph_v2_debug.log",
    include_modules=["graph_v2", "tools", "agents"]
)

# 2. 状态变化监控器
class StateMonitor:
    def __init__(self):
        self.state_history = []

    def log_state_change(self, node_name: str, state_before: State, state_after: State):
        """记录状态变化"""
        changes = self._detect_changes(state_before, state_after)

        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "node": node_name,
            "changes": changes,
            "state_snapshot": self._create_snapshot(state_after)
        }

        self.state_history.append(log_entry)

        # 打印重要变化
        if changes:
            print(f"🔄 [{node_name}] 状态变化:")
            for change in changes:
                print(f"   {change}")

    def _detect_changes(self, before: State, after: State) -> List[str]:
        changes = []

        # 检查计划变化
        if before.get("plan") != after.get("plan"):
            if not before.get("plan") and after.get("plan"):
                changes.append(f"✅ 创建新计划: {after['plan'].plan_id}")
            elif before.get("plan") and after.get("plan"):
                changes.append(f"📝 计划更新: {after['plan'].plan_id}")

        # 检查当前步骤变化
        if before.get("current_step") != after.get("current_step"):
            old_step = before.get("current_step", "None")
            new_step = after.get("current_step", "None")
            changes.append(f"🎯 步骤变化: {old_step} → {new_step}")

        # 检查消息变化
        old_msg_count = len(before.get("messages", []))
        new_msg_count = len(after.get("messages", []))
        if old_msg_count != new_msg_count:
            changes.append(f"💬 消息数量: {old_msg_count} → {new_msg_count}")

        return changes

# 3. 执行流程模拟器
class ExecutionSimulator:
    def __init__(self):
        self.monitor = StateMonitor()

    def simulate_execution(self, user_input: str, debug_mode: bool = True):
        """模拟完整执行流程"""
        print(f"🚀 开始模拟执行: {user_input}")

        # 初始状态
        state = {"messages": [HumanMessage(content=user_input)]}

        iteration = 0
        max_iterations = 10  # 防止无限循环

        while iteration < max_iterations:
            iteration += 1
            print(f"\n--- 第 {iteration} 轮执行 ---")

            # 保存执行前状态
            state_before = copy.deepcopy(state)

            # 执行Master Agent
            print("🧠 调用 Master Agent...")
            state = master_agent_node(state, config)

            # 记录状态变化
            self.monitor.log_state_change("master_agent", state_before, state)

            # 路由判断
            next_node = should_continue(state)
            print(f"🔀 路由决策: {next_node}")

            if next_node == END:
                print("✅ 执行完成")
                break
            elif next_node == "execution_engine":
                print("⚙️ 调用执行引擎...")
                state_before_engine = copy.deepcopy(state)
                state = execution_engine_node(state)
                self.monitor.log_state_change("execution_engine", state_before_engine, state)
            elif next_node == "master_agent":
                print("🔄 继续Master Agent处理...")
                continue
            else:
                print(f"❌ 未知路由: {next_node}")
                break

        if iteration >= max_iterations:
            print("⚠️ 达到最大迭代次数，可能存在无限循环")

        return state, self.monitor.state_history

# 4. 性能分析器
class PerformanceAnalyzer:
    def __init__(self):
        self.metrics = {}

    def analyze_execution(self, state_history: List[Dict]):
        """分析执行性能"""

        # 统计节点调用次数
        node_calls = {}
        for entry in state_history:
            node = entry["node"]
            node_calls[node] = node_calls.get(node, 0) + 1

        # 统计工具调用
        tool_calls = self._extract_tool_calls(state_history)

        # 计算执行时间
        if state_history:
            start_time = datetime.fromisoformat(state_history[0]["timestamp"])
            end_time = datetime.fromisoformat(state_history[-1]["timestamp"])
            total_time = (end_time - start_time).total_seconds()
        else:
            total_time = 0

        return {
            "total_execution_time": total_time,
            "node_call_counts": node_calls,
            "tool_call_counts": tool_calls,
            "total_iterations": len(state_history)
        }

# 5. 错误诊断器
class ErrorDiagnostic:
    def diagnose_common_issues(self, state: State, error: Exception = None):
        """诊断常见问题"""
        issues = []

        # 检查1：执行引擎无限循环
        plan = state.get("plan")
        if plan and not plan.is_complete():
            pending_steps = [s for s in plan.steps if s.status == "pending"]
            if len(pending_steps) > 0:
                current_step = state.get("current_step")
                if not current_step:
                    issues.append({
                        "type": "execution_engine_stuck",
                        "description": "执行引擎可能卡住：有待执行步骤但current_step为空",
                        "solution": "检查get_next_pending_step函数逻辑"
                    })

        # 检查2：计划转换问题
        if plan and hasattr(plan, '__dict__'):
            if not hasattr(plan, 'is_complete'):
                issues.append({
                    "type": "plan_model_mismatch",
                    "description": "计划模型不匹配：缺少is_complete方法",
                    "solution": "使用ensure_enhanced_plan转换计划模型"
                })

        # 检查3：工具调用失败
        if error and "tool" in str(error).lower():
            issues.append({
                "type": "tool_call_failure",
                "description": f"工具调用失败: {str(error)}",
                "solution": "检查工具参数和API配置"
            })

        return issues

# 使用示例
def debug_execution_example():
    """调试执行示例"""

    # 创建调试工具
    simulator = ExecutionSimulator()
    analyzer = PerformanceAnalyzer()
    diagnostic = ErrorDiagnostic()

    try:
        # 模拟执行
        final_state, history = simulator.simulate_execution(
            "制作一个哪吒鬼畜视频",
            debug_mode=True
        )

        # 性能分析
        metrics = analyzer.analyze_execution(history)
        print(f"\n📊 性能分析:")
        print(f"   总执行时间: {metrics['total_execution_time']:.2f}秒")
        print(f"   节点调用次数: {metrics['node_call_counts']}")
        print(f"   总迭代次数: {metrics['total_iterations']}")

        # 问题诊断
        issues = diagnostic.diagnose_common_issues(final_state)
        if issues:
            print(f"\n⚠️ 发现问题:")
            for issue in issues:
                print(f"   {issue['type']}: {issue['description']}")
                print(f"   解决方案: {issue['solution']}")
        else:
            print("\n✅ 未发现问题")

    except Exception as e:
        print(f"\n❌ 执行出错: {str(e)}")
        issues = diagnostic.diagnose_common_issues({}, e)
        for issue in issues:
            print(f"   可能原因: {issue['description']}")
            print(f"   建议解决: {issue['solution']}")
```

---

# 第八部分：最佳实践

## 开发规范

## 架构原则

### Graph V2核心优势

```mermaid
mindmap
  root((Graph V2优势))
    架构设计
      极简架构
        只有2个核心节点
        易于理解和维护
        降低复杂度
      智能路由
        基于计划特征判断
        自动选择执行路径
        无需手动配置
    扩展性
      Agent-as-Tool
        专家能力封装
        统一接口标准
        高度模块化
      工具生态
        丰富的工具集
        易于添加新工具
        支持复杂组合
    可靠性
      状态管理
        完整的状态追踪
        执行历史记录
        错误恢复机制
      模板系统
        预定义最佳实践
        参数化配置
        降低出错概率
```

### 开发最佳实践

#### 1. 架构设计原则

**DO ✅**:
- 新功能优先考虑添加工具，而不是修改核心架构
- 使用Agent-as-Tool模式封装复杂逻辑
- 通过模板定义复杂流程，避免硬编码
- 充分利用状态管理工具进行调试和监控

**DON'T ❌**:
- 不要直接修改should_continue路由逻辑
- 不要在Master Agent中硬编码业务逻辑
- 不要绕过状态管理工具直接修改state
- 不要创建超过必要的新节点

#### 2. 工具开发规范

```python
# ✅ 正确的工具开发模式
class NewExpertTool:
    """新专家工具的标准实现"""

    def __init__(self, tools_list: List):
        self.agent = create_agent(
            agent_name="new_expert_agent",
            agent_type="new_expert",
            tools=tools_list,
            prompt_template_name="new_expert_prompt"
        )

    def __call__(self, task_description: str, **kwargs) -> Dict[str, Any]:
        """统一的调用接口"""
        try:
            result = self.agent.invoke({
                "messages": [HumanMessage(content=task_description)],
                **kwargs
            })

            return {
                "success": True,
                "content": result.content,
                "assets": self._extract_assets(result),
                "metadata": self._extract_metadata(result)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "content": f"执行失败: {str(e)}",
                "assets": {},
                "metadata": {}
            }
```

#### 3. 模板设计指南

```python
# ✅ 优秀的模板设计
def create_excellent_template():
    return PlanTemplate(
        # 1. 清晰的标识和描述
        template_id="clear_meaningful_id",
        name="用户友好的名称",
        description="详细的功能描述",

        # 2. 合理的参数设计
        parameters={
            "required_param": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="清晰的参数说明",
                validation_pattern=r"^[a-zA-Z0-9_]+$"  # 参数验证
            ),
            "optional_param": ParameterSchema(
                type=ParameterType.INTEGER,
                default=10,
                min_value=1,
                max_value=100,
                description="可选参数，有默认值和范围限制"
            )
        },

        # 3. 逻辑清晰的步骤设计
        step_templates=[
            StepTemplate(
                template_step_id="meaningful_step_id",
                name="步骤名称",
                description_template="使用{参数}的描述模板",
                tool_to_use="appropriate_expert_tool",
                step_type=StepType.DATA_COLLECTION,
                dependencies=[]  # 明确的依赖关系
            )
        ]
    )
```

#### 4. 调试和监控策略

```python
# ✅ 完整的调试策略
class DebuggingStrategy:
    def __init__(self):
        self.enable_logging()
        self.setup_monitoring()

    def enable_logging(self):
        """启用分层日志"""
        logging.getLogger("graph_v2.master_agent").setLevel(logging.DEBUG)
        logging.getLogger("graph_v2.execution_engine").setLevel(logging.INFO)
        logging.getLogger("tools.experts").setLevel(logging.DEBUG)

    def setup_monitoring(self):
        """设置监控检查点"""
        # 在关键节点添加监控
        pass

    def validate_state(self, state: State) -> List[str]:
        """状态验证"""
        issues = []

        # 检查必要字段
        if "messages" not in state:
            issues.append("缺少messages字段")

        # 检查计划一致性
        plan = state.get("plan")
        current_step = state.get("current_step")

        if current_step and plan:
            step_exists = any(s.step_id == current_step for s in plan.steps)
            if not step_exists:
                issues.append(f"current_step '{current_step}' 在计划中不存在")

        return issues
```

## 性能优化

#### 1. 执行效率优化

```mermaid
graph TB
    subgraph "性能优化策略"
        A[并行执行] --> A1[识别可并行步骤]
        A --> A2[使用parallel_group]

        B[缓存机制] --> B1[结果缓存]
        B --> B2[模板缓存]

        C[资源管理] --> C1[连接池]
        C --> C2[内存优化]

        D[智能重试] --> D1[指数退避]
        D --> D2[错误分类]
    end
```

#### 2. 扩展性设计

```python
# ✅ 可扩展的架构设计
class ExtensibleArchitecture:
    """可扩展架构示例"""

    def __init__(self):
        self.expert_registry = {}
        self.template_registry = {}
        self.tool_registry = {}

    def register_expert(self, name: str, expert_class: Type):
        """动态注册专家"""
        self.expert_registry[name] = expert_class

    def register_template(self, template: PlanTemplate):
        """动态注册模板"""
        self.template_registry[template.template_id] = template

    def get_available_experts(self) -> List[str]:
        """获取可用专家列表"""
        return list(self.expert_registry.keys())
```

### 🔮 未来发展方向

1. **多模态融合**: 更深度的视觉、音频、视频专家协作
2. **智能优化**: 基于历史执行数据的自动优化
3. **分布式执行**: 支持大规模并行处理
4. **用户个性化**: 基于用户偏好的模板推荐
5. **实时协作**: 多用户协同创作支持

### 🎉 结语

Graph V2框架代表了AI Agent系统设计的最前沿实践：

- **简单而不简陋**: 2节点架构隐藏复杂性，暴露简洁性
- **强大而不臃肿**: Agent-as-Tool设计实现高度模块化
- **智能而不黑盒**: 完整的状态管理和执行追踪
- **灵活而不混乱**: 模板系统标准化复杂流程

这个架构让复杂的多模态AI创作变得简单可控，是现代AI应用开发的最佳实践。无论是简单的单步任务，还是复杂的多步骤创作流程，Graph V2都能优雅地处理，为开发者提供了一个强大而易用的AI Agent平台。

**开始你的Graph V2开发之旅吧！** 🚀
