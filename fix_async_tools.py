#!/usr/bin/env python3
"""
修复异步工具调用问题的脚本

问题：某些工具只定义了异步版本，导致在同步上下文中调用时返回
"This tool can only be run asynchronously." 错误

解决方案：为这些工具添加同步包装器
"""

import asyncio
from functools import partial
from typing import Any, Callable

def create_sync_wrapper(async_func: Callable) -> Callable:
    """
    为异步函数创建同步包装器
    
    这个函数会在同步上下文中运行异步函数
    """
    def sync_wrapper(*args, **kwargs):
        try:
            # 检查是否已经在运行的事件循环中
            loop = asyncio.get_running_loop()
            # 如果在运行的循环中，我们需要使用不同的方法
            # 但这通常意味着我们应该使用异步版本
            return "Error: Cannot run async function in sync context when event loop is running"
        except RuntimeError:
            # 没有运行的事件循环，我们可以创建一个新的
            return asyncio.run(async_func(*args, **kwargs))
    
    return sync_wrapper

# 需要修复的工具列表
ASYNC_ONLY_TOOLS = [
    'design_voice_from_prompt',
    'generate_video_from_text_idea', 
    'image_to_video',
    'video_synthesis'
]

def fix_tool_definition(tool_factory_func, async_impl_func):
    """
    修复工具定义的模板函数
    
    Args:
        tool_factory_func: 工具工厂函数
        async_impl_func: 异步实现函数
    """
    # 创建同步包装器
    sync_impl = create_sync_wrapper(async_impl_func)
    
    # 返回修复后的工具定义
    return {
        'func': sync_impl,           # 同步版本
        'coroutine': async_impl_func, # 异步版本
    }

print("🔧 DeerFlow 异步工具修复指南")
print("=" * 50)
print()

print("❌ 问题描述:")
print("某些工具返回 'This tool can only be run asynchronously.' 错误")
print()

print("🔍 问题根源:")
print("工具定义中 func 参数设置为错误消息，而不是实际的同步实现")
print()

print("✅ 解决方案:")
print("1. 方案一：添加同步包装器（简单但可能阻塞）")
print("2. 方案二：确保 Agent 始终使用异步调用（推荐）")
print("3. 方案三：重新设计工具为同步优先")
print()

print("🛠️ 具体修复步骤:")
print()

print("步骤 1: 修改工具定义文件")
print("文件: src/tools/audio/text_to_speech.py (行 572)")
print("文件: src/tools/video/text_to_video.py (行 185)")
print("文件: src/tools/video/image_to_video.py (行 203)")
print("文件: src/tools/video/synthesis.py (行 278)")
print()

print("原来的代码:")
print("""
return StructuredTool.from_function(
    func=lambda *args, **kwargs: "This tool can only be run asynchronously.",  # ❌
    coroutine=partial(_async_function, config),
    name="tool_name",
    # ...
)
""")

print("修复后的代码:")
print("""
# 添加同步包装器
def _sync_wrapper(*args, **kwargs):
    try:
        return asyncio.run(partial(_async_function, config)(*args, **kwargs))
    except Exception as e:
        return f"同步执行错误: {str(e)}"

return StructuredTool.from_function(
    func=_sync_wrapper,  # ✅ 同步包装器
    coroutine=partial(_async_function, config),  # ✅ 异步版本
    name="tool_name",
    # ...
)
""")

print()
print("步骤 2: 检查 Agent 配置")
print("确保 LangGraph 的 ReAct Agent 正确处理异步工具调用")
print()

print("⚠️ 注意事项:")
print("- 同步包装器可能会阻塞事件循环")
print("- 建议优先使用异步版本")
print("- 考虑将长时间运行的任务改为异步优先设计")
print()

print("🔧 立即修复命令:")
print("请手动编辑上述文件，或运行以下 sed 命令进行批量替换")