# 🦌 Graph V2 快速参考指南

> 这是一个简化的参考指南，帮助快速理解Graph V2的核心概念和使用方法

## 🚀 30秒快速理解

### 核心架构
```
用户输入 → Master Agent → 智能路由 → 执行引擎（可选）→ 结果输出
```

### 关键特点
- **2个节点**: `master_agent` + `execution_engine`
- **智能路由**: 基于计划特征自动选择路径
- **Agent-as-Tool**: 专家能力工具化封装

## 🔄 执行流程类型

### 1. 简单任务（Master Agent直接处理）
```
"画一只猫" → Master Agent → Visual Expert → 返回结果
```

### 2. 复杂任务（执行引擎驱动）
```
"制作宣传视频" → Master Agent创建计划 → 执行引擎循环驱动 → 完成所有步骤
```

### 3. 模板任务（模板系统）
```
"制作鬼畜视频" → 推荐模板 → 实例化计划 → 执行引擎驱动
```

## 🤖 专家工具体系

| 专家 | 功能 | 底层工具 |
|------|------|----------|
| **Visual Expert** | 图像生成/编辑 | jmeng_image_generation, flux_image_edit |
| **Audio Expert** | 音频生成/TTS | suno_music_generation, text_to_speech |
| **Video Expert** | 视频生成/合成 | image_to_video, video_synthesis |

## 🛠️ 核心工具类型

### 状态管理工具
- `get_current_plan()` - 获取当前计划
- `update_step_status()` - 更新步骤状态
- `resolve_step_inputs()` - 解析步骤输入

### 模板系统工具
- `recommend_template()` - 推荐模板
- `create_plan_from_template()` - 从模板创建计划

### 规划工具
- `planner_tool()` - 创建自定义计划
- `reviser_tool()` - 修订计划

## 📋 State结构

```python
class State(TypedDict):
    messages: list                    # 消息历史
    plan: Optional[EnhancedPlan]     # 执行计划
    current_step: Optional[str]      # 当前步骤ID
    template_id: Optional[str]       # 模板ID
    execution_mode: str              # 执行模式
```

## 🔧 快速开发

### 添加新专家工具
```python
# 1. 创建Agent
expert_agent = create_agent(
    agent_name="new_expert",
    tools=new_tools,
    prompt_template_name="new_expert_prompt"
)

# 2. 封装为工具
def new_expert_tool(task_description: str) -> Dict:
    result = expert_agent.invoke({"messages": [HumanMessage(content=task_description)]})
    return {"success": True, "content": result.content, "assets": {...}}

# 3. 注册到Master Agent
expert_tools.append(StructuredTool.from_function(new_expert_tool))
```

### 创建新模板
```python
template = PlanTemplate(
    template_id="new_template",
    name="新模板",
    parameters={"param1": ParameterSchema(...)},
    step_templates=[
        StepTemplate(
            template_step_id="step1",
            tool_to_use="expert_tool",
            description_template="执行{param1}任务"
        )
    ]
)
```

## 🚨 常见问题

### 执行引擎无限循环
**原因**: Master Agent没有调用`update_step_status`
**解决**: 检查工具调用日志，确保状态更新

### 计划转换失败
**原因**: Legacy Plan格式不兼容
**解决**: 使用`ensure_enhanced_plan`转换

### 模板实例化失败
**原因**: 参数验证不通过
**解决**: 使用`validate_template_params`检查参数

## 🎯 最佳实践

### DO ✅
- 新功能优先添加工具，不修改核心架构
- 使用Agent-as-Tool模式封装复杂逻辑
- 通过模板定义复杂流程
- 充分利用状态管理工具调试

### DON'T ❌
- 不要直接修改should_continue路由逻辑
- 不要在Master Agent中硬编码业务逻辑
- 不要绕过状态管理工具直接修改state
- 不要创建超过必要的新节点

## 📚 完整文档

详细内容请参考：[GRAPH_V2_DEVELOPER_GUIDE.md](./GRAPH_V2_DEVELOPER_GUIDE.md)

---

**Graph V2**: 简单而强大的AI Agent系统 🚀
