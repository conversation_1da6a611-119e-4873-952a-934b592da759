#!/usr/bin/env python3
"""
测试静态常量修复效果 - 这可能是解决JSON解析错误的关键！
"""

import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_static_constants_fix():
    """测试静态常量修复效果"""
    logger.info("🔧 测试静态常量修复效果...")
    
    try:
        # 直接导入模块避免循环导入
        import sys
        import importlib.util
        
        # 直接加载multi_speaker_tts模块
        spec = importlib.util.spec_from_file_location(
            "multi_speaker_tts", 
            "/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py"
        )
        multi_speaker_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(multi_speaker_module)
        
        # 检查修复内容
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了动态引用
        static_checks = {
            "移除MINIMAX_PARAM_RANGES导入": "MINIMAX_PARAM_RANGES" not in content,
            "移除MINIMAX_EMOTIONS导入": "MINIMAX_EMOTIONS" not in content,
            "移除动态speed范围": 'MINIMAX_PARAM_RANGES["speed"]' not in content,
            "移除动态vol范围": 'MINIMAX_PARAM_RANGES["vol"]' not in content,
            "移除动态pitch范围": 'MINIMAX_PARAM_RANGES["pitch"]' not in content,
            "使用静态speed范围": "ge=0.5" in content and "le=2.0" in content,
            "使用静态vol范围": "le=10.0" in content,
            "使用静态pitch范围": "ge=-12" in content and "le=12" in content,
            "使用静态emotion描述": "calm, happy, sad, angry, fearful, disgusted, surprised" in content
        }
        
        logger.info("🔍 静态常量修复检查:")
        all_fixed = True
        for check_name, passed in static_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                all_fixed = False
        
        return all_fixed
        
    except Exception as e:
        logger.error(f"❌ 静态常量修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_creation_and_serialization():
    """测试工具创建和序列化"""
    logger.info("🛠️ 测试工具创建和序列化...")
    
    try:
        # 直接导入模块避免循环导入
        import sys
        import importlib.util
        
        # 加载配置模块
        config_spec = importlib.util.spec_from_file_location(
            "configuration", 
            "/Users/<USER>/openArt-1/deer-flow/src/config/configuration.py"
        )
        config_module = importlib.util.module_from_spec(config_spec)
        config_spec.loader.exec_module(config_module)
        
        # 加载multi_speaker_tts模块
        spec = importlib.util.spec_from_file_location(
            "multi_speaker_tts", 
            "/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py"
        )
        multi_speaker_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(multi_speaker_module)
        
        # 创建配置
        config = config_module.Configuration.from_runnable_config()
        
        # 尝试创建工具
        tool = multi_speaker_module.get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.info("✅ 工具正确返回None（配置缺失时的预期行为）")
            logger.info("📋 这说明工具逻辑正常，只是缺少MINIMAX配置")
            
            # 即使工具为None，我们也可以测试args_schema
            try:
                input_class = multi_speaker_module.MultiSpeakerTTSInput
                logger.info("✅ MultiSpeakerTTSInput类加载成功")
                
                # 测试创建一个实例
                test_input = input_class(
                    dialogue_script=[{"speaker": "测试", "text": "测试文本"}]
                )
                logger.info("✅ MultiSpeakerTTSInput实例创建成功")
                
                # 测试序列化
                input_dict = test_input.dict()
                logger.info("✅ MultiSpeakerTTSInput序列化成功")
                
                # 测试JSON序列化
                json_str = json.dumps(input_dict, ensure_ascii=False)
                logger.info(f"✅ JSON序列化成功，长度: {len(json_str)} 字符")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ args_schema测试失败: {e}")
                return False
        else:
            logger.info("✅ 工具创建成功")
            logger.info(f"📋 工具名称: {tool.name}")
            logger.info(f"📋 描述长度: {len(tool.description)} 字符")
            
            # 测试完整的工具序列化
            try:
                tool_dict = {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": {"type": "object", "properties": {}}
                    }
                }
                json_str = json.dumps(tool_dict, ensure_ascii=False)
                logger.info(f"✅ 完整工具JSON序列化成功，长度: {len(json_str)} 字符")
                
                # 检查JSON长度
                if len(json_str) > 2000:
                    logger.warning(f"⚠️ JSON较长: {len(json_str)} 字符")
                else:
                    logger.info(f"✅ JSON长度合理: {len(json_str)} 字符")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ 工具JSON序列化失败: {e}")
                return False
        
    except Exception as e:
        logger.error(f"❌ 工具创建和序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pydantic_field_validation():
    """测试Pydantic字段验证"""
    logger.info("📋 测试Pydantic字段验证...")
    
    try:
        # 直接导入模块避免循环导入
        import sys
        import importlib.util
        
        # 加载multi_speaker_tts模块
        spec = importlib.util.spec_from_file_location(
            "multi_speaker_tts", 
            "/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py"
        )
        multi_speaker_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(multi_speaker_module)
        
        # 获取输入类
        input_class = multi_speaker_module.MultiSpeakerTTSInput
        
        # 测试各种参数值
        test_cases = [
            # 正常值
            {"speed": 1.0, "vol": 1.0, "pitch": 0, "emotion": "calm"},
            # 边界值
            {"speed": 0.5, "vol": 0.1, "pitch": -12, "emotion": "happy"},
            {"speed": 2.0, "vol": 10.0, "pitch": 12, "emotion": "sad"},
        ]
        
        for i, params in enumerate(test_cases):
            try:
                test_input = input_class(
                    dialogue_script=[{"speaker": "测试", "text": "测试文本"}],
                    **params
                )
                logger.info(f"✅ 测试用例 {i+1} 验证成功: {params}")
            except Exception as e:
                logger.error(f"❌ 测试用例 {i+1} 验证失败: {params} - {e}")
                return False
        
        # 测试无效值（应该失败）
        invalid_cases = [
            {"speed": 0.1},  # 太小
            {"speed": 3.0},  # 太大
            {"vol": 0.0},    # 太小
            {"vol": 15.0},   # 太大
            {"pitch": -15},  # 太小
            {"pitch": 15},   # 太大
        ]
        
        for i, params in enumerate(invalid_cases):
            try:
                test_input = input_class(
                    dialogue_script=[{"speaker": "测试", "text": "测试文本"}],
                    **params
                )
                logger.warning(f"⚠️ 无效测试用例 {i+1} 意外通过: {params}")
            except Exception as e:
                logger.info(f"✅ 无效测试用例 {i+1} 正确拒绝: {params}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Pydantic字段验证测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始静态常量修复验证...")
    logger.info("这可能是解决JSON解析错误的关键修复！")
    
    tests = [
        ("静态常量修复检查", test_static_constants_fix),
        ("工具创建和序列化", test_tool_creation_and_serialization),
        ("Pydantic字段验证", test_pydantic_field_validation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 静态常量修复验证结果")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("\n🎉 静态常量修复完全成功！")
        logger.info("")
        logger.info("🚀 **关键修复内容:**")
        logger.info("   ✅ 移除了MINIMAX_PARAM_RANGES动态引用")
        logger.info("   ✅ 移除了MINIMAX_EMOTIONS动态引用")
        logger.info("   ✅ 使用静态数值: speed(0.5-2.0), vol(0.1-10.0), pitch(-12-12)")
        logger.info("   ✅ 使用静态emotion描述")
        logger.info("   ✅ Pydantic字段验证正常")
        logger.info("   ✅ JSON序列化完全稳定")
        logger.info("")
        logger.info("🎯 **预期效果:**")
        logger.info("   • 彻底解决 'Extra data: line 1 column 586' JSON解析错误")
        logger.info("   • LangGraph工具序列化过程完全稳定")
        logger.info("   • Audio Agent能正常识别multi_speaker_tts工具")
        logger.info("   • 不再有动态内容导致的不确定性")
        logger.info("")
        logger.info("🔧 **技术原理:**")
        logger.info("   • 问题根源: Pydantic字段验证中的动态常量引用")
        logger.info("   • 解决方案: 将所有动态引用改为静态数值")
        logger.info("   • 效果: 消除序列化过程中的不确定性")
        logger.info("")
        logger.info("🎭 **现在应该完全解决JSON解析错误了！**")
        logger.info("   建议立即重新测试Audio Agent的多人TTS功能")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
