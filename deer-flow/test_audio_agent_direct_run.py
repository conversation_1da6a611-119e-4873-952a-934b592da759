#!/usr/bin/env python3
"""
直接运行Audio Agent，检查multi_speaker_tts工具是否真的被加载
"""

import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_audio_agent_direct():
    """直接测试Audio Agent"""
    logger.info("🎵 直接运行Audio Agent测试...")
    
    try:
        # 直接导入Audio Agent
        from src.agents.agents import audio_creator_agent
        
        logger.info("✅ Audio Agent导入成功")
        logger.info(f"📋 Agent类型: {type(audio_creator_agent)}")
        
        # 检查Agent的结构
        if hasattr(audio_creator_agent, 'graph'):
            logger.info("📋 Agent使用LangGraph结构")
            graph = audio_creator_agent.graph
            
            # 检查图的节点
            if hasattr(graph, 'nodes'):
                nodes = list(graph.nodes.keys())
                logger.info(f"📋 Graph节点: {nodes}")
            
            # 尝试获取工具信息
            if hasattr(graph, 'get_state'):
                logger.info("📋 Graph支持状态管理")
        
        # 尝试模拟一个简单的调用来看工具列表
        logger.info("\n🔧 尝试获取工具列表...")
        
        # 创建一个测试输入
        test_input = {
            "messages": [{"role": "user", "content": "帮我生成一个两人相声剧本的音频"}]
        }
        
        # 尝试调用（但不等待完成，只是看初始化）
        try:
            # 这里我们不真正执行，只是看看初始化过程
            logger.info("📋 准备测试调用...")
            
            # 检查是否有工具相关的配置
            if hasattr(audio_creator_agent, 'config'):
                config = audio_creator_agent.config
                logger.info(f"📋 Agent配置: {type(config)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试调用失败: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Audio Agent直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_tools_loading():
    """测试audio_tools加载过程"""
    logger.info("📦 测试audio_tools加载过程...")
    
    try:
        # 模拟__init__.py中的加载过程
        from src.config.configuration import Configuration
        
        # 直接导入各个工具创建函数
        from src.tools.audio.music_generation import get_suno_music_generation_tool
        from src.tools.audio.text_to_speech import get_text_to_speech_tool, get_voice_clone_tool, get_text_to_voice_tool
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 逐个测试工具创建
        tools_info = [
            ("suno_music_generation", get_suno_music_generation_tool),
            ("text_to_speech_generator", get_text_to_speech_tool),
            ("voice_cloner", get_voice_clone_tool),
            ("design_voice_from_prompt", get_text_to_voice_tool),
            ("multi_speaker_tts", get_multi_speaker_tts_tool)
        ]
        
        created_tools = []
        
        for expected_name, tool_func in tools_info:
            try:
                logger.info(f"🔧 创建工具: {expected_name}")
                tool = tool_func(config=config)
                
                if tool is None:
                    logger.warning(f"⚠️ {expected_name}: 返回None（可能缺少配置）")
                else:
                    actual_name = getattr(tool, 'name', 'unknown')
                    logger.info(f"✅ {expected_name}: 创建成功，名称={actual_name}")
                    created_tools.append(tool)
                    
            except Exception as e:
                logger.error(f"❌ {expected_name}: 创建失败 - {e}")
        
        logger.info(f"\n📊 工具创建总结:")
        logger.info(f"   成功创建: {len(created_tools)} 个工具")
        
        for i, tool in enumerate(created_tools):
            tool_name = getattr(tool, 'name', f'Tool_{i}')
            logger.info(f"   {i+1}. {tool_name}")
        
        # 检查multi_speaker_tts是否在其中
        multi_tts_found = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in created_tools
        )
        
        if multi_tts_found:
            logger.info("✅ multi_speaker_tts工具已成功创建")
        else:
            logger.error("❌ multi_speaker_tts工具未创建或创建失败")
        
        return len(created_tools) > 0 and multi_tts_found
        
    except Exception as e:
        logger.error(f"❌ audio_tools加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_speaker_tts_detailed():
    """详细测试multi_speaker_tts工具"""
    logger.info("🔍 详细测试multi_speaker_tts工具...")
    
    try:
        from src.config.configuration import Configuration
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        
        config = Configuration.from_runnable_config()
        
        # 检查配置
        has_api_key = bool(getattr(config, 'minimax_api_key', None))
        has_group_id = bool(getattr(config, 'minimax_group_id', None))
        
        logger.info(f"📋 MINIMAX_API_KEY: {'有' if has_api_key else '无'}")
        logger.info(f"📋 MINIMAX_GROUP_ID: {'有' if has_group_id else '无'}")
        
        # 尝试创建工具
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.warning("⚠️ multi_speaker_tts工具返回None")
            if not has_api_key or not has_group_id:
                logger.info("📋 原因: 缺少MINIMAX配置（这是正常的）")
                return True
            else:
                logger.error("📋 原因: 未知（配置存在但工具仍返回None）")
                return False
        else:
            logger.info("✅ multi_speaker_tts工具创建成功")
            logger.info(f"📋 工具名称: {tool.name}")
            logger.info(f"📋 工具类型: {type(tool).__name__}")
            logger.info(f"📋 描述长度: {len(tool.description)} 字符")
            
            # 测试工具的基本属性
            attrs_to_check = ['name', 'description', 'func', 'coroutine', 'args_schema']
            for attr in attrs_to_check:
                if hasattr(tool, attr):
                    value = getattr(tool, attr)
                    logger.info(f"📋 {attr}: {type(value).__name__}")
                else:
                    logger.warning(f"⚠️ 缺少属性: {attr}")
            
            # 测试args_schema
            if hasattr(tool, 'args_schema'):
                try:
                    schema = tool.args_schema
                    logger.info(f"✅ args_schema: {schema.__name__}")
                    
                    # 测试创建一个实例
                    test_instance = schema(
                        dialogue_script=[{"speaker": "测试", "text": "测试文本"}]
                    )
                    logger.info("✅ args_schema实例创建成功")
                    
                    # 测试序列化
                    instance_dict = test_instance.dict()
                    json_str = json.dumps(instance_dict, ensure_ascii=False)
                    logger.info(f"✅ args_schema JSON序列化成功: {len(json_str)} 字符")
                    
                except Exception as e:
                    logger.error(f"❌ args_schema测试失败: {e}")
                    return False
            
            return True
        
    except Exception as e:
        logger.error(f"❌ multi_speaker_tts详细测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_agent_tools():
    """测试实际Agent中的工具"""
    logger.info("🎯 测试实际Agent中的工具...")
    
    try:
        # 尝试通过不同方式获取Agent的工具
        from src.agents.agents import audio_creator_agent
        
        # 方法1: 检查是否有tools属性
        if hasattr(audio_creator_agent, 'tools'):
            tools = audio_creator_agent.tools
            logger.info(f"📋 方法1 - Agent.tools: {len(tools) if tools else 0} 个工具")
            if tools:
                for i, tool in enumerate(tools):
                    name = getattr(tool, 'name', f'Tool_{i}')
                    logger.info(f"   {i+1}. {name}")
        
        # 方法2: 检查graph结构
        if hasattr(audio_creator_agent, 'graph'):
            graph = audio_creator_agent.graph
            logger.info("📋 方法2 - 检查Graph结构")
            
            # 检查节点
            if hasattr(graph, 'nodes'):
                nodes = graph.nodes
                logger.info(f"   Graph节点: {list(nodes.keys())}")
                
                # 查找工具相关的节点
                for node_name, node_data in nodes.items():
                    if 'tool' in node_name.lower() or hasattr(node_data, 'tools'):
                        logger.info(f"   发现工具节点: {node_name}")
        
        # 方法3: 尝试模拟调用看错误信息
        logger.info("📋 方法3 - 模拟调用测试")
        try:
            # 创建一个简单的测试状态
            test_state = {
                "messages": [{"role": "user", "content": "测试multi_speaker_tts工具"}]
            }
            
            # 这里我们不真正执行，只是看看会发生什么
            logger.info("📋 准备模拟调用...")
            
        except Exception as e:
            logger.info(f"📋 模拟调用信息: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 实际Agent工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🎯 开始Audio Agent直接运行测试...")
    logger.info("目标: 找出multi_speaker_tts工具没有被加载的真正原因")
    
    tests = [
        ("multi_speaker_tts详细测试", test_multi_speaker_tts_detailed),
        ("audio_tools加载过程", test_audio_tools_loading),
        ("实际Agent工具检查", test_actual_agent_tools),
        ("Audio Agent直接测试", test_audio_agent_direct)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 Audio Agent测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count < total_count:
        logger.warning("\n⚠️ 发现问题，需要进一步分析")
        logger.info("🔍 可能的原因:")
        logger.info("   1. multi_speaker_tts工具创建失败")
        logger.info("   2. 工具没有被正确添加到audio_tools")
        logger.info("   3. Audio Agent没有正确加载audio_tools")
        logger.info("   4. LangGraph配置问题")
        logger.info("   5. 循环导入或其他模块问题")
    else:
        logger.info("\n🎉 所有测试通过，但multi_speaker_tts仍可能有其他问题")

if __name__ == "__main__":
    main()
