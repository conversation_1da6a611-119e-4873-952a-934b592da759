#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from dataclasses import fields

# 手动设置环境变量
os.environ['MINIMAX_API_KEY'] = 'test_key'
os.environ['MINIMAX_GROUP_ID'] = 'test_group'

print('环境变量设置:')
print(f'MINIMAX_API_KEY: {os.environ.get("MINIMAX_API_KEY")}')
print(f'MINIMAX_GROUP_ID: {os.environ.get("MINIMAX_GROUP_ID")}')

from src.config.configuration import Configuration

print('\n配置字段检查:')
for field in fields(Configuration):
    if 'minimax' in field.name:
        env_name = field.name.upper()
        env_value = os.environ.get(env_name)
        print(f'{field.name} -> {env_name}: {env_value}')

# 调试from_runnable_config方法
print('\n调试from_runnable_config:')
values = {}
for field in fields(Configuration):
    if field.init:
        env_value = os.environ.get(field.name.upper())
        values[field.name] = env_value
        if 'minimax' in field.name:
            print(f'{field.name}: {env_value} (bool: {bool(env_value)})')

filtered_values = {k: v for k, v in values.items() if v}
print(f'\n过滤后的值数量: {len(filtered_values)}')
for k, v in filtered_values.items():
    if 'minimax' in k:
        print(f'{k}: {v}')

config = Configuration()
print('\n直接创建配置对象:')
print(f'minimax_api_key: {config.minimax_api_key}')
print(f'minimax_group_id: {config.minimax_group_id}')

config2 = Configuration.from_runnable_config()
print('\nfrom_runnable_config创建:')
print(f'minimax_api_key: {config2.minimax_api_key}')
print(f'minimax_group_id: {config2.minimax_group_id}')
