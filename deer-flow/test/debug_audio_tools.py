#!/usr/bin/env python3
"""
精准调试Audio工具问题
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def debug_audio_tools_step_by_step():
    """逐步调试音频工具"""
    
    print("🔍 Step 1: 检查multi_speaker_tts_tool初始化...")
    try:
        from src.tools import multi_speaker_tts_tool
        print(f"✅ multi_speaker_tts_tool导入成功: {multi_speaker_tts_tool}")
        print(f"📋 工具名称: {multi_speaker_tts_tool.name if multi_speaker_tts_tool else 'None'}")
        print(f"📋 工具类型: {type(multi_speaker_tts_tool)}")
    except Exception as e:
        print(f"❌ multi_speaker_tts_tool导入失败: {e}")
        return False
    
    print("\n🔍 Step 2: 检查audio_tools列表...")
    try:
        from src.tools import audio_tools
        print(f"✅ audio_tools导入成功")
        print(f"📋 audio_tools长度: {len(audio_tools)}")
        print(f"📋 audio_tools内容:")
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                name = getattr(tool, 'name', 'Unknown')
                print(f"   {i+1}. {name} ({type(tool)})")
            else:
                print(f"   {i+1}. None")
        
        # 检查multi_speaker_tts是否在列表中
        multi_tts_in_list = multi_speaker_tts_tool in audio_tools
        print(f"\n📋 multi_speaker_tts_tool在audio_tools中: {'✅ 是' if multi_tts_in_list else '❌ 否'}")
        
        if not multi_tts_in_list:
            print("🔍 检查工具名称匹配...")
            tool_names = [getattr(tool, 'name', '') for tool in audio_tools if tool is not None]
            has_name_match = 'multi_speaker_tts' in tool_names
            print(f"📋 按名称匹配: {'✅ 是' if has_name_match else '❌ 否'}")
            print(f"📋 实际工具名称: {tool_names}")
        
    except Exception as e:
        print(f"❌ audio_tools检查失败: {e}")
        return False
    
    print("\n🔍 Step 3: 检查Audio Agent创建...")
    try:
        from src.agents.agents import audio_creator_agent
        print(f"✅ audio_creator_agent导入成功")
        print(f"📋 Agent类型: {type(audio_creator_agent)}")
        
        # 尝试获取Agent的工具信息
        if hasattr(audio_creator_agent, 'tools'):
            agent_tools = audio_creator_agent.tools
            print(f"📋 Agent.tools: {agent_tools}")
        
        # 检查Agent的其他属性
        attrs = dir(audio_creator_agent)
        relevant_attrs = [attr for attr in attrs if 'tool' in attr.lower() or 'bind' in attr.lower()]
        print(f"📋 Agent相关属性: {relevant_attrs}")
        
    except Exception as e:
        print(f"❌ Audio Agent检查失败: {e}")
        return False
    
    print("\n🔍 Step 4: 检查create_agent函数...")
    try:
        from src.agents.factory import create_agent
        from src.tools import audio_tools
        
        print(f"✅ create_agent函数导入成功")
        print(f"📋 传入的audio_tools数量: {len(audio_tools)}")
        
        # 手动创建一个测试Agent
        print("\n🧪 手动创建测试Agent...")
        test_agent = create_agent(
            agent_name="test_audio_agent",
            agent_type="audio_creator",
            tools=audio_tools,
            prompt_template_name="audio_creator_prompt"
        )
        
        print(f"✅ 测试Agent创建成功: {type(test_agent)}")
        
        # 检查测试Agent的工具
        if hasattr(test_agent, 'tools'):
            test_tools = test_agent.tools
            print(f"📋 测试Agent工具数量: {len(test_tools) if test_tools else 0}")
            if test_tools:
                print("📋 测试Agent工具列表:")
                for i, tool in enumerate(test_tools):
                    name = getattr(tool, 'name', 'Unknown')
                    print(f"   {i+1}. {name}")
        
    except Exception as e:
        print(f"❌ create_agent测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🎯 开始精准调试Audio工具问题...")
    result = debug_audio_tools_step_by_step()
    print(f"\n📊 调试结果: {'✅ 成功' if result else '❌ 失败'}")
