#!/usr/bin/env python3
"""
测试优化后的多人TTS工具描述
"""

import os
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_tool_description():
    """测试工具描述的可读性和AI友好性"""
    logger.info("📝 测试优化后的工具描述...")
    
    try:
        from src.config.configuration import Configuration
        
        # 避免循环导入，直接导入工具函数
        import sys
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src/tools/audio')
        
        from multi_speaker_tts import get_multi_speaker_tts_tool
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        # 获取工具
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.warning("⚠️ 工具未启用（可能是配置缺失）")
            return False
        
        # 显示工具基本信息
        logger.info(f"✅ 工具名称: {tool.name}")
        logger.info(f"📋 工具描述长度: {len(tool.description)} 字符")
        
        # 分析描述结构
        description = tool.description
        sections = description.split('\n\n')
        
        logger.info(f"📊 描述结构分析:")
        logger.info(f"   • 总段落数: {len(sections)}")
        
        # 检查关键信息是否包含
        key_info_checks = {
            "用途说明": any("用途" in section or "播客" in section for section in sections),
            "核心优势": any("优势" in section or "并发" in section for section in sections),
            "声音组合": any("推荐声音" in section or "presenter" in section for section in sections),
            "使用示例": any("示例" in section or "dialogue_script" in section for section in sections),
            "高级控制": any("高级控制" in section or "voice_mapping" in section for section in sections),
            "输出内容": any("输出" in section or "URL" in section for section in sections)
        }
        
        logger.info(f"🔍 关键信息检查:")
        for info_type, found in key_info_checks.items():
            status = "✅" if found else "❌"
            logger.info(f"   {status} {info_type}: {'包含' if found else '缺失'}")
        
        # 检查参数schema
        if hasattr(tool, 'args_schema'):
            schema = tool.args_schema
            logger.info(f"📋 参数Schema: {schema.__name__}")
            
            # 检查必需参数
            required_fields = []
            optional_fields = []
            
            for field_name, field_info in schema.__fields__.items():
                if field_info.is_required():
                    required_fields.append(field_name)
                else:
                    optional_fields.append(field_name)
            
            logger.info(f"   • 必需参数: {', '.join(required_fields)}")
            logger.info(f"   • 可选参数: {', '.join(optional_fields)}")
        
        # 评估AI友好性
        ai_friendly_score = 0
        total_checks = len(key_info_checks)
        
        for found in key_info_checks.values():
            if found:
                ai_friendly_score += 1
        
        ai_friendly_percentage = (ai_friendly_score / total_checks) * 100
        
        logger.info(f"🤖 AI友好性评分: {ai_friendly_score}/{total_checks} ({ai_friendly_percentage:.1f}%)")
        
        if ai_friendly_percentage >= 90:
            logger.info("🎉 优秀！工具描述非常适合AI理解和调用")
        elif ai_friendly_percentage >= 70:
            logger.info("👍 良好！工具描述基本适合AI使用")
        else:
            logger.warning("⚠️ 需要改进！工具描述可能不够清晰")
        
        return ai_friendly_percentage >= 70
        
    except Exception as e:
        logger.error(f"❌ 工具描述测试失败: {e}")
        return False

def test_example_usage_scenarios():
    """测试不同使用场景的示例"""
    logger.info("🎭 测试使用场景示例...")
    
    try:
        # 场景1: 简单播客对话
        simple_podcast = {
            "dialogue_script": [
                {"speaker": "主持人", "text": "欢迎大家收听今天的AI技术分享节目！"},
                {"speaker": "嘉宾", "text": "很高兴来到这里和大家分享AI的最新发展。"},
                {"speaker": "主持人", "text": "那我们就开始今天的精彩对话吧！"}
            ]
        }
        
        # 场景2: 专业有声书
        audiobook_scenario = {
            "dialogue_script": [
                {"speaker": "旁白", "text": "在一个遥远的星球上，住着一群智慧的机器人。"},
                {"speaker": "机器人A", "text": "今天的任务是什么？", "emotion": "curious"},
                {"speaker": "机器人B", "text": "我们要去探索新的能源。", "emotion": "determined"}
            ],
            "voice_mapping": {
                "旁白": "audiobook_male_1",
                "机器人A": "male-qn-qingse", 
                "机器人B": "female-yujie"
            }
        }
        
        # 场景3: 教学对话
        educational_dialogue = {
            "dialogue_script": [
                {"speaker": "老师", "text": "今天我们来学习人工智能的基本概念。", "speed": 0.9},
                {"speaker": "学生", "text": "老师，什么是机器学习？", "emotion": "curious"},
                {"speaker": "老师", "text": "机器学习是让计算机从数据中学习的技术。", "speed": 0.8, "vol": 1.2}
            ],
            "voice_mapping": {
                "老师": "presenter_female",
                "学生": "female-shaonv"
            },
            "generate_timestamps": True
        }
        
        # 场景4: 角色扮演故事
        roleplay_story = {
            "dialogue_script": [
                {"speaker": "王子", "text": "美丽的公主，你愿意和我一起去冒险吗？", "emotion": "romantic"},
                {"speaker": "公主", "text": "当然愿意，勇敢的王子！", "emotion": "happy", "pitch": 2},
                {"speaker": "旁白", "text": "于是他们踏上了寻找宝藏的旅程。", "speed": 1.1}
            ],
            "voice_mapping": {
                "王子": "junlang_nanyou",
                "公主": "tianxin_xiaoling",
                "旁白": "audiobook_male_2"
            }
        }
        
        scenarios = [
            ("简单播客对话", simple_podcast),
            ("专业有声书", audiobook_scenario),
            ("教学对话", educational_dialogue),
            ("角色扮演故事", roleplay_story)
        ]
        
        logger.info("📋 使用场景示例验证:")
        
        for scenario_name, scenario_data in scenarios:
            logger.info(f"\n🎬 场景: {scenario_name}")
            
            # 验证必需参数
            has_dialogue_script = "dialogue_script" in scenario_data
            dialogue_count = len(scenario_data.get("dialogue_script", []))
            
            # 验证对话项结构
            valid_dialogue_items = True
            if has_dialogue_script:
                for item in scenario_data["dialogue_script"]:
                    if not ("speaker" in item and "text" in item):
                        valid_dialogue_items = False
                        break
            
            # 验证可选参数
            has_voice_mapping = "voice_mapping" in scenario_data
            has_advanced_params = any(
                param in item for item in scenario_data.get("dialogue_script", [])
                for param in ["emotion", "speed", "vol", "pitch"]
            )
            
            logger.info(f"   • 对话数量: {dialogue_count}")
            logger.info(f"   • 结构有效: {'✅' if valid_dialogue_items else '❌'}")
            logger.info(f"   • 声音映射: {'✅' if has_voice_mapping else '⚪'}")
            logger.info(f"   • 高级参数: {'✅' if has_advanced_params else '⚪'}")
            
            # 计算复杂度
            complexity = "简单"
            if has_voice_mapping and has_advanced_params:
                complexity = "专业"
            elif has_voice_mapping or has_advanced_params:
                complexity = "中级"
            
            logger.info(f"   • 使用复杂度: {complexity}")
        
        logger.info("\n✅ 所有使用场景示例都结构合理，适合AI理解和生成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 使用场景测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始优化后工具描述测试...")
    
    # 测试步骤
    tests = [
        ("工具描述分析", test_tool_description),
        ("使用场景示例", test_example_usage_scenarios)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 工具描述优化测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 工具描述优化成功！")
        logger.info("")
        logger.info("🚀 **优化效果:**")
        logger.info("   📝 结构清晰 - 按用途、优势、示例、控制、输出分段")
        logger.info("   🎯 场景明确 - 明确列出播客、有声书、教学等应用场景")
        logger.info("   💡 示例丰富 - 提供简单到复杂的多种使用示例")
        logger.info("   🔧 参数详细 - 每个参数都有清晰的说明和范围")
        logger.info("   🤖 AI友好 - 描述结构化，便于AI理解和调用")
        logger.info("")
        logger.info("🎯 现在AI可以更容易理解和使用多人TTS工具了！")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步优化")

if __name__ == "__main__":
    main()
