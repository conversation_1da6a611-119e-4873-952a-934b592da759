#!/usr/bin/env python3
"""
测试音频工具集成情况
"""

import os
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_audio_tools_import():
    """测试音频工具导入"""
    logger.info("📦 测试音频工具导入...")
    
    try:
        from src.tools import audio_tools
        
        logger.info(f"✅ audio_tools导入成功")
        logger.info(f"📋 音频工具数量: {len(audio_tools)}")
        
        # 列出所有音频工具
        logger.info("🔧 可用音频工具:")
        tool_names = []
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                tool_name = getattr(tool, 'name', f'Tool_{i}')
                tool_names.append(tool_name)
                logger.info(f"   {i+1}. {tool_name}")
            else:
                logger.warning(f"   {i+1}. None (工具未初始化)")
        
        # 检查多人TTS工具是否存在
        has_multi_speaker_tts = 'multi_speaker_tts' in tool_names
        
        if has_multi_speaker_tts:
            logger.info("✅ multi_speaker_tts工具已正确集成")
        else:
            logger.error("❌ multi_speaker_tts工具未找到")
            logger.info(f"📋 实际工具名称: {tool_names}")
        
        return has_multi_speaker_tts and len(audio_tools) > 0
        
    except Exception as e:
        logger.error(f"❌ 音频工具导入失败: {e}")
        return False

def test_multi_speaker_tts_tool_details():
    """测试多人TTS工具详细信息"""
    logger.info("🎭 测试多人TTS工具详细信息...")
    
    try:
        from src.tools import multi_speaker_tts_tool
        
        if multi_speaker_tts_tool is None:
            logger.error("❌ multi_speaker_tts_tool为None")
            return False
        
        logger.info(f"✅ 工具实例创建成功")
        logger.info(f"📋 工具名称: {multi_speaker_tts_tool.name}")
        logger.info(f"📋 工具描述长度: {len(multi_speaker_tts_tool.description)} 字符")
        
        # 检查工具描述中的关键信息
        description = multi_speaker_tts_tool.description
        key_checks = {
            "包含完整音色库": "完整音色库" in description,
            "包含46个音色": "46个" in description,
            "包含推荐组合": "推荐声音组合" in description,
            "包含使用场景": "播客制作" in description,
            "包含参数控制": "语速" in description and "音量" in description
        }
        
        logger.info("🔍 工具描述内容检查:")
        all_passed = True
        for check_name, passed in key_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
            if not passed:
                all_passed = False
        
        # 显示工具描述的前几行
        desc_lines = description.split('\n')[:5]
        logger.info("📝 工具描述预览:")
        for line in desc_lines:
            if line.strip():
                logger.info(f"   {line}")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 多人TTS工具详细信息测试失败: {e}")
        return False

def test_audio_tools_in_agents():
    """测试音频工具在agents中的使用"""
    logger.info("🤖 测试音频工具在agents中的使用...")
    
    try:
        from src.agents.agents import audio_creator_agent
        from src.tools import audio_tools
        
        logger.info(f"✅ audio_creator_agent导入成功")
        
        # 检查Agent类型
        agent_type = type(audio_creator_agent).__name__
        logger.info(f"📋 Agent类型: {agent_type}")
        
        # 尝试获取Agent的配置信息
        if hasattr(audio_creator_agent, 'config'):
            config = audio_creator_agent.config
            logger.info(f"📋 Agent配置: {type(config)}")
        
        # 检查传入的工具列表
        logger.info(f"📋 传入audio_creator_agent的工具数量: {len(audio_tools)}")
        
        # 验证工具列表中包含多人TTS
        multi_tts_in_tools = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in audio_tools if tool is not None
        )
        
        if multi_tts_in_tools:
            logger.info("✅ multi_speaker_tts工具已传入audio_creator_agent")
        else:
            logger.error("❌ multi_speaker_tts工具未传入audio_creator_agent")
        
        return multi_tts_in_tools
        
    except Exception as e:
        logger.error(f"❌ 音频工具在agents中的使用测试失败: {e}")
        return False

def test_configuration_integration():
    """测试配置集成"""
    logger.info("⚙️ 测试配置集成...")
    
    try:
        from src.config.configuration import Configuration, MINIMAX_SYSTEM_VOICES
        
        config = Configuration.from_runnable_config()
        
        logger.info(f"✅ 配置创建成功")
        logger.info(f"📋 Minimax API Key: {'已设置' if config.minimax_api_key else '未设置'}")
        logger.info(f"📋 Minimax Group ID: {'已设置' if config.minimax_group_id else '未设置'}")
        logger.info(f"📋 系统音色数量: {len(MINIMAX_SYSTEM_VOICES)}")
        
        # 测试配置方法
        male_voices = config.get_minimax_voices_by_gender("male")
        female_voices = config.get_minimax_voices_by_gender("female")
        
        logger.info(f"📊 男声音色: {len(male_voices)}个")
        logger.info(f"📊 女声音色: {len(female_voices)}个")
        
        # 测试推荐组合
        professional_pair = config.get_recommended_voice_pair("professional")
        logger.info(f"💡 专业组合: {professional_pair['male']} + {professional_pair['female']}")
        
        return config.minimax_api_key and config.minimax_group_id
        
    except Exception as e:
        logger.error(f"❌ 配置集成测试失败: {e}")
        return False

def test_prompt_template_update():
    """测试提示词模板更新"""
    logger.info("📝 测试提示词模板更新...")
    
    try:
        from src.prompts.template import get_prompt_template
        
        # 获取audio_creator_prompt模板
        prompt_template = get_prompt_template("audio_creator_prompt")
        
        logger.info(f"✅ 提示词模板获取成功")
        logger.info(f"📋 模板长度: {len(prompt_template)} 字符")
        
        # 检查关键内容
        key_checks = {
            "包含多人TTS工具": "multi_speaker_tts" in prompt_template,
            "包含工具选择指南": "工具选择指南" in prompt_template,
            "包含播客示例": "播客对话" in prompt_template,
            "包含音色库信息": "46个系统音色" in prompt_template,
            "包含推荐组合": "推荐组合" in prompt_template
        }
        
        logger.info("🔍 提示词模板内容检查:")
        all_passed = True
        for check_name, passed in key_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'包含' if passed else '缺失'}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 提示词模板更新测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始音频工具集成全面测试...")
    
    # 测试步骤
    tests = [
        ("音频工具导入", test_audio_tools_import),
        ("多人TTS工具详情", test_multi_speaker_tts_tool_details),
        ("音频工具在agents中的使用", test_audio_tools_in_agents),
        ("配置集成", test_configuration_integration),
        ("提示词模板更新", test_prompt_template_update)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 音频工具集成全面测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 音频工具集成完全成功！")
        logger.info("")
        logger.info("🚀 **集成成功总结:**")
        logger.info("   🎭 多人TTS工具已正确集成到audio_tools列表")
        logger.info("   🤖 Audio Agent已配备完整的音频工具集")
        logger.info("   📝 提示词已更新，包含详细的工具使用指南")
        logger.info("   ⚙️ 配置系统完整，支持46个音色和推荐组合")
        logger.info("   🔧 工具描述完善，AI可以充分理解功能")
        logger.info("")
        logger.info("🎯 现在用户可以直接要求Audio Agent：")
        logger.info("   • '为我制作一个播客对话'")
        logger.info("   • '创建有声书角色对话'")
        logger.info("   • '生成教学问答音频'")
        logger.info("   • '制作广告配音对话'")
        logger.info("")
        logger.info("Audio Agent会自动识别场景并选择合适的工具！")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
