#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的多模态理解工具测试

直接测试理解工具，避免循环导入问题。
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_understanding_tools():
    """测试理解工具"""
    print("🦌 DeerFlow 多模态理解工具测试")
    print("=" * 50)
    
    try:
        # 直接导入理解工具模块
        from src.config.configuration import Configuration
        from src.tools.understanding.video_understanding import (
            get_multimodal_understanding_tool,
            get_video_understanding_tool
        )
        from src.tools.understanding.image_understanding import get_image_understanding_tool
        
        print("✅ 成功导入理解工具模块")
        
        # 创建配置
        config = Configuration.from_runnable_config()
        print("✅ 成功创建配置")
        
        # 创建工具实例
        multimodal_tool = get_multimodal_understanding_tool(config)
        image_tool = get_image_understanding_tool(config)
        video_tool = get_video_understanding_tool(config)
        
        print(f"✅ 多模态理解工具: {multimodal_tool.name}")
        print(f"✅ 图片理解工具: {image_tool.name}")
        print(f"✅ 视频理解工具: {video_tool.name}")
        
        # 测试图片分析
        print("\n🖼️  测试图片分析...")
        test_image = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Cat03.jpg/1200px-Cat03.jpg"
        
        result = image_tool.invoke({
            "image_url": test_image,
            "question": "这张图片中有什么动物？",
            "analysis_type": "objects",
            "detail_level": "brief"
        })
        
        print("✅ 图片分析成功")
        print(f"📄 结果: {result}")
        
        # 测试多模态工具
        print("\n🎯 测试多模态工具...")
        result2 = multimodal_tool.invoke({
            "media_url": test_image,
            "question": "请简要描述这张图片",
            "analysis_focus": "general",
            "max_tokens": 500
        })
        
        print("✅ 多模态分析成功")
        print(f"📄 结果: {result2}")
        
        print("\n🎉 所有测试通过！")
        print("✨ 多模态理解工具已成功开发并可以正常使用")
        print(f"🤖 使用模型: gemini-2.5-pro-preview-06-05")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = test_understanding_tools()
    
    if success:
        print("\n" + "=" * 50)
        print("🎯 工具使用说明:")
        print("1. 图片分析: 使用 get_image_understanding_tool(config)")
        print("2. 视频分析: 使用 get_video_understanding_tool(config)")
        print("3. 多模态分析: 使用 get_multimodal_understanding_tool(config)")
        print("4. 所有工具都使用 gemini-2.5-pro-preview-06-05 模型")
        print("5. 支持的分析类型: general, objects, scene, style, text")
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit(main())
