#!/usr/bin/env python3
"""
测试完整的Minimax声音系统和参数配置
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_voice_configuration():
    """测试声音配置系统"""
    logger.info("🎭 测试Minimax声音配置系统...")
    
    try:
        from src.config.minimax_voices import (
            MINIMAX_SYSTEM_VOICES, 
            MINIMAX_PARAM_RANGES, 
            MINIMAX_EMOTIONS,
            get_voices_by_gender,
            get_voices_by_category,
            RECOMMENDED_VOICE_PAIRS,
            MinimaxVoiceCategory
        )
        
        # 统计声音数量
        total_voices = len(MINIMAX_SYSTEM_VOICES)
        male_voices = len(get_voices_by_gender("male"))
        female_voices = len(get_voices_by_gender("female"))
        
        logger.info(f"✅ 声音配置加载成功")
        logger.info(f"📊 声音统计:")
        logger.info(f"   • 总计: {total_voices} 个系统音色")
        logger.info(f"   • 男声: {male_voices} 个")
        logger.info(f"   • 女声: {female_voices} 个")
        logger.info(f"   • 其他: {total_voices - male_voices - female_voices} 个")
        
        # 显示各分类的声音数量
        logger.info(f"📋 分类统计:")
        for category in MinimaxVoiceCategory:
            voices = get_voices_by_category(category)
            logger.info(f"   • {category.value}: {len(voices)} 个")
        
        # 显示参数范围
        logger.info(f"🎚️ 参数范围:")
        for param, config in MINIMAX_PARAM_RANGES.items():
            logger.info(f"   • {param}: [{config['min']}, {config['max']}], 默认: {config['default']}")
        
        # 显示支持的情感
        logger.info(f"😊 支持情感: {', '.join(MINIMAX_EMOTIONS)}")
        
        # 显示推荐组合
        logger.info(f"💡 推荐声音组合:")
        for name, pair in RECOMMENDED_VOICE_PAIRS.items():
            logger.info(f"   • {name}: {pair['male']} + {pair['female']} - {pair['description']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 声音配置测试失败: {e}")
        return False

def test_parameter_validation():
    """测试参数验证"""
    logger.info("🔍 测试参数验证...")
    
    try:
        from src.config.minimax_voices import MINIMAX_PARAM_RANGES, MINIMAX_EMOTIONS
        
        # 测试有效参数
        test_cases = [
            {"speed": 1.0, "vol": 1.0, "pitch": 0, "emotion": "calm", "expected": True},
            {"speed": 0.5, "vol": 10.0, "pitch": -12, "emotion": "happy", "expected": True},
            {"speed": 2.0, "vol": 0.1, "pitch": 12, "emotion": "sad", "expected": True},
            {"speed": 0.4, "vol": 1.0, "pitch": 0, "emotion": "calm", "expected": False},  # speed太小
            {"speed": 1.0, "vol": 11.0, "pitch": 0, "emotion": "calm", "expected": False},  # vol太大
            {"speed": 1.0, "vol": 1.0, "pitch": 13, "emotion": "calm", "expected": False},  # pitch太大
            {"speed": 1.0, "vol": 1.0, "pitch": 0, "emotion": "invalid", "expected": False},  # 无效情感
        ]
        
        success_count = 0
        for i, case in enumerate(test_cases):
            # 验证speed
            speed_valid = (MINIMAX_PARAM_RANGES["speed"]["min"] <= case["speed"] <= 
                          MINIMAX_PARAM_RANGES["speed"]["max"])
            
            # 验证vol
            vol_valid = (0 < case["vol"] <= MINIMAX_PARAM_RANGES["vol"]["max"])
            
            # 验证pitch
            pitch_valid = (MINIMAX_PARAM_RANGES["pitch"]["min"] <= case["pitch"] <= 
                          MINIMAX_PARAM_RANGES["pitch"]["max"])
            
            # 验证emotion
            emotion_valid = case["emotion"] in MINIMAX_EMOTIONS
            
            # 总体验证
            is_valid = speed_valid and vol_valid and pitch_valid and emotion_valid
            expected = case["expected"]
            
            if is_valid == expected:
                success_count += 1
                status = "✅"
            else:
                status = "❌"
            
            logger.info(f"   {status} 测试{i+1}: speed={case['speed']}, vol={case['vol']}, "
                       f"pitch={case['pitch']}, emotion={case['emotion']} -> "
                       f"{'有效' if is_valid else '无效'}")
        
        logger.info(f"📊 参数验证结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        logger.error(f"❌ 参数验证测试失败: {e}")
        return False

def test_voice_selection_logic():
    """测试声音选择逻辑"""
    logger.info("🎵 测试声音选择逻辑...")
    
    try:
        from src.config.minimax_voices import MINIMAX_SYSTEM_VOICES, get_voices_by_gender
        from src.config.configuration import Configuration
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        # 测试自动分配逻辑
        speakers = ["主持人", "嘉宾A", "嘉宾B", "观众", "专家"]
        
        logger.info("📋 自动声音分配测试:")
        for idx, speaker in enumerate(speakers):
            if idx % 2 == 0:
                voice_id = config.minimax_default_female_voice
                gender = "女声"
            else:
                voice_id = config.minimax_default_male_voice
                gender = "男声"
            
            # 验证声音ID是否存在
            voice_exists = voice_id in MINIMAX_SYSTEM_VOICES
            voice_info = MINIMAX_SYSTEM_VOICES.get(voice_id, {})
            voice_name = voice_info.get("name", "未知")
            
            status = "✅" if voice_exists else "❌"
            logger.info(f"   {status} {speaker} (索引{idx}) -> {voice_id} ({voice_name}, {gender})")
        
        # 测试推荐组合
        logger.info("💡 推荐组合测试:")
        from src.config.minimax_voices import RECOMMENDED_VOICE_PAIRS
        
        for combo_name, combo in RECOMMENDED_VOICE_PAIRS.items():
            male_exists = combo["male"] in MINIMAX_SYSTEM_VOICES
            female_exists = combo["female"] in MINIMAX_SYSTEM_VOICES
            
            status = "✅" if (male_exists and female_exists) else "❌"
            logger.info(f"   {status} {combo_name}: {combo['male']} + {combo['female']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 声音选择逻辑测试失败: {e}")
        return False

def test_dialogue_item_creation():
    """测试对话项创建和验证"""
    logger.info("📝 测试对话项创建...")
    
    try:
        # 模拟DialogueItem类
        from pydantic import BaseModel, Field, ValidationError
        from src.config.minimax_voices import MINIMAX_PARAM_RANGES, MINIMAX_EMOTIONS
        
        class TestDialogueItem(BaseModel):
            speaker: str = Field(..., description="说话人名称")
            text: str = Field(..., description="对话文本内容", max_length=10000)
            emotion: str = Field(default="calm", description="情感类型")
            speed: float = Field(
                default=1.0, 
                ge=MINIMAX_PARAM_RANGES["speed"]["min"], 
                le=MINIMAX_PARAM_RANGES["speed"]["max"]
            )
            vol: float = Field(
                default=1.0,
                gt=0.0,
                le=MINIMAX_PARAM_RANGES["vol"]["max"]
            )
            pitch: int = Field(
                default=0,
                ge=MINIMAX_PARAM_RANGES["pitch"]["min"],
                le=MINIMAX_PARAM_RANGES["pitch"]["max"]
            )
        
        # 测试有效的对话项
        valid_items = [
            {
                "speaker": "主持人",
                "text": "欢迎大家收听今天的节目！",
                "emotion": "happy",
                "speed": 1.2,
                "vol": 1.5,
                "pitch": 2
            },
            {
                "speaker": "嘉宾",
                "text": "很高兴来到这里分享。",
                "emotion": "calm"
                # 其他参数使用默认值
            }
        ]
        
        # 测试无效的对话项
        invalid_items = [
            {
                "speaker": "测试",
                "text": "测试",
                "speed": 3.0  # 超出范围
            },
            {
                "speaker": "测试",
                "text": "测试",
                "vol": 15.0  # 超出范围
            },
            {
                "speaker": "测试", 
                "text": "测试",
                "pitch": 20  # 超出范围
            }
        ]
        
        # 测试有效项
        logger.info("✅ 有效对话项测试:")
        for i, item in enumerate(valid_items):
            try:
                dialogue = TestDialogueItem(**item)
                logger.info(f"   ✅ 项目{i+1}: {dialogue.speaker} - {dialogue.emotion} "
                           f"(速度:{dialogue.speed}, 音量:{dialogue.vol}, 语调:{dialogue.pitch})")
            except ValidationError as e:
                logger.error(f"   ❌ 项目{i+1}验证失败: {e}")
        
        # 测试无效项
        logger.info("❌ 无效对话项测试:")
        for i, item in enumerate(invalid_items):
            try:
                dialogue = TestDialogueItem(**item)
                logger.error(f"   ❌ 项目{i+1}应该失败但通过了")
            except ValidationError as e:
                logger.info(f"   ✅ 项目{i+1}正确拒绝: {str(e).split('\\n')[0]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 对话项测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始完整声音系统测试...")
    
    # 测试步骤
    tests = [
        ("声音配置系统", test_voice_configuration),
        ("参数验证", test_parameter_validation),
        ("声音选择逻辑", test_voice_selection_logic),
        ("对话项创建", test_dialogue_item_creation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 完整声音系统测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 完整声音系统测试全部通过！")
        logger.info("")
        logger.info("🚀 **系统特性总结:**")
        logger.info("   🎭 40+ 系统音色 - 涵盖主持人、有声书、角色扮演等")
        logger.info("   🎚️ 精细参数控制 - 语速、音量、语调、情感全面支持")
        logger.info("   🔍 严格参数验证 - 确保所有参数在有效范围内")
        logger.info("   💡 智能推荐组合 - 预设专业、青年、有声书等组合")
        logger.info("   🔧 统一配置管理 - 集中管理所有声音和参数配置")
        logger.info("")
        logger.info("🎯 您的多人TTS工具现在拥有完整的专业级声音系统！")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
