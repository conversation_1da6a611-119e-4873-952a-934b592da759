#!/usr/bin/env python3
"""
测试Audio Agent工具集成
"""

import os
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_audio_tools_import():
    """测试音频工具导入"""
    logger.info("📦 测试音频工具导入...")
    
    try:
        from src.tools import audio_tools, multi_speaker_tts_tool
        
        logger.info(f"✅ audio_tools导入成功")
        logger.info(f"📋 音频工具数量: {len(audio_tools)}")
        
        # 检查multi_speaker_tts_tool是否正确初始化
        if multi_speaker_tts_tool is None:
            logger.error("❌ multi_speaker_tts_tool为None")
            return False
        
        logger.info(f"✅ multi_speaker_tts_tool初始化成功")
        logger.info(f"📋 工具名称: {multi_speaker_tts_tool.name}")
        
        # 检查是否在audio_tools列表中
        multi_tts_in_list = multi_speaker_tts_tool in audio_tools
        logger.info(f"📋 multi_speaker_tts_tool在audio_tools中: {'是' if multi_tts_in_list else '否'}")
        
        # 列出所有音频工具
        logger.info("🔧 audio_tools列表中的工具:")
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                tool_name = getattr(tool, 'name', f'Tool_{i}')
                logger.info(f"   {i+1}. {tool_name}")
            else:
                logger.warning(f"   {i+1}. None (工具未初始化)")
        
        return multi_tts_in_list and len(audio_tools) > 0
        
    except Exception as e:
        logger.error(f"❌ 音频工具导入测试失败: {e}")
        return False

def test_audio_agent_tools():
    """测试Audio Agent的工具配置"""
    logger.info("🤖 测试Audio Agent的工具配置...")
    
    try:
        from src.agents.agents import audio_creator_agent, audio_agent
        from src.tools import audio_tools
        
        logger.info(f"✅ Audio Agent导入成功")
        
        # 检查audio_creator_agent和audio_agent是否是同一个对象
        is_same_agent = audio_creator_agent is audio_agent
        logger.info(f"📋 audio_creator_agent和audio_agent是同一对象: {'是' if is_same_agent else '否'}")
        
        # 尝试获取Agent的工具信息
        agent_type = type(audio_creator_agent).__name__
        logger.info(f"📋 Agent类型: {agent_type}")
        
        # 检查Agent是否有tools属性或相关配置
        has_tools_attr = hasattr(audio_creator_agent, 'tools')
        logger.info(f"📋 Agent有tools属性: {'是' if has_tools_attr else '否'}")
        
        if has_tools_attr:
            agent_tools = audio_creator_agent.tools
            logger.info(f"📋 Agent工具数量: {len(agent_tools) if agent_tools else 0}")
            
            if agent_tools:
                logger.info("🔧 Agent中的工具:")
                for i, tool in enumerate(agent_tools):
                    tool_name = getattr(tool, 'name', f'Tool_{i}')
                    logger.info(f"   {i+1}. {tool_name}")
                
                # 检查multi_speaker_tts是否在Agent的工具中
                multi_tts_in_agent = any(
                    getattr(tool, 'name', '') == 'multi_speaker_tts' 
                    for tool in agent_tools
                )
                logger.info(f"📋 multi_speaker_tts在Agent工具中: {'是' if multi_tts_in_agent else '否'}")
                
                return multi_tts_in_agent
        
        # 如果没有直接的tools属性，检查其他可能的配置方式
        logger.info("🔍 检查其他可能的工具配置方式...")
        
        # 检查是否有config或其他属性
        if hasattr(audio_creator_agent, 'config'):
            logger.info("📋 Agent有config属性")
        
        if hasattr(audio_creator_agent, 'runnable'):
            logger.info("📋 Agent有runnable属性")
            runnable = audio_creator_agent.runnable
            if hasattr(runnable, 'tools'):
                logger.info(f"📋 Runnable有tools属性，工具数量: {len(runnable.tools)}")
        
        # 传入的audio_tools应该包含multi_speaker_tts_tool
        multi_tts_in_tools = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in audio_tools if tool is not None
        )
        
        logger.info(f"📋 传入Agent的audio_tools包含multi_speaker_tts: {'是' if multi_tts_in_tools else '否'}")
        
        return multi_tts_in_tools
        
    except Exception as e:
        logger.error(f"❌ Audio Agent工具配置测试失败: {e}")
        return False

def test_audio_agent_creation():
    """测试Audio Agent创建过程"""
    logger.info("🏗️ 测试Audio Agent创建过程...")
    
    try:
        from src.agents.factory import create_agent
        from src.tools import audio_tools
        
        logger.info(f"✅ 导入成功")
        logger.info(f"📋 传入create_agent的audio_tools数量: {len(audio_tools)}")
        
        # 检查传入的工具列表
        logger.info("🔧 传入create_agent的工具:")
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                tool_name = getattr(tool, 'name', f'Tool_{i}')
                logger.info(f"   {i+1}. {tool_name}")
            else:
                logger.warning(f"   {i+1}. None")
        
        # 检查multi_speaker_tts是否在传入的工具中
        multi_tts_in_input = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in audio_tools if tool is not None
        )
        
        logger.info(f"📋 multi_speaker_tts在传入工具中: {'是' if multi_tts_in_input else '否'}")
        
        return multi_tts_in_input
        
    except Exception as e:
        logger.error(f"❌ Audio Agent创建过程测试失败: {e}")
        return False

def test_multi_speaker_tts_tool_details():
    """测试多人TTS工具详细信息"""
    logger.info("🎭 测试多人TTS工具详细信息...")
    
    try:
        from src.tools import multi_speaker_tts_tool
        
        if multi_speaker_tts_tool is None:
            logger.error("❌ multi_speaker_tts_tool为None")
            return False
        
        logger.info(f"✅ 工具实例创建成功")
        logger.info(f"📋 工具名称: {multi_speaker_tts_tool.name}")
        logger.info(f"📋 工具类型: {type(multi_speaker_tts_tool)}")
        
        # 检查工具是否可调用
        is_callable = callable(multi_speaker_tts_tool)
        logger.info(f"📋 工具可调用: {'是' if is_callable else '否'}")
        
        # 检查工具描述
        if hasattr(multi_speaker_tts_tool, 'description'):
            desc_length = len(multi_speaker_tts_tool.description)
            logger.info(f"📋 工具描述长度: {desc_length} 字符")
            
            # 检查描述中的关键词
            description = multi_speaker_tts_tool.description
            key_features = {
                "包含多人对话": "多人" in description or "对话" in description,
                "包含音色库": "音色" in description or "声音" in description,
                "包含相声功能": "相声" in description or "逗哏" in description or "捧哏" in description,
                "包含播客功能": "播客" in description,
                "包含推荐组合": "推荐" in description or "组合" in description
            }
            
            logger.info("🔍 工具描述特性检查:")
            for feature, has_feature in key_features.items():
                status = "✅" if has_feature else "❌"
                logger.info(f"   {status} {feature}: {'包含' if has_feature else '缺失'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多人TTS工具详细信息测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始Audio Agent工具集成测试...")
    
    # 测试步骤
    tests = [
        ("音频工具导入", test_audio_tools_import),
        ("Audio Agent工具配置", test_audio_agent_tools),
        ("Audio Agent创建过程", test_audio_agent_creation),
        ("多人TTS工具详细信息", test_multi_speaker_tts_tool_details)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 Audio Agent工具集成测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Audio Agent工具集成完全正常！")
        logger.info("")
        logger.info("🚀 **集成状态:**")
        logger.info("   ✅ multi_speaker_tts_tool正确初始化")
        logger.info("   ✅ 工具已添加到audio_tools列表")
        logger.info("   ✅ audio_tools正确传递给Audio Agent")
        logger.info("   ✅ 工具描述完整，包含所有必要信息")
        logger.info("")
        logger.info("🎭 Audio Agent应该能够使用多人TTS工具处理相声剧本任务！")
    else:
        logger.warning("⚠️ 部分测试失败，多人TTS工具可能未正确集成")

if __name__ == "__main__":
    main()
