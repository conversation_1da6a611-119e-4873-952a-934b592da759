#!/usr/bin/env python3
"""
测试多人TTS工具集成到DeerFlow系统
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_tool_import():
    """测试工具导入"""
    logger.info("🧪 测试多人TTS工具导入...")
    
    try:
        from src.tools import multi_speaker_tts_tool
        from src.tools import audio_tools
        
        if multi_speaker_tts_tool is None:
            logger.warning("⚠️ 多人TTS工具未启用（可能是配置缺失）")
            return False
        
        logger.info(f"✅ 工具导入成功: {multi_speaker_tts_tool.name}")
        logger.info(f"📋 工具描述: {multi_speaker_tts_tool.description[:100]}...")
        logger.info(f"🔧 音频工具总数: {len(audio_tools)}")
        
        # 检查工具是否在audio_tools列表中
        tool_names = [tool.name for tool in audio_tools if tool is not None]
        logger.info(f"🎵 可用音频工具: {tool_names}")
        
        if multi_speaker_tts_tool.name in tool_names:
            logger.info("✅ 多人TTS工具已成功集成到音频工具列表")
            return True
        else:
            logger.error("❌ 多人TTS工具未在音频工具列表中找到")
            return False
            
    except ImportError as e:
        logger.error(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

async def test_tool_execution():
    """测试工具执行"""
    logger.info("🚀 测试多人TTS工具执行...")
    
    try:
        from src.tools import multi_speaker_tts_tool
        
        if multi_speaker_tts_tool is None:
            logger.warning("⚠️ 工具未启用，跳过执行测试")
            return False
        
        # 准备测试数据
        test_input = {
            "dialogue_script": [
                {
                    "speaker": "主持人",
                    "text": "欢迎大家收听今天的AI技术分享节目！",
                    "emotion": "happy",
                    "speed": 1.0
                },
                {
                    "speaker": "嘉宾",
                    "text": "很高兴来到这里和大家分享AI的最新发展。",
                    "emotion": "neutral",
                    "speed": 0.9
                },
                {
                    "speaker": "主持人", 
                    "text": "那我们就开始今天的精彩对话吧！",
                    "emotion": "happy",
                    "speed": 1.1
                }
            ],
            "voice_mapping": {
                "主持人": "female-yujie",
                "嘉宾": "male-qn-qingse"
            },
            "generate_timestamps": True,
            "enable_concurrent": True
        }
        
        logger.info("📤 发送工具调用请求...")
        logger.info(f"📋 对话脚本: {len(test_input['dialogue_script'])}句")
        
        # 异步调用工具
        result = await multi_speaker_tts_tool.arun(tool_input=test_input)
        
        logger.info("📥 工具执行完成")
        logger.info(f"📄 执行结果: {result}")
        
        # 检查结果
        if "✅" in str(result):
            logger.info("🎉 多人TTS工具执行成功！")
            return True
        else:
            logger.error(f"❌ 工具执行失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 工具执行异常: {e}", exc_info=True)
        return False

async def test_agent_integration():
    """测试与Audio Agent的集成"""
    logger.info("🤖 测试与Audio Agent的集成...")
    
    try:
        from src.agents.agents import audio_creator_agent

        # 创建简单的测试输入
        test_input = {
            "messages": [],
            "user_request": "请为我制作一个关于AI技术的播客对话，包含主持人和嘉宾的对话"
        }
        
        logger.info("📤 向Audio Agent发送请求...")
        
        # 调用Audio Agent
        result = await audio_creator_agent.ainvoke(test_input)
        
        logger.info("📥 Audio Agent响应完成")
        logger.info(f"📄 Agent响应: {result}")
        
        # 检查响应中是否包含多人TTS工具的使用
        response_text = str(result)
        if "multi_speaker_tts" in response_text.lower() or "多人" in response_text:
            logger.info("🎉 Audio Agent成功识别并可能使用了多人TTS工具！")
            return True
        else:
            logger.info("ℹ️ Audio Agent响应中未明确提及多人TTS工具，但集成正常")
            return True
            
    except Exception as e:
        logger.error(f"❌ Agent集成测试失败: {e}", exc_info=True)
        return False

async def main():
    """主测试函数"""
    logger.info("🎭 开始多人TTS工具集成测试...")
    
    # 测试步骤
    tests = [
        ("工具导入测试", test_tool_import),
        ("工具执行测试", test_tool_execution),
        ("Agent集成测试", test_agent_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("📊 集成测试总结")
    logger.info(f"{'='*50}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 多人TTS工具已成功集成到DeerFlow系统！")
        logger.info("🚀 现在AI Agent可以使用这个工具来生成多人对话音频了！")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和依赖")

if __name__ == "__main__":
    asyncio.run(main())
