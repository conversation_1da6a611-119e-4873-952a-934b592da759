#!/usr/bin/env python3
"""
测试修复后的导入
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path='.env')

def test_audio_tools_import():
    """测试音频工具导入"""
    print("🔧 测试音频工具导入...")
    
    try:
        from src.tools import audio_tools, multi_speaker_tts_tool
        
        print(f"✅ audio_tools导入成功，数量: {len(audio_tools)}")
        print(f"✅ multi_speaker_tts_tool导入成功: {multi_speaker_tts_tool.name if multi_speaker_tts_tool else 'None'}")
        
        # 列出所有工具
        print("\n🔧 audio_tools列表:")
        for i, tool in enumerate(audio_tools):
            if tool is not None:
                name = getattr(tool, 'name', 'Unknown')
                print(f"   {i+1}. {name}")
            else:
                print(f"   {i+1}. None")
        
        # 检查multi_speaker_tts是否在列表中
        multi_tts_names = [getattr(tool, 'name', '') for tool in audio_tools if tool is not None]
        has_multi_tts = 'multi_speaker_tts' in multi_tts_names
        
        print(f"\n📋 multi_speaker_tts在audio_tools中: {'✅ 是' if has_multi_tts else '❌ 否'}")
        
        return has_multi_tts
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_expert_tools_import():
    """测试Expert工具导入"""
    print("\n🤖 测试Expert工具导入...")
    
    try:
        from src.tools import get_expert_tools
        
        print("✅ get_expert_tools函数导入成功")
        
        # 获取Expert工具
        expert_tools = get_expert_tools()
        print(f"✅ Expert工具获取成功，数量: {len(expert_tools)}")
        
        if expert_tools:
            print("\n🔧 Expert工具列表:")
            for i, tool in enumerate(expert_tools):
                if tool is not None:
                    name = getattr(tool, 'name', 'Unknown')
                    print(f"   {i+1}. {name}")
                else:
                    print(f"   {i+1}. None")
        
        return len(expert_tools) > 0
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_agent_import():
    """测试Audio Agent导入"""
    print("\n🎭 测试Audio Agent导入...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        print(f"✅ Audio Agent导入成功")
        print(f"📋 Agent类型: {type(audio_creator_agent)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 测试修复后的导入...")
    
    # 测试步骤
    tests = [
        ("音频工具导入", test_audio_tools_import),
        ("Expert工具导入", test_expert_tools_import),
        ("Audio Agent导入", test_audio_agent_import)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        result = test_func()
        results[test_name] = result
        
        if result:
            print(f"✅ {test_name} - 成功")
        else:
            print(f"❌ {test_name} - 失败")
    
    # 总结
    print(f"\n{'='*50}")
    print("📊 测试总结")
    print(f"{'='*50}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("\n🎉 循环导入问题修复成功！")
        print("🎭 multi_speaker_tts工具现在应该可以正常使用了！")
    else:
        print("\n⚠️ 仍有问题需要解决")
