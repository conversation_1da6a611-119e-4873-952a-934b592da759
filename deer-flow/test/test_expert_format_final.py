#!/usr/bin/env python3
"""
测试Expert格式最终修复
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_expert_format_consistency():
    """测试Expert格式一致性"""
    logger.info("🔧 测试Expert格式最终一致性...")
    
    try:
        # 读取experts.py文件
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/experts.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查三个Expert是否都使用相同的模式
        patterns_to_check = {
            "都使用enhanced_context展开": content.count("**enhanced_context") >= 3,
            "都构建full_message": content.count("full_message = task_description") >= 3,
            "都处理step_inputs": content.count('if step_inputs:') >= 3,
            "都处理available_files": content.count('if enhanced_context.get("available_files"):') >= 3,
            "都使用相同的messages格式": content.count('"messages": [("human", full_message)]') >= 3
        }
        
        logger.info("🔍 Expert格式一致性检查:")
        all_passed = True
        for pattern, passed in patterns_to_check.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {pattern}: {'一致' if passed else '不一致'}")
            if not passed:
                all_passed = False
        
        # 检查是否移除了旧的不一致格式
        old_patterns = {
            "移除了task字段": '"task": enhanced_context' not in content,
            "移除了直接字段传递": content.count('"description": task_description') == 0,  # 应该只在enhanced_context构建中出现
        }
        
        logger.info("🔍 旧格式移除检查:")
        for pattern, passed in old_patterns.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {pattern}: {'完成' if passed else '未完成'}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Expert格式一致性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始Expert格式最终修复验证...")
    
    result = test_expert_format_consistency()
    
    if result:
        logger.info("🎉 Expert格式修复完全成功！")
        logger.info("")
        logger.info("🚀 **修复总结:**")
        logger.info("   ✅ 所有Expert现在使用完全相同的输入格式")
        logger.info("   ✅ 都使用enhanced_context展开模式")
        logger.info("   ✅ 都构建包含上下文的full_message")
        logger.info("   ✅ 都正确处理step_inputs和available_files")
        logger.info("   ✅ 移除了导致不一致的旧格式")
        logger.info("")
        logger.info("🎯 **预期效果:**")
        logger.info("   • Audio Expert现在应该与Visual Expert行为完全一致")
        logger.info("   • 不再出现JSON解析错误")
        logger.info("   • 相声剧本任务应该能正常处理")
        logger.info("")
        logger.info("🎭 现在可以重新测试Audio Agent的相声剧本生成了！")
    else:
        logger.error("❌ Expert格式修复未完全成功，需要进一步检查")

if __name__ == "__main__":
    main()
