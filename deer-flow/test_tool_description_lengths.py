#!/usr/bin/env python3
"""
测试所有工具描述长度
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_all_tool_descriptions():
    """测试所有工具描述长度"""
    logger.info("📏 测试所有工具描述长度...")
    
    try:
        # 直接导入工具创建函数
        from src.config.configuration import Configuration
        from src.tools.audio.music_generation import get_suno_music_generation_tool
        from src.tools.audio.text_to_speech import get_text_to_speech_tool, get_voice_clone_tool, get_text_to_voice_tool
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        
        config = Configuration.from_runnable_config()
        
        # 创建所有工具
        tools_info = [
            ("suno_music_generation", get_suno_music_generation_tool(config)),
            ("text_to_speech_generator", get_text_to_speech_tool(config)),
            ("voice_cloner", get_voice_clone_tool(config)),
            ("design_voice_from_prompt", get_text_to_voice_tool(config)),
            ("multi_speaker_tts", get_multi_speaker_tts_tool(config))
        ]
        
        logger.info("\n📊 工具描述长度对比:")
        logger.info("=" * 80)
        
        for expected_name, tool in tools_info:
            if tool is None:
                logger.warning(f"⚠️ {expected_name}: 工具创建失败 (None)")
                continue
            
            actual_name = getattr(tool, 'name', 'unknown')
            description = getattr(tool, 'description', '')
            desc_length = len(description)
            
            # 状态判断
            if desc_length > 1500:
                status = "❌ 过长"
            elif desc_length > 1000:
                status = "⚠️ 较长"
            elif desc_length > 500:
                status = "✅ 正常"
            else:
                status = "✅ 简洁"
            
            logger.info(f"{status} {actual_name}: {desc_length} 字符")
            
            # 如果是multi_speaker_tts，显示详细信息
            if actual_name == 'multi_speaker_tts':
                logger.info(f"📝 multi_speaker_tts描述前200字符:")
                logger.info(f"   {repr(description[:200])}")
                logger.info(f"📝 multi_speaker_tts描述后200字符:")
                logger.info(f"   {repr(description[-200:])}")
                
                # 检查描述中的特殊内容
                special_checks = {
                    "包含emoji": any(ord(c) > 127 for c in description),
                    "包含换行符": '\n' in description,
                    "包含大括号": '{' in description and '}' in description,
                    "包含引号": '"' in description and "'" in description,
                    "包含markdown": '**' in description or '*' in description,
                    "包含中文": any('\u4e00' <= c <= '\u9fff' for c in description)
                }
                
                logger.info(f"📋 multi_speaker_tts描述特征:")
                for check_name, has_feature in special_checks.items():
                    status = "✅" if has_feature else "❌"
                    logger.info(f"   {status} {check_name}")
        
        # 建议的描述长度
        logger.info(f"\n💡 建议:")
        logger.info(f"   • 工具描述应控制在500-800字符以内")
        logger.info(f"   • multi_speaker_tts当前1742字符，建议缩减到800字符以内")
        logger.info(f"   • 过长的描述可能导致JSON解析问题或LLM处理困难")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 工具描述长度测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_description_optimization():
    """建议描述优化方案"""
    logger.info("\n🎯 multi_speaker_tts描述优化建议:")
    logger.info("=" * 60)
    
    current_desc = """专业多人对话音频生成工具 - 基于Minimax TTS，支持播客、有声书、教学对话等场景。

🎭 **完整音色库 (46个，男声23个，女声21个，中性2个)：**
• 青年音色(8个): male-qn-qingse, male-qn-jingying, male-qn-badao, male-qn-daxuesheng, female-shaonv, female-yujie, female-chengshu, female-tianmei
• 专业主持人(2个): presenter_male, presenter_female
• 有声书(4个): audiobook_male_1, audiobook_male_2, audiobook_female_1, audiobook_female_2
• 精品版(8个): male-qn-qingse-jingpin, male-qn-jingying-jingpin, male-qn-badao-jingpin, male-qn-daxuesheng-jingpin, female-shaonv-jingpin, female-yujie-jingpin, female-chengshu-jingpin, female-tianmei-jingpin
• 儿童音色(3个): clever_boy, cute_boy, lovely_girl
• 角色扮演(11个): cartoon_pig, bingjiao_didi, junlang_nanyou, chunzhen_xuedi, lengdan_xiongzhang, badao_shaoye, tianxin_xiaoling, qiaopi_mengmei, wumei_yujie, diadia_xuemei, danya_xuejie
• 英文角色(10个): Santa_Claus, Grinch, Rudolph, Arnold, Charming_Santa, Charming_Lady, Sweet_Girl, Cute_Elf, Attractive_Girl, Serene_Woman

💡 **推荐声音组合：**
• professional: presenter_male + presenter_female - 专业主持人组合
• youth: male-qn-jingying + female-yujie - 青年组合
• audiobook: audiobook_male_1 + audiobook_female_1 - 有声书组合
• character: junlang_nanyou + tianxin_xiaoling - 角色扮演组合
• premium: male-qn-jingying-jingpin + female-yujie-jingpin - 精品版组合

📝 **简单使用示例：**
只需提供对话脚本，工具会自动分配声音。
参数格式：dialogue_script包含speaker和text字段的对象列表。

🔧 **高级控制：**
• voice_mapping: 指定每个角色的具体声音ID
• emotion: happy/sad/angry/calm等7种情感
• speed: 0.5-2.0语速控制
• vol: 0.1-10.0音量控制
• pitch: -12到12语调控制

📤 **输出内容：**
• 完整对话音频URL（可直接播放）
• 每句话的独立音频URL（便于编辑）
• 精确时间戳文件（用于视频制作）
• 详细统计信息（时长、说话人数等）"""
    
    optimized_desc = """Professional multi-speaker TTS tool for generating dialogue audio with multiple voices. Supports podcasts, audiobooks, educational content, and character conversations using Minimax TTS.

Key Features:
• 46 voice options (23 male, 21 female, 2 neutral) including youth, professional, audiobook, premium, children, and character voices
• Automatic voice assignment or custom voice mapping per speaker
• Concurrent audio generation for faster processing
• Emotion control (happy/sad/angry/calm), speed (0.5-2.0x), volume, and pitch adjustment
• Complete dialogue audio + individual clips + timestamp file output

Usage: Provide dialogue_script with speaker and text fields. Tool automatically assigns appropriate voices or use voice_mapping for custom assignment.

Recommended voice combinations: professional (presenter_male + presenter_female), youth (male-qn-jingying + female-yujie), audiobook, character, and premium pairs available."""
    
    logger.info(f"📏 当前描述长度: {len(current_desc)} 字符")
    logger.info(f"📏 优化后长度: {len(optimized_desc)} 字符")
    logger.info(f"📉 减少: {len(current_desc) - len(optimized_desc)} 字符 ({((len(current_desc) - len(optimized_desc)) / len(current_desc) * 100):.1f}%)")
    
    logger.info(f"\n✨ 优化后描述:")
    logger.info(f"   {repr(optimized_desc)}")

def main():
    """主函数"""
    logger.info("🎯 开始工具描述长度分析...")
    
    test_all_tool_descriptions()
    suggest_description_optimization()

if __name__ == "__main__":
    main()
