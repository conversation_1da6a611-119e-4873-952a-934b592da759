#!/usr/bin/env python3
"""
演示脚本：展示透明化AI工作流的新体验

这个脚本展示了改进后的interactive_chat.py的核心功能，
包括可视化器的各种显示效果。
"""

import asyncio
import time
from rich.console import Console
from rich.panel import Panel

# 导入我们的可视化器
import sys
import os
sys.path.append(os.path.dirname(__file__))

from interactive_chat import WorkflowVisualizer

console = Console()

def demo_thinking_process():
    """演示AI思考过程显示"""
    console.print("\n[bold blue]🎬 演示1: AI思考过程可视化[/bold blue]")
    
    visualizer = WorkflowVisualizer()
    
    # 模拟AI思考
    thought = """这是一个复杂的跨领域任务，需要：
1. 角色设计（哪吒外卖员形象）
2. 配音制作（指定台词）  
3. 视频合成（图片+音频）
我需要制定一个分步计划来完成这个任务。"""
    
    reasoning = "基于任务的复杂度和跨领域特性，我判断需要使用规划工具来分解任务。"
    
    visualizer.show_thinking_process(thought, reasoning)
    time.sleep(2)

def demo_task_analysis():
    """演示任务分析显示"""
    console.print("\n[bold blue]🎬 演示2: 智能任务分析[/bold blue]")
    
    visualizer = WorkflowVisualizer()
    
    # 模拟任务分析
    task = "做一个哪吒送外卖的搞笑短片，第一句话是'我本是哪吒今晚送外卖'"
    complexity = "复杂"
    decision = "需要规划 - 涉及图像生成、配音制作、视频合成多个领域"
    
    visualizer.show_task_analysis(task, complexity, decision)
    time.sleep(2)

def demo_plan_creation():
    """演示计划创建显示"""
    console.print("\n[bold blue]🎬 演示3: 执行计划生成[/bold blue]")
    
    visualizer = WorkflowVisualizer()
    
    # 模拟计划对象
    class MockStep:
        def __init__(self, step_id, description, tool_to_use, status="pending", dependencies=None):
            self.step_id = step_id
            self.description = description
            self.tool_to_use = tool_to_use
            self.status = status
            self.dependencies = dependencies or []
    
    class MockPlan:
        def __init__(self):
            self.steps = [
                MockStep(1, "设计哪吒送外卖的角色形象，融合传统神话与现代外卖元素", "visual_expert"),
                MockStep(2, "为台词'我本是哪吒今晚送外卖'生成配音", "audio_expert", dependencies=[]),
                MockStep(3, "将角色图片和配音合成为短视频", "video_expert", dependencies=[1, 2])
            ]
    
    mock_plan = MockPlan()
    visualizer.show_plan_creation(mock_plan)
    time.sleep(3)

def demo_tool_execution():
    """演示工具执行过程"""
    console.print("\n[bold blue]🎬 演示4: 工具执行过程[/bold blue]")
    
    visualizer = WorkflowVisualizer()
    
    # 步骤1: 显示步骤执行
    step_info = {
        'description': '设计哪吒送外卖的角色形象，融合传统神话与现代外卖元素',
        'tool_to_use': 'visual_expert'
    }
    visualizer.show_step_execution(step_info, 1)
    time.sleep(1)
    
    # 步骤2: 显示工具调用
    tool_args = {
        'task_description': '设计哪吒送外卖的角色形象，融合传统神话与现代外卖元素',
        'context': '现代卡通风格，保持哪吒的经典特征如红缨枪、莲花等',
        'step_inputs': {
            'style': 'modern_cartoon',
            'character': 'nezha',
            'theme': 'food_delivery',
            'elements': ['red_spear', 'lotus', 'delivery_uniform']
        }
    }
    visualizer.show_tool_call('visual_expert', tool_args, "开始创建角色设计...")
    time.sleep(2)
    
    # 步骤3: 显示工具结果
    result = {
        'success': True,
        'content': '成功生成哪吒外卖员角色形象',
        'assets': {
            'images': [{
                'name': '现代哪吒外卖配送员',
                'description': '保持哪吒经典特征的现代外卖员形象，穿着外卖制服，手持红缨枪改造的外卖保温箱',
                'url': 'https://example.com/nezha_delivery.jpg'
            }]
        },
        'metadata': {
            'style': 'modern_cartoon',
            'resolution': '1024x1024',
            'model_used': 'FLUX-1.1-pro'
        }
    }
    visualizer.show_tool_result('visual_expert', result, True)
    time.sleep(2)
    
    # 步骤4: 显示步骤完成
    visualizer.show_step_completion(1, True)
    time.sleep(1)

def demo_final_summary():
    """演示最终总结显示"""
    console.print("\n[bold blue]🎬 演示5: 最终结果总结[/bold blue]")
    
    visualizer = WorkflowVisualizer()
    visualizer.start_time = time.time() - 45.6  # 模拟执行时间
    visualizer.total_steps = 3
    
    final_message = """🎉 哪吒送外卖短片制作完成！

我已经成功为您创建了一个搞笑的哪吒送外卖短片：

📸 **角色设计**: 现代哪吒外卖员形象，保持经典特征同时融入外卖元素
🎵 **配音制作**: "我本是哪吒今晚送外卖" - 年轻男性声音，略带神话色彩
🎬 **视频合成**: 将角色图片和配音完美结合

![哪吒外卖员](https://example.com/nezha_delivery_video.mp4)

这个短片既保持了哪吒的神话魅力，又巧妙地融入了现代外卖文化，相信会带来不少欢乐！"""
    
    visualizer.show_final_summary(final_message, 45.6)
    time.sleep(3)

def main():
    """主演示函数"""
    console.print(Panel(
        "[bold]🎭 DeerFlow V2 透明化AI工作流演示[/bold]\n\n"
        "这个演示展示了全新的透明化用户体验，\n"
        "让您完全看到AI的思考和工作过程！\n\n"
        "[yellow]即将演示以下功能:[/yellow]\n"
        "1. 🧠 AI思考过程可视化\n"
        "2. 📊 智能任务分析\n"
        "3. 📋 执行计划生成\n"
        "4. 🔧 工具执行过程\n"
        "5. 🎉 最终结果总结",
        title="[bold cyan]演示开始[/bold cyan]",
        border_style="cyan"
    ))
    
    input("\n按回车键开始演示...")
    
    # 运行各个演示
    demo_thinking_process()
    input("\n按回车键继续...")
    
    demo_task_analysis()
    input("\n按回车键继续...")
    
    demo_plan_creation()
    input("\n按回车键继续...")
    
    demo_tool_execution()
    input("\n按回车键继续...")
    
    demo_final_summary()
    
    console.print(Panel(
        "[bold green]🎉 演示完成！[/bold green]\n\n"
        "这就是DeerFlow V2的全新透明化体验。\n"
        "现在您可以运行 [bold]python interactive_chat.py[/bold] \n"
        "来体验真实的AI工作流！",
        title="[bold green]演示结束[/bold green]",
        border_style="green"
    ))

if __name__ == "__main__":
    main()
