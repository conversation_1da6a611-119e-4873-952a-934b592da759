#!/usr/bin/env python3
"""
直接测试Audio Agent的工具列表
"""

import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_audio_agent_tools():
    """直接测试Audio Agent的工具"""
    logger.info("🎵 直接测试Audio Agent的工具列表...")
    
    try:
        # 直接导入Audio Agent
        from src.agents.agents import audio_creator_agent
        
        logger.info("✅ Audio Agent导入成功")
        logger.info(f"📋 Agent类型: {type(audio_creator_agent).__name__}")
        
        # 检查Agent的属性
        agent_attrs = dir(audio_creator_agent)
        logger.info(f"📋 Agent属性数量: {len(agent_attrs)}")
        
        # 查找工具相关的属性
        tool_attrs = [attr for attr in agent_attrs if 'tool' in attr.lower()]
        logger.info(f"📋 工具相关属性: {tool_attrs}")
        
        # 尝试获取工具列表
        tools = None
        if hasattr(audio_creator_agent, 'tools'):
            tools = audio_creator_agent.tools
            logger.info(f"📋 Agent.tools: {len(tools) if tools else 0}个工具")
        elif hasattr(audio_creator_agent, 'runnable') and hasattr(audio_creator_agent.runnable, 'tools'):
            tools = audio_creator_agent.runnable.tools
            logger.info(f"📋 Agent.runnable.tools: {len(tools) if tools else 0}个工具")
        elif hasattr(audio_creator_agent, 'graph'):
            logger.info("📋 Agent使用graph结构")
            # 检查graph的节点
            if hasattr(audio_creator_agent.graph, 'nodes'):
                nodes = audio_creator_agent.graph.nodes
                logger.info(f"📋 Graph节点: {list(nodes.keys()) if nodes else []}")
        
        if tools:
            logger.info("\n🔧 Audio Agent工具列表:")
            for i, tool in enumerate(tools):
                tool_name = getattr(tool, 'name', f'Tool_{i}')
                tool_desc = getattr(tool, 'description', 'No description')[:100]
                logger.info(f"   {i+1}. {tool_name}")
                logger.info(f"      描述: {tool_desc}...")
            
            # 检查是否有multi_speaker_tts
            multi_tts_found = any(
                getattr(tool, 'name', '') == 'multi_speaker_tts' 
                for tool in tools
            )
            
            if multi_tts_found:
                logger.info("\n✅ 找到multi_speaker_tts工具！")
                # 获取该工具的详细信息
                multi_tts_tool = next(
                    tool for tool in tools 
                    if getattr(tool, 'name', '') == 'multi_speaker_tts'
                )
                logger.info(f"📋 工具类型: {type(multi_tts_tool).__name__}")
                logger.info(f"📋 工具描述长度: {len(multi_tts_tool.description) if hasattr(multi_tts_tool, 'description') else 0}")
            else:
                logger.error("\n❌ 未找到multi_speaker_tts工具！")
                logger.info("📋 可用工具名称:")
                for tool in tools:
                    logger.info(f"   - {getattr(tool, 'name', 'Unknown')}")
        else:
            logger.warning("⚠️ 无法获取Audio Agent的工具列表")
        
        return tools is not None and len(tools) > 0
        
    except Exception as e:
        logger.error(f"❌ Audio Agent工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_tools_import():
    """测试audio_tools导入"""
    logger.info("📦 测试audio_tools导入...")
    
    try:
        from src.tools import audio_tools
        
        logger.info(f"✅ audio_tools导入成功，共{len(audio_tools)}个工具")
        
        logger.info("\n🔧 audio_tools列表:")
        for i, tool in enumerate(audio_tools):
            tool_name = getattr(tool, 'name', f'Tool_{i}')
            logger.info(f"   {i+1}. {tool_name}")
        
        # 检查multi_speaker_tts
        multi_tts_in_tools = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in audio_tools
        )
        
        if multi_tts_in_tools:
            logger.info("✅ audio_tools中包含multi_speaker_tts")
        else:
            logger.error("❌ audio_tools中不包含multi_speaker_tts")
        
        return multi_tts_in_tools
        
    except Exception as e:
        logger.error(f"❌ audio_tools导入失败: {e}")
        return False

def test_tool_serialization():
    """测试工具序列化（模拟LangGraph的工具处理）"""
    logger.info("🔄 测试工具序列化...")
    
    try:
        from src.tools import audio_tools
        
        # 模拟LangGraph对工具的序列化处理
        serialized_tools = []
        
        for tool in audio_tools:
            try:
                # 尝试获取工具的基本信息
                tool_info = {
                    "name": getattr(tool, 'name', 'unknown'),
                    "description": getattr(tool, 'description', 'no description'),
                    "type": type(tool).__name__
                }
                
                # 检查描述长度和内容
                desc = tool_info["description"]
                if len(desc) > 1000:
                    logger.warning(f"⚠️ 工具 {tool_info['name']} 描述过长: {len(desc)} 字符")
                
                # 检查描述中是否有特殊字符
                if '{' in desc and '}' in desc:
                    logger.warning(f"⚠️ 工具 {tool_info['name']} 描述包含大括号")
                
                if '"' in desc and "'" in desc:
                    logger.warning(f"⚠️ 工具 {tool_info['name']} 描述包含混合引号")
                
                serialized_tools.append(tool_info)
                
            except Exception as e:
                logger.error(f"❌ 工具序列化失败: {getattr(tool, 'name', 'unknown')} - {e}")
        
        logger.info(f"✅ 成功序列化 {len(serialized_tools)} 个工具")
        
        # 检查multi_speaker_tts的序列化
        multi_tts_serialized = next(
            (tool for tool in serialized_tools if tool['name'] == 'multi_speaker_tts'),
            None
        )
        
        if multi_tts_serialized:
            logger.info("✅ multi_speaker_tts工具序列化成功")
            logger.info(f"📋 描述长度: {len(multi_tts_serialized['description'])}")
        else:
            logger.error("❌ multi_speaker_tts工具序列化失败")
        
        return len(serialized_tools) == len(audio_tools)
        
    except Exception as e:
        logger.error(f"❌ 工具序列化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始Audio Agent工具直接测试...")
    
    # 测试步骤
    tests = [
        ("audio_tools导入", test_audio_tools_import),
        ("Audio Agent工具", test_audio_agent_tools),
        ("工具序列化", test_tool_serialization)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 Audio Agent工具测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 Audio Agent包含multi_speaker_tts工具！")
        logger.info("问题可能在于JSON解析或其他运行时错误")
    else:
        logger.warning("⚠️ Audio Agent缺少multi_speaker_tts工具或存在其他问题")

if __name__ == "__main__":
    main()
