#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试语音克隆工具的URL支持功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parents[1]
sys.path.insert(0, str(project_root))

def test_url_detection():
    """测试URL检测功能"""
    print("🔍 测试URL检测功能...")
    
    try:
        from src.tools.audio.voice_clone import VoiceCloneInput
        
        # 测试URL输入
        url_params = {
            "audio_file_path": "https://example.com/audio.mp3",
            "voice_id": "TestVoiceURL",
            "test_text": "这是URL音频测试"
        }
        
        url_input = VoiceCloneInput(**url_params)
        print(f"✅ URL输入验证通过: {url_input.audio_file_path}")
        
        # 测试本地文件路径输入
        local_params = {
            "audio_file_path": "/path/to/local/audio.mp3",
            "voice_id": "TestVoiceLocal",
            "test_text": "这是本地文件测试"
        }
        
        local_input = VoiceCloneInput(**local_params)
        print(f"✅ 本地文件输入验证通过: {local_input.audio_file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL检测测试失败: {e}")
        return False


def test_model_fixed():
    """测试模型参数固定"""
    print("\n🔧 测试模型参数固定...")
    
    try:
        from src.tools.audio.voice_clone import VoiceCloneInput
        
        # 不提供model参数，应该使用默认值
        params = {
            "audio_file_path": "https://example.com/audio.mp3",
            "voice_id": "TestModel"
        }
        
        input_obj = VoiceCloneInput(**params)
        print(f"✅ 默认模型: {input_obj.model}")
        
        if input_obj.model == "speech-02-hd":
            print("✅ 模型已正确固定为 speech-02-hd")
            return True
        else:
            print(f"❌ 模型应该是 speech-02-hd，但是: {input_obj.model}")
            return False
            
    except Exception as e:
        print(f"❌ 模型固定测试失败: {e}")
        return False


def test_tool_creation():
    """测试工具创建（模拟配置）"""
    print("\n🛠️ 测试工具创建...")
    
    try:
        from src.tools.audio.voice_clone import get_voice_clone_tool
        
        # 创建模拟配置
        class MockConfig:
            def __init__(self):
                self.minimax_api_key = "test_key"
                self.minimax_group_id = "test_group"
                self.cos_bucket = "test_bucket"
                self.cos_region = "test_region"
        
        config = MockConfig()
        tool = get_voice_clone_tool(config)
        
        if tool:
            print(f"✅ 工具创建成功: {tool.name}")
            
            # 检查描述是否包含URL相关信息
            if "音频URL" in tool.description and "网络音频资源" in tool.description:
                print("✅ 工具描述已更新，包含URL支持信息")
                return True
            else:
                print("❌ 工具描述未包含URL支持信息")
                return False
        else:
            print("❌ 工具创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 工具创建测试失败: {e}")
        return False


def test_parameter_optimization():
    """测试参数优化"""
    print("\n📋 测试参数优化...")
    
    try:
        from src.tools.audio.voice_clone import VoiceCloneInput
        
        # 测试最简参数（AI Agent常用场景）
        minimal_params = {
            "audio_file_path": "https://example.com/voice.mp3",
            "voice_id": "AIGeneratedVoice"
        }
        
        minimal_input = VoiceCloneInput(**minimal_params)
        print(f"✅ 最简参数测试通过")
        print(f"   - 音频路径: {minimal_input.audio_file_path}")
        print(f"   - 音色ID: {minimal_input.voice_id}")
        print(f"   - 默认模型: {minimal_input.model}")
        print(f"   - 降噪: {minimal_input.enable_noise_reduction}")
        print(f"   - 音量归一化: {minimal_input.enable_volume_normalization}")
        
        # 测试带试听的参数
        with_test_params = {
            "audio_file_path": "https://example.com/voice.mp3",
            "voice_id": "AIVoiceWithTest",
            "test_text": "这是一个音色克隆测试，请听听效果如何。"
        }
        
        test_input = VoiceCloneInput(**with_test_params)
        print(f"✅ 带试听参数测试通过")
        print(f"   - 试听文本: {test_input.test_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数优化测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🎭 语音克隆工具URL支持测试")
    print("=" * 60)
    
    tests = [
        ("URL检测功能", test_url_detection),
        ("模型参数固定", test_model_fixed),
        ("工具创建", test_tool_creation),
        ("参数优化", test_parameter_optimization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！语音克隆工具已优化完成。")
        print("\n📝 优化总结:")
        print("- ✅ 支持音频URL输入")
        print("- ✅ 模型固定为 speech-02-hd")
        print("- ✅ 参数描述更新")
        print("- ✅ 适合AI Agent使用")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
