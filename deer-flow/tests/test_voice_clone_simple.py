#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版语音克隆工具测试
直接测试 voice_clone.py 模块，避免复杂依赖
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parents[1]
sys.path.insert(0, str(project_root))

def test_voice_clone_module():
    """测试语音克隆模块导入和基本功能"""
    print("🔧 测试语音克隆模块...")
    
    try:
        # 直接导入模块
        from src.tools.audio.voice_clone import VoiceCloneInput, get_voice_clone_tool
        print("✅ 模块导入成功")
        
        # 测试输入模式
        print("\n📋 测试输入模式...")
        
        # 测试有效参数
        valid_params = {
            "audio_file_path": "/path/to/test.mp3",
            "voice_id": "TestVoice123",
            "test_text": "这是一个测试",
            "enable_noise_reduction": True,
            "enable_volume_normalization": True
        }
        
        input_obj = VoiceCloneInput(**valid_params)
        print(f"✅ 有效参数验证通过: {input_obj.voice_id}")
        
        # 测试无效参数 - voice_id太短
        try:
            invalid_params = {
                "audio_file_path": "/path/to/test.mp3",
                "voice_id": "123",  # 太短
            }
            VoiceCloneInput(**invalid_params)
            print("❌ 应该拒绝无效的voice_id")
            return False
        except Exception as e:
            print(f"✅ 正确拒绝无效voice_id: {str(e)[:50]}...")
        
        # 测试自动生成voice_id
        auto_params = {
            "audio_file_path": "/path/to/test.mp3",
            # 不提供voice_id
        }
        auto_input = VoiceCloneInput(**auto_params)
        print(f"✅ 自动生成voice_id测试通过")
        
        print("\n🛠️ 测试工具创建...")
        
        # 创建模拟配置
        class MockConfig:
            def __init__(self):
                self.minimax_api_key = "test_key"
                self.minimax_group_id = "test_group"
                self.cos_bucket = "test_bucket"
                self.cos_region = "test_region"
        
        config = MockConfig()
        tool = get_voice_clone_tool(config)
        
        if tool:
            print(f"✅ 工具创建成功")
            print(f"📝 工具名称: {tool.name}")
            print(f"📋 工具描述长度: {len(tool.description)} 字符")
            
            # 检查工具属性
            if hasattr(tool, 'args_schema'):
                print(f"✅ 工具有输入模式: {tool.args_schema.__name__}")
            else:
                print("❌ 工具缺少输入模式")
                return False
                
            return True
        else:
            print("❌ 工具创建失败")
            return False
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def test_voice_clone_validation():
    """测试详细的参数验证"""
    print("\n🔍 测试详细参数验证...")
    
    try:
        from src.tools.audio.voice_clone import VoiceCloneInput
        
        # 测试各种voice_id格式
        test_cases = [
            ("ValidVoice123", True, "有效的voice_id"),
            ("Voice_Test_01", True, "包含下划线"),
            ("Voice-Test-02", True, "包含连字符"),
            ("MyVoice2024", True, "字母数字组合"),
            ("123Voice", False, "数字开头"),
            ("Voice_", False, "下划线结尾"),
            ("Voice-", False, "连字符结尾"),
            ("Short", False, "太短"),
            ("V" * 300, False, "太长"),
        ]
        
        passed = 0
        for voice_id, should_pass, description in test_cases:
            try:
                VoiceCloneInput(
                    audio_file_path="/test.mp3",
                    voice_id=voice_id
                )
                if should_pass:
                    print(f"✅ {description}: {voice_id}")
                    passed += 1
                else:
                    print(f"❌ 应该失败但通过了: {voice_id}")
            except Exception as e:
                if not should_pass:
                    print(f"✅ {description}: 正确拒绝 {voice_id}")
                    passed += 1
                else:
                    print(f"❌ 应该通过但失败了: {voice_id} - {e}")
        
        print(f"\n参数验证测试: {passed}/{len(test_cases)} 通过")
        return passed == len(test_cases)
        
    except Exception as e:
        print(f"❌ 验证测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🎭 简化版语音克隆工具测试")
    print("=" * 60)
    
    tests = [
        ("模块功能", test_voice_clone_module),
        ("参数验证", test_voice_clone_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新版语音克隆工具基本功能正常。")
        print("\n📝 使用说明:")
        print("- 工具名称: clone_voice_from_audio")
        print("- 主要参数: audio_file_path, voice_id")
        print("- 可选参数: test_text, prompt_audio_path, prompt_text")
        print("- 音频要求: mp3/m4a/wav, 10秒-5分钟, ≤20MB")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
