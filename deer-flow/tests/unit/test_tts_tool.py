import os
import sys
from dotenv import load_dotenv

# Add project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.tools.audio.text_to_speech import get_text_to_speech_tool

def test_tts_tool():
    """
    A simple test function for the Text-to-Speech tool.
    """
    # Load environment variables from .env file in the project root
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    load_dotenv(dotenv_path=dotenv_path)
    
    api_key = os.getenv("MINIMAX_API_KEY")
    group_id = os.getenv("MINIMAX_GROUP_ID")

    if not api_key or not group_id:
        print("错误：必须设置 MINIMAX_API_KEY 和 MINIMAX_GROUP_ID。")
        print("请在 'deer-flow' 目录下创建一个 .env 文件并填入这些值。")
        print("文件内容格式如下:\nMINIMAX_API_KEY=\"your_api_key_here\"\nMINIMAX_GROUP_ID=\"your_group_id_here\"")
        return

    print("正在初始化文本转语音（TTS）工具...")
    tts_tool = get_text_to_speech_tool(api_key=api_key, group_id=group_id)
    
    test_text = "你好，欢迎使用MiniMax开放平台。"
    
    print(f"正在使用文本调用工具: '{test_text}'")
    
    try:
        # The tool's input is a dictionary matching the TextToSpeechInput schema
        result = tts_tool.invoke({"text": test_text})
        print("\n--- 工具返回结果 ---")
        print(result)
        print("--------------------")
        assert "Successfully generated speech" in result
        print("\n测试成功通过！")
    except Exception as e:
        print(f"\n工具调用期间发生错误: {e}")

if __name__ == "__main__":
    test_tts_tool() 