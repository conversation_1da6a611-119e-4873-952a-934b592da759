"""
A minimal, focused unit test for a single tool.

This script imports and tests ONLY the 'jmeng_image_generator' tool
to ensure it works in complete isolation before testing other tools or agents.
This approach avoids chain-reaction import errors from other modules.
"""
import os
import pprint
import asyncio
from dotenv import load_dotenv
import sys
import importlib.util # Import the necessary library for true isolation

# This is crucial and must be at the top
load_dotenv()

# --- Step 1: Define the path to the module we want to test ---
# We build the absolute path to the specific .py file.
MODULE_PATH = os.path.abspath("src/tools/image/text_to_image.py")
MODULE_NAME = "text_to_image"

def load_module_from_file(path, name):
    """
    Loads a Python module directly from its file path, bypassing package __init__.
    This is the key to our new, truly isolated testing strategy.
    """
    spec = importlib.util.spec_from_file_location(name, path)
    if spec is None:
        raise ImportError(f"Could not load spec for module {name} from {path}")
    module = importlib.util.module_from_spec(spec)
    sys.modules[name] = module
    spec.loader.exec_module(module)
    return module

async def run_single_tool_test():
    """
    Initializes and runs a single tool to confirm its functionality.
    """
    print("--- Running Focused Unit Test for: jmeng_image_generator ---")

    # --- Step 2: Load the module directly, bypassing the package ---
    try:
        text_to_image_module = load_module_from_file(MODULE_PATH, MODULE_NAME)
        # Now we can get the factory function from the loaded module
        get_jmeng_image_generation_tool = text_to_image_module.get_jmeng_image_generation_tool
        print("Module 'text_to_image.py' loaded successfully in isolation.")
    except Exception as e:
        print(f"\n--- An error occurred during module loading ---")
        import traceback
        traceback.print_exc()
        return

    # --- Step 3: Get API Key and check for configuration ---
    api_key = os.getenv("JMENG_PROXY_API_KEY")
    if not api_key:
        print("\n[SKIPPED] JMENG_PROXY_API_KEY not found in .env file.")
        print("Please ensure the key is set to run this test.")
        return

    print("API Key found. Proceeding with the test.")

    try:
        # --- Step 4: Create the tool instance ---
        jmeng_tool = get_jmeng_image_generation_tool(api_key=api_key)
        print(f"Tool '{jmeng_tool.name}' created successfully.")

        # --- Step 5: Define input and invoke the tool ---
        tool_input = {"prompt": "a cute white cat"}
        print(f"Invoking tool with input: {tool_input}")

        result = await jmeng_tool.ainvoke(tool_input)

        # --- Step 6: Print the result ---
        print("\n--- Tool Execution Result ---")
        pprint.pprint(result)
        print("--------------------------")

    except Exception as e:
        print(f"\n--- An error occurred during the test ---")
        import traceback
        traceback.print_exc()
        print("-----------------------------------------")


if __name__ == "__main__":
    print("Starting the truly isolated tool test...")
    asyncio.run(run_single_tool_test())
    print("\nTest script finished.") 