import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
load_dotenv()

# 导入工具的工厂函数，而不是直接导入实例
from src.tools.image.flux_image_edit import get_flux_image_edit_tool

# 通过调用工厂函数来获取工具实例
flux_image_edit_tool = get_flux_image_edit_tool()

def main():
    """
    同步函数，用于调用 Flux 工具并打印结果。
    """
    if not flux_image_edit_tool:
        print("Flux 工具无法初始化。请检查您的环境变量配置。")
        return

    print("--- 正在调用 Flux 图像编辑工具 ---")
    
    # 策略: prompt 应该是清晰、详细的英文指令
    prompt = "turn this person into a 90s cartoon character, with vibrant colors and bold outlines"
    # 工具需要一个公开可访问的 URL 或本地文件路径
    input_image_url = "https://replicate.delivery/pbxt/N55l5TWGh8mSlNzW8usReoaNhGbFwvLeZR3TX1NL4pd2Wtfv/replicate-prediction-f2d25rg6gnrma0cq257vdw2n4c.png"
    
    print(f"Prompt: {prompt}")
    print(f"Input Image: {input_image_url}")
    print("------------------------------------")

    try:
        # 工具的函数是同步的，所以我们使用 invoke
        # 我们将测试默认的 'flux-kontext-pro' 模型
        result = flux_image_edit_tool.invoke({
            "prompt": prompt,
            "input_image": input_image_url,
        })
        print("\n--- 工具返回结果 ---")
        print(result)
        print("--------------------")
    except Exception as e:
        print(f"工具调用期间发生错误: {e}")
        print("请检查工具的实现以及 FLUX_API_KEY 是否已在您的 .env 文件中正确设置。")


if __name__ == "__main__":
    # 运行前的环境变量检查
    if not os.getenv("FLUX_API_KEY"):
        print("警告: 未设置所需的环境变量 'FLUX_API_KEY'。")
        print("工具将无法运行。请将其添加到您的 .env 文件中。")
    else:
        main() 
