# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
End-to-end test for the Audio Creator Agent, with optional Lang<PERSON>mith tracing.
This script runs a single, comprehensive task to test the agent's ability 
to orchestrate multiple tools in a coordinated sequence.
"""

import os
import sys
from dotenv import load_dotenv
from langsmith import traceable

# Add the project root to the Python path for absolute imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Load environment variables from the .env file in the project root
dotenv_path = os.path.join(project_root, '.env')
load_dotenv()

# Import agent and config after setting up the path
from src.agents.agents import audio_creator_agent
from src.config.configuration import Configuration

def run_audio_agent_traceable(prompt: str):
    """Wrapper to run the agent with LangSmith tracing."""
    @traceable(run_type="chain", name="Audio Agent Invocation")
    def traceable_agent_invocation():
        return audio_creator_agent.invoke({
            "messages": [("user", prompt)],
        })
    return traceable_agent_invocation()


if __name__ == "__main__":
    # --- Setup the Comprehensive "Nezha" Task ---
    
    # Verify that necessary API keys are configured before starting
    config = Configuration.from_runnable_config()
    if not all([config.suno_api_key, config.minimax_api_key, config.minimax_group_id]):
        print("❌ 错误: 缺少必要的 API 密钥 (SUNO_API_KEY, MINIMAX_API_KEY, MINIMAX_GROUP_ID)。请检查根目录下的 .env 文件。")
        sys.exit(1)

    # The user-provided audio file for Nezha's voice
    nezha_audio_path = os.path.join(project_root, 'assets', '哪吒.mp3')

    if not os.path.exists(nezha_audio_path):
        print(f"⚠️  跳过测试任务: 在 '{nezha_audio_path}' 未找到哪吒的音频文件。")
        sys.exit(1)

    # The comprehensive task for the agent
    task = f"请用哪吒的声音说出这段自我介绍：'我命由我不由天，是魔是仙，我自己说了算！'，并为这段话配上一段激昂、充满力量的国风战斗背景音乐。"
    # Add the file path context directly into the task description for the agent
    prompt = f"用户提供的哪吒声音文件位于 '{nezha_audio_path}'。任务：{task}"
    
    print(f"Invoking Audio Agent with prompt: '{prompt}'")
    print("ℹ️  注意: 由于缺少音频混合工具，预期将生成两个文件：一个语音文件和一个音乐文件。")

    # --- Execute the Agent ---

    langsmith_api_key = os.getenv("LANGSMITH_API_KEY")

    if not langsmith_api_key:
        print("\nLangSmith API Key not set, skipping traceable execution.")
        result = audio_creator_agent.invoke({
            "messages": [("user", prompt)],
        })
    else:
        print("\nRunning with LangSmith tracing...")
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGCHAIN_ENDPOINT"] = os.getenv("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com")
        os.environ["LANGCHAIN_API_KEY"] = langsmith_api_key
        os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGCHAIN_PROJECT", "deer-flow-audio-test")
        
        result = run_audio_agent_traceable(prompt)

    # --- Print the Final Result ---
    
    print("\n----- Agent Final Answer -----")
    if result and 'messages' in result and result['messages']:
        # The final answer is typically the last message in the list
        final_answer = result['messages'][-1].content
        print(final_answer)
    else:
        print("Could not extract a final answer from the agent's response.")
        print("Full response:", result)
    print("----------------------------") 