import os
import asyncio
from dotenv import load_dotenv

# Ensure the project root is in the Python path
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables from .env file, assuming it's in the deer-flow directory
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

from src.agents import audio_creator_agent
from src.utils.langsmith_utils import get_client

# Initialize LangSmith for tracing
client = get_client()

async def run_tts_agent_test():
    """
    Tests the AudioAgent's new TTS capability.
    The agent is asked to first write a short text praising <PERSON><PERSON><PERSON>,
    and then use the TTS tool to read it aloud.
    """
    print("\n--- [Test Case: Agent Writes and Speaks Praise for Nezha] ---")
    
    agent_executor = audio_creator_agent

    # A two-step prompt to test reasoning and tool selection
    input_prompt = {
        "messages": [
            ("user", "请为哪吒写一段简短的赞美之词，然后用一个年轻、坚定的男性声音（'male-qn-qingse'）将它朗读出来。")
        ]
    }
    
    print(f"Invoking AudioAgent with prompt: '{input_prompt['messages'][0][1]}'")

    try:
        # Stream events to observe the agent's process in real-time
        async for event in agent_executor.astream_events(input_prompt, version="v1"):
            kind = event["event"]
            if kind == "on_chat_model_stream":
                content = event["data"]["chunk"].content
                if content:
                    print(content, end="")
            elif kind == "on_tool_start":
                print(f"\n\n--- Calling Tool: {event['name']} ---")
                print("Parameters:")
                # Pretty print the input dictionary
                import json
                print(json.dumps(event['data'].get('input'), indent=2, ensure_ascii=False))
            elif kind == "on_tool_end":
                print(f"\n--- Tool Output for: {event['name']} ---")
                print(event['data'].get('output'))
                print("---------------------------------")

    except Exception as e:
        print(f"\n--- An error occurred during the agent execution ---")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Ensure necessary ENV VARS are set before running
    if not all([os.getenv("MINIMAX_API_KEY"), os.getenv("MINIMAX_GROUP_ID")]):
        print("\nERROR: MINIMAX_API_KEY and MINIMAX_GROUP_ID must be set in your .env file.")
        print("Please create a 'deer-flow/.env' file and add them before running.")
    else:
        asyncio.run(run_tts_agent_test()) 