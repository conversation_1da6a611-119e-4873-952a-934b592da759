import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add the project root ('deer-flow') to the Python path
# This allows us to import from 'src' regardless of where the script is run from
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now we can import our tool factory
from src.tools.video.text_to_video import get_text_to_video_tool, TextToVideoInput

async def main():
    """
    Main function to test the text_to_video tool.
    """
    # Load environment variables from the .env file in the `deer-flow` directory
    dotenv_path = os.path.join(project_root, '.env')
    load_dotenv(dotenv_path=dotenv_path)

    kling_api_base = os.getenv("KLING_API_BASE")
    kling_api_key = os.getenv("KLING_API_KEY")

    if not all([kling_api_base, kling_api_key]):
        logging.error("KLING_API_BASE and KLING_API_KEY must be set in your .env file.")
        return

    logging.info("Initializing Text-to-Video tool...")
    # Create an instance of the tool using the factory function
    text_to_video_tool = get_text_to_video_tool(
        api_base_url=kling_api_base,
        api_key=kling_api_key
    )

    if text_to_video_tool is None:
        logging.error("Failed to initialize the Text-to-Video tool.")
        return

    # Define the input for the tool
    # Using the Pydantic model for clarity and validation
    tool_input = TextToVideoInput(
        prompt="A beautiful sunset over the ocean, cinematic, 4k",
        model_name="kling-v1",
        aspect_ratio="16:9",
        duration="5"
    )

    logging.info(f"Running tool with input: {tool_input.dict()}")

    try:
        # Run the tool's async method
        result = await text_to_video_tool.ainvoke(tool_input.dict())
        logging.info("--- Tool Execution Result ---")
        logging.info(result)
        logging.info("-----------------------------")
    except Exception as e:
        logging.error(f"An error occurred while running the tool: {e}", exc_info=True)

if __name__ == "__main__":
    # In Python 3.8+, on Windows, the default event loop policy changes.
    # If you encounter issues on Windows, you might need to set the policy explicitly:
    # if os.name == 'nt':
    #     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main()) 