import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Setup basic logging to see detailed execution information
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

load_dotenv()

# Import the tool factory function from the module we want to test
from src.tools.audio.music_generation import get_suno_music_generation_tool

def test_instrumental_inspiration_mode():
    """
    Tests the 'Instrumental Inspiration Mode' of the Suno tool,
    which corresponds to '场景四: 纯音乐.灵感模式' from the documentation.
    """
    print("\n--- [Test Case: Instrumental Inspiration Mode] ---")
    
    api_key = os.getenv("SUNO_API_KEY")
    base_url = os.getenv("SUNO_BASE_URL")

    if not api_key or not base_url:
        print("SUNO_API_KEY and SUNO_BASE_URL must be set in your .env file. Skipping test.")
        return

    try:
        # 1. Create the tool instance directly using the factory
        suno_tool = get_suno_music_generation_tool(api_key=api_key, base_url=base_url)
        
        # 2. Define parameters based on the documentation for this scenario
        params = {
            "prompt": "A cheerful and relaxing Lofi hip-hop track, featuring a gentle piano melody, soft drum beats, and a warm, jazzy bassline. Perfect for a cozy coffee shop vlog atmosphere.",
            "make_instrumental": True,
            "tags": "lofi hip-hop, chill, cozy, coffee shop, vlog, instrumental",
            "title": "LoFi Cafe Vibes"
        }
        
        print("Invoking tool with params:")
        print(json.dumps(params, indent=2, ensure_ascii=False))

        # 3. Invoke the tool with the parameters
        result = suno_tool.invoke(params)
        
        print("\n--- Tool Result ---")
        print(result)
        print("--------------------")

    except Exception as e:
        print(f"\n--- An error occurred during the test ---")
        logging.error(e, exc_info=True)
        print("-----------------------------------------")

def test_nezha_custom_lyrics_mode():
    """
    Tests the 'Custom Lyrics Mode' of the Suno tool,
    which corresponds to '场景二: 自定义.歌词歌名' from the documentation.
    We will create a theme song for Nezha in Honor of Kings.
    """
    print("\n--- [Test Case: Nezha Custom Lyrics Mode (王者荣耀)] ---")
    
    api_key = os.getenv("SUNO_API_KEY")
    base_url = os.getenv("SUNO_BASE_URL")

    if not api_key or not base_url:
        print("SUNO_API_KEY and SUNO_BASE_URL must be set. Skipping test.")
        return

    try:
        suno_tool = get_suno_music_generation_tool(api_key=api_key, base_url=base_url)
        
        lyrics = """[Verse 1]
峡谷中，烽烟起
红莲之枪，燃我战意
脚下是风火轮，永不停息
所到之处，皆留燎原火迹

[Chorus]
乾坤圈出，定住顽敌
混天绫上，束缚天与地
三头六臂，神魔都要辟易
我命由我，就是唯一的真理
"""
        
        params = {
            "prompt": lyrics,
            "custom_mode": True,
            "make_instrumental": False,
            "tags": "王者荣耀, 哪吒, 战斗, 摇滚, 史诗, battle, rock, epic",
            "title": "哪吒 - 峡谷战歌"
        }
        
        print("Invoking tool with custom lyrics params:")
        print(json.dumps(params, indent=2, ensure_ascii=False))

        result = suno_tool.invoke(params)
        
        print("\n--- Tool Result ---")
        print(result)
        print("--------------------")

    except Exception as e:
        print(f"\n--- An error occurred during the test ---")
        logging.error(e, exc_info=True)
        print("-----------------------------------------")

if __name__ == "__main__":
    # We can run one test at a time by commenting out the others
    # test_instrumental_inspiration_mode()
    test_nezha_custom_lyrics_mode()

    # You can add more test functions here for other scenarios from the documentation,
    # for example, for '场景二: 自定义.歌词歌名':
    # def test_custom_lyrics_mode():
    #     ...
    #     params = {
    #         "prompt": "[Verse] Hello world, this is a test...",
    #         "custom_mode": True,
    #         "make_instrumental": False,
    #         ...
    #     }
    #     ...
    # test_custom_lyrics_mode() 