# test_all_tools.py

import asyncio
import logging
import os
from pathlib import Path
from dotenv import load_dotenv, find_dotenv
import json
import random

# --- Setup ---
# Load environment variables from .env file at the project root
# This allows the script to be run from the root of the `deer-flow` directory
load_dotenv(find_dotenv())

# Configure logging for clear output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Add the project's source directory to the Python path.
# This ensures that imports like `from src.config...` work correctly.
import sys
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

try:
    from langchain_core.runnables import RunnableConfig
    from src.config.configuration import Configuration
    from src.tools import (
        jmeng_tool,
        flux_tool,
        multi_image_flux_tool,
        # image_to_video_tool, # Temporarily disabled
        # text_to_video_tool, # Temporarily disabled
        suno_tool,
        tts_tool,
        voice_clone_tool,
        text_to_voice_tool,
    )
except ImportError as e:
    logger.error(f"Failed to import necessary modules: {e}")
    logger.error("Please ensure you run this script from the root of the 'deer-flow' project directory.")
    sys.exit(1)


# --- Test Functions ---

async def run_test(tool_name: str, tool_func, config: RunnableConfig, **kwargs):
    """Helper function to run a tool test and print the result."""
    logger.info(f"--- Running test for: {tool_name} ---")
    if not tool_func:
        logger.warning(f"Tool '{tool_name}' is not available/initialized. Skipping test.")
        return None
    try:
        # Use _arun for async execution of the tool, which is standard for LangChain tools
        result = await tool_func._arun(**kwargs, config=config)
        logger.info(f"✅ SUCCESS: {tool_name}")
        
        # Pretty print the result for readability
        print("Result:")
        try:
            # Try to parse if the result is a JSON string or a list/dict
            if isinstance(result, str):
                # Attempt to load string as JSON, otherwise print as is
                try:
                    result_data = json.loads(result)
                    print(json.dumps(result_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(result)
            else:
                # If it's already a dict/list, just dump it
                print(json.dumps(result, indent=2, ensure_ascii=False))
        except Exception:
             print(result) # Fallback to simple print

        return result
    except Exception as e:
        logger.error(f"❌ FAILED: {tool_name}", exc_info=True)
        return None


async def main():
    """Main function to run all tool tests."""
    logger.info("🚀 Starting comprehensive tool test suite...")
    
    # Create a runnable config, which can hold credentials or other settings.
    # The tools are designed to pull from environment variables if this is empty.
    run_config = RunnableConfig()
    
    # Store results to chain tests (e.g., use a generated image for an image-to-video test)
    test_results = {}

    # --- Image Tools ---
    logger.info("\n" + "="*20 + " TESTING IMAGE TOOLS " + "="*20)
    
    # 1. Text to Image
    image_result = await run_test(
        "jmeng_image_generation",
        jmeng_tool,
        config=run_config,
        prompt="A cute ginger cat wearing a wizard hat, magical sparks, detailed, fantasy art"
    )
    if image_result and isinstance(image_result, list) and len(image_result) > 0 and isinstance(image_result[0], str) and image_result[0].startswith("http"):
        test_results["image_url"] = image_result[0]
    elif image_result and isinstance(image_result, str) and image_result.startswith("http"):
        test_results["image_url"] = image_result
    else:
        logger.error(f"Text-to-image step failed or returned an invalid result: {image_result}")
        test_results["image_url"] = None

    # 2. Image Editing (depends on previous test)
    if test_results.get("image_url"):
        await run_test(
            "flux_image_editor",
            flux_tool,
            config=run_config,
            prompt="Make the cat a space cat, wearing a space helmet, background is a galaxy",
            input_image=test_results["image_url"]
        )
        await run_test(
           "multi_image_flux_editor",
           multi_image_flux_tool,
           config=run_config,
           prompt="A majestic lion",
           input_images=[test_results["image_url"]], # Using the same image for style and structure
        )
    else:
        logger.warning("Skipping Image Editing tests because the text-to-image step failed or returned an invalid result.")

    # --- Video Tools (Temporarily Disabled) ---
    # logger.info("\n" + "="*20 + " TESTING VIDEO TOOLS " + "="*20)

    # # 4. Image to Video (depends on first image test)
    # if test_results.get("image_url"):
    #     await run_test(
    #         "image_to_video",
    #         image_to_video_tool,
    #         config=run_config,
    #         image_url=test_results["image_url"],
    #         motion_strength=8 # A value between 1-10
    #     )
    # else:
    #     logger.warning("Skipping Image to Video test because the text-to-image step failed.")
    
    # # 5. Text to Video
    # await run_test(
    #     "text_to_video_generator",
    #     text_to_video_tool,
    #     config=run_config,
    #     prompt="A beautiful sunset over a calm ocean, cinematic, 4k"
    # )

    # --- Audio Tools ---
    logger.info("\n" + "="*20 + " TESTING AUDIO TOOLS " + "="*20)

    # 6. Music Generation
    music_result = await run_test(
        "suno_music_generation",
        suno_tool,
        config=run_config,
        prompt="A lofi hip-hop track with a chill beat, suitable for studying.",
        make_instrumental=True,
        title="Lofi Study Beats"
    )
    if music_result and isinstance(music_result, list) and len(music_result) > 0 and music_result[0].get("cos_url"):
        test_results["music_url"] = music_result[0]["cos_url"]

    # 7. Text to Speech
    await run_test(
        "text_to_speech_generator",
        tts_tool,
        config=run_config,
        text="Hello, this is a test of the text to speech synthesis system.",
        voice_id="male-qn-qingse"
    )

    # 8. Voice Cloning (requires user action OR previous test success)
    # !!! IMPORTANT !!! 
    # This test will first try to use the URL from the music generation test.
    # If that test failed, it will fall back to the local file path.
    # You SHOULD still configure the local path as a backup.
    voice_clone_audio_path = "/Users/<USER>/openArt-1/deer-flow/assets/voice_sample.mp3" # <-- CONFIGURE THIS AS A BACKUP
    
    input_audio_for_clone = test_results.get("music_url") # Prefer URL from previous test

    if not input_audio_for_clone:
        logger.warning("Music generation test failed or did not return a URL. Falling back to local file for voice cloning test.")
        if Path(voice_clone_audio_path).exists():
            input_audio_for_clone = voice_clone_audio_path
        else:
            logger.warning(f"--> Skipping Voice Clone test. The fallback local file was not found at: {voice_clone_audio_path}. Please provide a valid path.")
            input_audio_for_clone = None

    if input_audio_for_clone:
        await run_test(
            "voice_cloner",
            voice_clone_tool,
            config=run_config,
            voice_id=f"my-test-clone-voice-{random.randint(100000, 999999)}",
            main_audio_path=input_audio_for_clone
        )

    # 9. Voice Design (formerly the broken Text-to-Voice)
    # This test now correctly calls the voice design endpoint.
    await run_test(
        "voice_creator_from_text",
        text_to_voice_tool,
        config=run_config,
        prompt="讲述悬疑故事的播音员，声音低沉富有磁性，语速时快时慢，营造紧张神秘的氛围。",
        preview_text="夜深了，古屋里只有他一人。窗外传来若有若无的脚步声，他屏住呼吸，慢慢地，慢慢地，走向那扇吱呀作响的门……"
    )

    logger.info("\n🏁 Tool test suite finished.")


if __name__ == "__main__":
    # Standard way to run an async main function
    asyncio.run(main()) 