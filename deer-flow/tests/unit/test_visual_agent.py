import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from dotenv import load_dotenv
from langsmith import traceable

# Now we should be able to import the agent directly
from src.agents import visual_designer_agent

# It's crucial to load .env before accessing environment variables
load_dotenv()

# This is the new helper function for traceable execution
def run_visual_agent_traceable(prompt):
    """Wrapper to run the agent with Lang<PERSON>mith tracing."""
    # The @traceable decorator can be applied dynamically
    @traceable(run_type="chain", name="Visual Agent Invocation")
    def traceable_agent_invocation():
        return visual_designer_agent.invoke({
            "messages": ("human", prompt),
        })
    return traceable_agent_invocation()


if __name__ == "__main__":
    prompt = "请帮我画一张图，一只可爱的猫咪，戴着一顶小帽子。然后，把这张猫变成梵高艺术的感觉。"
    print(f"Invoking Visual Agent with prompt: '{prompt}'")

    # Explicitly check the key *after* load_dotenv()
    langsmith_api_key = os.getenv("LANGSMITH_API_KEY")
    print(f"DEBUG: LANGSMITH_API_KEY value is: {langsmith_api_key}")

    if not langsmith_api_key:
        print("LangSmith API Key not set, skipping traceable execution.")
        result = visual_designer_agent.invoke({
            "messages": ("human", prompt),
        })
    else:
        print("Running with LangSmith tracing...")
        # Ensure other LangSmith variables are also set
        os.environ["LANGCHAIN_TRACING_V2"] = "true" # Common requirement
        os.environ["LANGCHAIN_ENDPOINT"] = os.getenv("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com")
        os.environ["LANGCHAIN_API_KEY"] = langsmith_api_key
        os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "default")
        
        print(f"DEBUG: LANGCHAIN_TRACING_V2 set to: {os.getenv('LANGCHAIN_TRACING_V2')}")
        print(f"DEBUG: LANGCHAIN_PROJECT set to: {os.getenv('LANGCHAIN_PROJECT')}")

        result = run_visual_agent_traceable(prompt)

    print("\n----- Agent Final Answer -----")
    if result and 'messages' in result and result['messages']:
        final_answer = result['messages'][-1].content
        print(final_answer)
    else:
        print("Could not extract a final answer from the agent's response.")
        print("Full response:", result)
    print("----------------------------") 