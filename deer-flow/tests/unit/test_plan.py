"""
A pure unit test for the Art Planner Prompt.

This script tests the core logic of the planner: the prompt and the LLM call.
It intentionally avoids importing from the project's graph/agent/tool structure
to provide a completely isolated testing environment for the planner's prompt.
"""
import os
import pprint
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers.json import JsonOutputParser

# Load environment variables from .env file at the project root
# This assumes the script is run from the `deer-flow` directory
load_dotenv()

# --- Test Configuration ---
# User-provided custom model endpoint for testing
CUSTOM_MODEL_NAME = "claude-sonnet-4-20250514"
CUSTOM_API_BASE = "https://one.glbai.com/v1"  # The base URL for the OpenAI-compatible API
CUSTOM_API_KEY = "sk-ROdooapOU5MDI7kKA310B344702c45AeA6A675F87c3b6cAb"

# Configure the LLM to use the custom endpoint
LLM = ChatOpenAI(
    model=CUSTOM_MODEL_NAME,
    temperature=0.7,
    openai_api_base=CUSTOM_API_BASE,
    openai_api_key=CUSTOM_API_KEY,
)
PROMPT_FILE_PATH = "src/prompts/art_planner_prompt_zh.md"
TEST_QUERY = "我想做一个哪吒的角色介绍视频，要体现他独特的个性魅力"

def load_system_prompt(file_path: str) -> str:
    """Loads the system prompt from the specified file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Error: Prompt file not found at {file_path}")
        raise
    except Exception as e:
        print(f"Error reading prompt file: {e}")
        raise

def run_prompt_test():
    """
    Runs an isolated test of the planner prompt.
    """
    print("--- Running Isolated Planner Prompt Test ---")
    print(f"Model: {LLM.model_name}")
    print(f"Query: '{TEST_QUERY}'")
    print("-" * 40)

    try:
        # 1. Load the planner's system prompt
        system_prompt_content = load_system_prompt(PROMPT_FILE_PATH)
        print("Successfully loaded prompt file.")

        # 2. Construct the message list
        messages = [
            SystemMessage(content=system_prompt_content),
            HumanMessage(content=TEST_QUERY),
        ]

        # 3. Define the chain: LLM -> JSON Parser
        chain = LLM | JsonOutputParser()

        # 4. Invoke the chain and get the raw JSON output
        print("\nInvoking LLM... (This may take a moment)")
        generated_plan = chain.invoke(messages)
        print("LLM invocation complete.")

        # 5. Print the result
        print("\n--- Generated Plan (Raw JSON Output) ---")
        pprint.pprint(generated_plan)
        print("-" * 40)

    except Exception as e:
        print(f"\n--- An error occurred during the test ---")
        print(f"Error: {e}")
        print("Please check your .env file for correct API keys and model availability.")

if __name__ == "__main__":
    # To see the full LangChain logs, uncomment the following lines
    # from langchain.globals import set_debug, set_verbose
    # set_debug(True)
    # set_verbose(True)

    run_prompt_test() 