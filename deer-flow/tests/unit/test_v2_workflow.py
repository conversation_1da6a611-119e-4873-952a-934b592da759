# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
load_dotenv()

from src.graph_v2.builder import create_v2_workflow
from src.graph_v2.types import State
from langchain_core.messages import HumanMessage

async def run_v2_test(inputs: State, thread_id: str):
    """Helper function to run the V2 workflow and print the output."""
    graph = create_v2_workflow()
    
    print("\n--- Running V2 Workflow ---")
    print(f"Input: {inputs['messages'][-1].content}")
    
    config = {
        "recursion_limit": 100,
        "configurable": {"thread_id": thread_id},
    }

    final_state = None
    async for event in graph.astream(inputs, config=config):
        print("\n--- Event ---")
        # Print the node that just ran
        node_name = list(event.keys())[0]
        print(f"Node: {node_name}")
        
        # Print the current state
        current_state: State = event[node_name]
        final_state = current_state
        
        print("\n--- Current State ---")
        # Print messages
        for msg in current_state.get("messages", []):
            print(f"- {msg.__class__.__name__}: {msg.content}")
            if msg.type == "ai" and msg.tool_calls:
                print(f"  Tool Calls: {msg.tool_calls}")

        # Print plan object
        plan = current_state.get("plan")
        if plan:
            print("\nPlan:")
            print(plan.json(indent=2))
        else:
            print("\nPlan: None")
        print("---------------------\n")

    
    print("\n--- Final State ---")
    if final_state:
        plan = final_state.get("plan")
        if plan:
            print("Final Plan State:")
            print(plan.json(indent=2))
            # Assert that the plan is complete
            assert plan.is_complete(), "The final plan state is not complete!"
            print("\n✅ Assertion Passed: Plan is complete.")
        else:
            # For simple tasks, there might not be a plan, just a direct answer.
             final_message = final_state.get('messages', [])[-1]
             if final_message.type == "ai" and not final_message.tool_calls:
                 print(f"✅ Simple Task Completed: {final_message.content}")
             else:
                 assert False, "Workflow ended without a complete plan or a final answer."
    else:
        assert False, "Workflow finished with no final state."

    print("--------------------\n")
    return final_state

async def main():
    """Main function to run tests."""
    
    # Test case: Complex Video Task (Plan-and-Execute)
    print("\n" + "="*50)
    print("  Running Test Case: Complex Video Task")
    print("="*50)
    video_input = {
        "messages": [
            HumanMessage(
                content="Create a short video about a robot exploring Mars. The video should have some epic, cinematic music."
            )
        ],
        "plan": None,  # Start with no plan
    }
    await run_v2_test(video_input, "test-video-thread-1")


if __name__ == "__main__":
    # This setup is required for the async functions to run
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest interrupted by user.") 