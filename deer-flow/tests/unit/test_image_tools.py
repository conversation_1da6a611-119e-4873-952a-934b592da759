"""
Unit test script for individual Visual (Image) Tools.

This script tests each image-related tool in isolation to ensure it functions
correctly before testing the full agent integration.
"""
import os
import pprint
import asyncio
from dotenv import load_dotenv

# This is crucial and must be at the top
load_dotenv()

# Ensure the app path is in the system path to find the 'src' module
import sys
sys.path.insert(0, os.getcwd())

# Import the factory functions for the tools we want to test
from src.tools.image.text_to_image import get_jmeng_image_generation_tool
from src.tools.image.gpt_image import get_gpt_image_1_text_to_image_tool

# --- Test Configuration ---
# Ensure required API keys are in your .env file

async def test_jmeng_text_to_image():
    """Tests the Jmeng Text-to-Image tool."""
    print("\n--- Testing: jmeng_image_generator ---")
    api_key = os.getenv("JMENG_PROXY_API_KEY")
    if not api_key:
        print("SKIPPED: JMENG_PROXY_API_KEY not found in .env file.")
        return

    try:
        # 1. Create the tool instance using its factory function
        jmeng_tool = get_jmeng_image_generation_tool(api_key=api_key)
        print("Tool created successfully.")

        # 2. Define the input for the tool
        tool_input = {"prompt": "a cute cat wearing a small hat"}
        print(f"Invoking with input: {tool_input}")

        # 3. Invoke the tool
        result = await jmeng_tool.ainvoke(tool_input)

        # 4. Print the result
        print("Result:")
        pprint.pprint(result)
    except Exception as e:
        print(f"An error occurred during the test: {e}")

async def test_gpt_text_to_image():
    """Tests the GPT-Image-1 Text-to-Image tool."""
    print("\n--- Testing: gpt_image_1_text_to_image ---")
    api_key = os.getenv("GPT_IMAGE_API_KEY")
    if not api_key:
        print("SKIPPED: GPT_IMAGE_API_KEY not found in .env file.")
        return

    try:
        # 1. Create the tool instance using its factory function
        gpt_tool = get_gpt_image_1_text_to_image_tool(api_key=api_key)
        print("Tool created successfully.")

        # 2. Define the input for the tool
        tool_input = {"prompt": "a photorealistic image of a futuristic library on Mars"}
        print(f"Invoking with input: {tool_input}")

        # 3. Invoke the tool
        result = await gpt_tool.ainvoke(tool_input)

        # 4. Print the result
        print("Result:")
        pprint.pprint(result)
    except Exception as e:
        print(f"An error occurred during the test: {e}")


async def main():
    """Runs all the tool unit tests."""
    print("--- Running Unit Tests for Visual Tools ---")
    await test_jmeng_text_to_image()
    await test_gpt_text_to_image()
    # We will add tests for editing tools here later
    print("\n--- All Tests Finished ---")


if __name__ == "__main__":
    # To see detailed logs from the tools, you can set the logging level
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the async main function
    asyncio.run(main()) 