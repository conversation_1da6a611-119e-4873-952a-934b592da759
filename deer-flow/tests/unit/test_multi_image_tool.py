# test_multi_image_tool.py
import os
import sys
import asyncio
from dotenv import load_dotenv

# 将项目根目录添加到 Python 路径中
# 这样我们就可以从 'src' 目录导入模块
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 从 .env 文件加载环境变量
load_dotenv()

# 现在我们可以安全地导入我们的模块
from src.tools.image.multi_image_edit import _run_multi_image_edit

def run_test():
    """
    运行多图编辑工具的测试。
    """
    print("🚀 开始测试 multi_image_edit 工具...")
    print("该测试将下载样本图片，本地合成，上传至腾讯云COS，然后调用图像编辑API，最后返回COS URL。")

    # 1. --- 配置 ---
    # 使用 Pexels 的公开图片进行测试
    test_image_urls = [
        "https://images.pexels.com/photos/842711/pexels-photo-842711.jpeg", # 风景
        "https://images.pexels.com/photos/1770809/pexels-photo-1770809.jpeg", # 鹦鹉
        "https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg"  # 沙滩
    ]
    
    test_prompt = "A surrealist painting of a parrot from image #2 sitting on a branch in the landscape of image #1, with the color palette of the beach in image #3."
    
    # --- 测试用例 1: 网格布局 (Grid Layout) ---
    print("\n--- 正在测试 'grid' 布局 ---")
    grid_result_url = _run_multi_image_edit(
        input_images=test_image_urls,
        prompt=test_prompt,
        layout="grid"
    )
    print(f"✅ 测试结果 ('grid' 布局): {grid_result_url}")
    
    # --- 测试用例 2: 带主图的网格布局 (Grid with Main Image) ---
    print("\n--- 正在测试 'grid_with_main' 布局 ---")
    main_image_result_url = _run_multi_image_edit(
        input_images=test_image_urls,
        prompt="Make the main subject (image #1, the landscape) look like a fantasy world, using the vibrant colors of the parrot in image #2.",
        layout="grid_with_main",
        main_image_index=1 # 将风景图作为主图
    )
    print(f"✅ 测试结果 ('grid_with_main' 布局): {main_image_result_url}")


    # --- 检查常见错误 ---
    if isinstance(grid_result_url, str) and "错误" in grid_result_url or \
       isinstance(main_image_result_url, str) and "错误" in main_image_result_url:
        print("\n❌ 一个或多个测试失败。请检查上面的错误信息。")
        print("常见问题排查点:")
        print("1. 您是否在项目根目录的 .env 文件中正确设置了所有 TENCENT_... 和 FLUX_... 环境变量？")
        print("2. 您的网络连接是否稳定，可以访问外网API？")
        print("3. 您的腾讯云COS存储桶是否设置了公共读权限？")
    else:
        print("\n🎉 所有测试已执行完毕。请检查上面返回的 URL，在浏览器中打开它们，确认图片是否已成功生成和存储。")


if __name__ == "__main__":
    # 确保所有必需的环境变量都已设置
    required_vars = [
        "TENCENT_SECRET_ID", 
        "TENCENT_SECRET_KEY", 
        "COS_REGION", 
        "COS_BUCKET", 
        "FLUX_API_KEY", 
        "FLUX_BASE_URL"
    ]
    
    # 从 .env 文件加载变量以进行检查
    load_dotenv()
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"🛑 错误: 缺少必要的环境变量: {', '.join(missing_vars)}")
        print("请在运行测试前，在项目根目录创建或更新您的 .env 文件。")
    else:
        run_test() 