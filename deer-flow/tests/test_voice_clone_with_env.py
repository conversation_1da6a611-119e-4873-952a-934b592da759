#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语音克隆工具真实测试（手动加载.env）
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parents[1]
sys.path.insert(0, str(project_root))

def load_env_file():
    """手动加载.env文件"""
    env_path = project_root / ".env"
    if not env_path.exists():
        print(f"❌ .env文件不存在: {env_path}")
        return False
    
    print(f"📁 加载.env文件: {env_path}")
    
    with open(env_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")  # 移除引号
                
                if key in ['MINIMAX_API_KEY', 'MINIMAX_GROUP_ID']:
                    os.environ[key] = value
                    print(f"✅ 设置环境变量: {key} = {value[:10]}...")
    
    return True


def test_configuration_after_env():
    """加载.env后测试配置"""
    print("\n🔧 测试配置加载...")
    
    try:
        from src.config.configuration import Configuration
        config = Configuration.from_runnable_config()
        
        print(f"minimax_api_key: {'已配置' if config.minimax_api_key else '❌ 未配置'}")
        print(f"minimax_group_id: {'已配置' if config.minimax_group_id else '❌ 未配置'}")
        
        if config.minimax_api_key:
            print(f"API Key 前缀: {config.minimax_api_key[:20]}...")
        if config.minimax_group_id:
            print(f"Group ID: {config.minimax_group_id}")
        
        return bool(config.minimax_api_key and config.minimax_group_id)
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_tool_creation():
    """测试工具创建"""
    print("\n🛠️ 测试工具创建...")
    
    try:
        from src.config.configuration import Configuration
        from src.tools.audio.voice_clone import get_voice_clone_tool

        config = Configuration.from_runnable_config()
        tool = get_voice_clone_tool(config)
        
        if tool:
            print(f"✅ 工具创建成功: {tool.name}")
            print(f"📝 工具描述: {tool.description[:100]}...")
            return tool
        else:
            print("❌ 工具创建失败")
            return None
            
    except Exception as e:
        print(f"❌ 工具创建异常: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_parameter_validation():
    """测试参数验证"""
    print("\n📋 测试参数验证...")
    
    try:
        from src.tools.audio.voice_clone import VoiceCloneInput
        
        # 测试URL参数
        test_params = {
            "audio_file_path": "https://example.com/test_audio.mp3",
            "voice_id": "TestVoice2024",
            "test_text": "这是一个语音克隆测试",
            "enable_noise_reduction": True,
            "enable_volume_normalization": True
        }
        
        input_obj = VoiceCloneInput(**test_params)
        print(f"✅ 参数验证通过:")
        print(f"   - 音频URL: {input_obj.audio_file_path}")
        print(f"   - 音色ID: {input_obj.voice_id}")
        print(f"   - 试听文本: {input_obj.test_text}")
        print(f"   - 模型: {input_obj.model}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数验证失败: {e}")
        return False


def test_integration():
    """测试集成"""
    print("\n🔗 测试工具集成...")
    
    try:
        # 检查工具是否在主模块中
        from src.tools import voice_clone_tool
        
        if voice_clone_tool:
            print(f"✅ 工具已集成: {voice_clone_tool.name}")
            return True
        else:
            print("❌ 工具未在主模块中找到")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def test_real_api_simple():
    """简单的真实API测试"""
    print("\n🚀 简单真实API测试...")
    print("⚠️ 注意：这将消耗实际的API配额")
    
    # 询问用户是否继续
    response = input("是否继续进行真实API测试？(y/N): ").strip().lower()
    if response != 'y':
        print("⏭️ 跳过真实API测试")
        return True
    
    # 询问音频URL
    print("\n请提供一个测试音频URL（建议使用10-30秒的清晰人声）:")
    audio_url = input("音频URL: ").strip()
    
    if not audio_url:
        print("❌ 需要提供音频URL")
        return False
    
    try:
        tool = test_tool_creation()
        if not tool:
            return False
        
        import time
        voice_id = f"TestVoice_{int(time.time())}"
        
        test_params = {
            "audio_file_path": audio_url,
            "voice_id": voice_id,
            "test_text": "这是一个真实的语音克隆测试。",
            "enable_noise_reduction": True,
            "enable_volume_normalization": True
        }
        
        print(f"\n🧪 开始API调用...")
        print(f"   - 音频URL: {audio_url}")
        print(f"   - 音色ID: {voice_id}")
        print(f"   - 试听文本: {test_params['test_text']}")
        
        print("\n⏳ 正在调用MiniMax API...")
        result = tool.func(**test_params)
        
        print(f"\n📊 API调用结果:")
        if isinstance(result, dict):
            if result.get("success"):
                print("🎉 API调用成功!")
                print(f"   - 音色ID: {result.get('voice_id')}")
                print(f"   - 消息: {result.get('message', '')}")
                if result.get('demo_audio_url'):
                    print(f"   - 试听音频: {result.get('demo_audio_url')}")
                
                # 显示使用信息
                usage_info = result.get('usage_info', {})
                if usage_info:
                    print(f"   - 有效期: {usage_info.get('valid_period', 'N/A')}")
                    print(f"   - 使用说明: {usage_info.get('usage_note', 'N/A')}")
                
                return True
            else:
                print("❌ API调用失败:")
                print(f"   - 错误: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ 意外的返回格式: {type(result)}")
            print(f"   - 内容: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 真实API测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🎭 语音克隆工具完整测试（含真实API）")
    print("=" * 60)
    
    # 首先加载.env文件
    if not load_env_file():
        return False
    
    tests = [
        ("配置加载", test_configuration_after_env),
        ("工具创建", lambda: test_tool_creation() is not None),
        ("参数验证", test_parameter_validation),
        ("工具集成", test_integration),
        ("真实API调用", test_real_api_simple),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if not result and test_name == "配置加载":
                print("⚠️ 配置加载失败，跳过后续测试")
                break
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！语音克隆工具完全就绪。")
        print("\n📝 确认项目:")
        print("- ✅ 环境变量正确加载")
        print("- ✅ 配置正确读取")
        print("- ✅ 工具创建成功")
        print("- ✅ 参数验证正确")
        print("- ✅ 系统集成完成")
        print("- ✅ 真实API功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
