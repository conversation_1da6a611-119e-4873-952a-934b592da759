# 🧪 DeerFlow 测试套件

这里包含了 DeerFlow 系统的所有测试代码，按功能和类型进行分类。

## 📁 目录结构

```
tests/
├── unit/                   # 单元测试
│   ├── test_models.py     # 数据模型测试
│   ├── test_tools.py      # 工具函数测试
│   └── test_utils.py      # 工具类测试
│
├── integration/           # 集成测试
│   ├── test_*.py         # 各种集成测试
│   └── workflows/        # 工作流测试
│
├── performance/          # 性能测试
│   ├── test_response_time.py
│   └── test_memory_usage.py
│
├── fixtures/             # 测试数据和夹具
│   ├── sample_data/
│   └── mock_responses/
│
└── debug/               # 调试脚本
    ├── debug_*.py       # 各种调试工具
    └── troubleshooting/
```

## 🚀 运行测试

### 运行所有测试
```bash
# 从项目根目录运行
python -m pytest tests/

# 使用 Makefile
make test
```

### 运行特定类型的测试
```bash
# 单元测试
python -m pytest tests/unit/

# 集成测试
python -m pytest tests/integration/

# 性能测试
python -m pytest tests/performance/
```

### 运行特定测试文件
```bash
# 运行特定文件
python -m pytest tests/integration/test_execution_engine.py

# 运行特定测试函数
python -m pytest tests/unit/test_models.py::test_enhanced_plan
```

## 🔧 调试工具

### 调试脚本
```bash
# Master Agent 调试
python tests/debug/debug_master_agent.py

# 系统问题调试
python tests/debug/debug_current_issues.py

# React Agent 调试
python tests/debug/debug_react.py
```

### 调试模式
```bash
# 启用详细输出
python -m pytest tests/ -v

# 启用调试日志
DEBUG=true python -m pytest tests/

# 停在第一个失败
python -m pytest tests/ -x
```

## 📊 测试覆盖率

```bash
# 生成覆盖率报告
python -m pytest --cov=src tests/ --cov-report=html

# 查看覆盖率
make coverage
```

## 🎯 测试指南

### 编写新测试
1. **单元测试**: 测试单个函数或类
2. **集成测试**: 测试组件间的交互
3. **性能测试**: 测试系统性能指标

### 测试命名规范
- 文件名: `test_<module_name>.py`
- 类名: `Test<ClassName>`
- 方法名: `test_<function_description>`

### 测试数据
- 使用 `fixtures/` 目录存放测试数据
- 避免在测试中硬编码数据
- 使用 pytest fixtures 管理测试状态

## 🐛 故障排除

如果测试失败：
1. 检查环境变量配置
2. 确认依赖包已安装
3. 查看详细错误日志
4. 使用调试脚本诊断问题

## 📝 贡献指南

添加新测试时：
1. 选择合适的测试类型目录
2. 遵循命名规范
3. 添加必要的文档说明
4. 确保测试可以独立运行

---

*最后更新: 2025-01-24*
