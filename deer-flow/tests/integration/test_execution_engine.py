#!/usr/bin/env python3
"""
测试执行引擎功能
验证各种场景下的路由和执行逻辑
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_routing_logic():
    """测试路由逻辑"""
    print("🔀 测试路由逻辑...")
    
    from src.graph_v2.builder import should_continue, should_use_execution_engine
    from src.graph_v2.types import State
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.template_tools import create_plan_from_template
    from langchain_core.messages import AIMessage, HumanMessage
    
    load_builtin_templates()
    
    # 场景1：简单对话 - 不需要执行引擎
    print("   场景1：简单对话")
    state1: State = {
        "messages": [
            HumanMessage(content="你好"),
            AIMessage(content="您好！我是DeerFlow助手。")
        ],
        "plan": None,
        "template_id": None,
        "template_params": None,
        "template_mode": False,
        "execution_mode": "auto",
        "template_context": None
    }
    
    use_engine1 = should_use_execution_engine(state1)
    route1 = should_continue(state1)
    print(f"     需要执行引擎: {use_engine1}")
    print(f"     路由结果: {route1}")
    
    if not use_engine1 and route1 == "__end__":
        print("     ✅ 正确：简单对话应该直接结束")
    else:
        print("     ❌ 错误：简单对话路由异常")
        return False
    
    # 场景2：单步任务 - 不需要执行引擎
    print("\n   场景2：单步任务")

    # 创建单步计划（模拟）
    single_step_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "测试", "style": "modern", "duration": 10},
        "user_context": "简单测试"
    })

    if not single_step_result['success']:
        print(f"     ❌ 创建测试计划失败: {single_step_result.get('error')}")
        return False

    single_step_plan = single_step_result['plan']

    # 只保留第一步，模拟单步任务
    single_step_plan.steps = single_step_plan.steps[:1]
    
    state2: State = {
        "messages": [
            HumanMessage(content="画一只猫"),
            AIMessage(content="我来为您画一只猫。")  # 移除tool_calls，简化测试
        ],
        "plan": single_step_plan,
        "template_id": None,
        "template_params": None,
        "template_mode": False,
        "execution_mode": "auto",
        "template_context": None
    }
    
    use_engine2 = should_use_execution_engine(state2)
    route2 = should_continue(state2)
    print(f"     需要执行引擎: {use_engine2}")
    print(f"     路由结果: {route2}")
    
    if not use_engine2 and route2 == "__end__":
        print("     ✅ 正确：单步任务应该结束（AI消息无tool_calls）")
    else:
        print("     ❌ 错误：单步任务路由异常")
        print(f"     实际路由: {route2}, 期望: __end__")
        return False
    
    # 场景3：多步模板计划 - 需要执行引擎
    print("\n   场景3：多步模板计划")

    multi_step_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "哪吒", "style": "modern", "duration": 30},
        "user_context": "做一个哪吒鬼畜视频"
    })

    if not multi_step_result['success']:
        print(f"     ❌ 创建测试计划失败: {multi_step_result.get('error')}")
        return False

    multi_step_plan = multi_step_result['plan']
    
    state3: State = {
        "messages": [
            HumanMessage(content="做一个哪吒鬼畜视频"),
            AIMessage(content="我已经为您创建了执行计划。")  # 移除tool_calls
        ],
        "plan": multi_step_plan,
        "template_id": "ai_parody_video",
        "template_params": {"character": "哪吒"},
        "template_mode": True,
        "execution_mode": "template",
        "template_context": None
    }
    
    use_engine3 = should_use_execution_engine(state3)
    route3 = should_continue(state3)
    print(f"     需要执行引擎: {use_engine3}")
    print(f"     路由结果: {route3}")
    print(f"     计划步骤数: {len(multi_step_plan.steps)}")
    print(f"     是否模板计划: {multi_step_plan.is_from_template}")
    
    if use_engine3 and route3 == "execution_engine":
        print("     ✅ 正确：多步模板计划应该使用执行引擎")
    else:
        print("     ❌ 错误：多步模板计划路由异常")
        return False
    
    return True

def test_graph_building():
    """测试图构建"""
    print("\n🏗️  测试图构建...")
    
    try:
        from src.graph_v2.builder import GraphBuilder
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        
        # 创建图构建器
        builder = GraphBuilder()
        
        # 创建检查点
        checkpointer = AsyncSqliteSaver.from_conn_string(":memory:")
        
        # 构建图
        graph = builder.build(checkpointer)
        
        print("   ✅ 图构建成功")
        print(f"   节点列表: {list(graph.nodes.keys())}")
        
        # 验证节点
        expected_nodes = {"master_agent", "execution_engine"}
        actual_nodes = set(graph.nodes.keys())
        
        if expected_nodes.issubset(actual_nodes):
            print("   ✅ 所有必需节点都存在")
        else:
            missing = expected_nodes - actual_nodes
            print(f"   ❌ 缺少节点: {missing}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 图构建失败: {e}")
        return False

def test_execution_engine_logic():
    """测试执行引擎逻辑（不实际运行）"""
    print("\n⚙️  测试执行引擎逻辑...")
    
    try:
        from src.graph_v2.execution_engine import should_use_execution_engine
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        from src.graph_v2.types import State
        
        load_builtin_templates()
        
        # 创建测试计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "悟空", "style": "cartoon", "duration": 45},
            "user_context": "测试执行引擎"
        })
        
        plan = plan_result['plan']
        
        # 测试不同状态下的判断
        test_cases = [
            ("无计划", None, False),
            ("单步计划", plan.steps[:1], False),
            ("多步模板计划", plan.steps, True),
        ]
        
        for case_name, test_steps, expected in test_cases:
            if test_steps is None:
                test_plan = None
            else:
                test_plan = plan
                test_plan.steps = test_steps
            
            state: State = {
                "messages": [],
                "plan": test_plan,
                "template_id": None,
                "template_params": None,
                "template_mode": False,
                "execution_mode": "auto",
                "template_context": None
            }
            
            result = should_use_execution_engine(state)
            print(f"   {case_name}: {result} (期望: {expected})")
            
            if result != expected:
                print(f"   ❌ {case_name} 判断错误")
                return False
        
        print("   ✅ 执行引擎逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 执行引擎逻辑测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🦌 DeerFlow 执行引擎测试")
    print("=" * 50)
    
    tests = [
        ("路由逻辑", test_routing_logic),
        ("图构建", test_graph_building),
        ("执行引擎逻辑", test_execution_engine_logic)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试崩溃: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 50)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 执行引擎基础功能测试通过！")
        print(f"\n📋 验证的功能:")
        print(f"✅ 智能路由：简单对话、单步任务、多步计划")
        print(f"✅ 图构建：master_agent + execution_engine")
        print(f"✅ 执行引擎逻辑：正确判断是否需要使用")
        
        print(f"\n🚀 下一步：在interactive_chat.py中测试真实场景")
        print(f"   建议测试:")
        print(f"   • '你好' → 应该正常对话")
        print(f"   • '画一只猫' → 应该单步执行")
        print(f"   • '做一个哪吒鬼畜视频' → 应该自动执行所有步骤")
        
        return True
    else:
        print(f"\n❌ 发现问题，需要修复后再进行真实测试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
