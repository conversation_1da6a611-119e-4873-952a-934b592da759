#!/usr/bin/env python3
"""
测试interactive_chat.py是否准备好进行真实测试
验证所有组件是否正确集成
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_interactive_imports():
    """测试interactive_chat.py的导入是否正常"""
    print("🔍 测试interactive_chat.py导入...")
    
    try:
        # 测试模板系统初始化
        from src.templates.builtin_templates import load_builtin_templates
        load_builtin_templates()
        print("   ✅ 模板系统初始化成功")
        
        # 测试图构建器
        from src.graph_v2.builder import GraphBuilder
        builder = GraphBuilder()
        print("   ✅ 图构建器导入成功")
        
        # 测试可视化器
        from rich.console import Console
        console = Console()
        print("   ✅ Rich控制台导入成功")
        
        # 测试异步SQLite保存器
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        print("   ✅ 异步SQLite保存器导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def test_graph_building():
    """测试图构建是否正常"""
    print("\n🔧 测试图构建...")
    
    try:
        from src.graph_v2.builder import GraphBuilder
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        
        # 创建内存检查点
        checkpointer = AsyncSqliteSaver.from_conn_string(":memory:")
        
        # 构建图
        builder = GraphBuilder()
        graph = builder.build(checkpointer)
        
        print("   ✅ 图构建成功")
        print(f"   图节点: {list(graph.nodes.keys())}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 图构建失败: {e}")
        return False

def test_template_integration():
    """测试模板系统集成"""
    print("\n🎯 测试模板系统集成...")
    
    try:
        from src.tools.template_tools import get_template_registry, recommend_template
        
        # 检查模板注册
        registry = get_template_registry()
        if len(registry) == 0:
            print("   ❌ 模板库为空")
            return False
        
        print(f"   ✅ 模板库包含 {len(registry)} 个模板")
        print(f"   模板列表: {list(registry.keys())}")
        
        # 测试模板推荐
        result = recommend_template.invoke({"user_input": "做一个哪吒鬼畜视频"})
        if result.get('recommended_template'):
            print(f"   ✅ 模板推荐正常: {result['recommended_template']} (置信度: {result['confidence']})")
        else:
            print("   ❌ 模板推荐失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模板系统测试失败: {e}")
        return False

def test_master_agent_tools():
    """测试Master Agent工具集成"""
    print("\n🤖 测试Master Agent工具集成...")
    
    try:
        # 测试状态管理工具
        from src.tools.state_management import get_current_plan, get_next_pending_step
        print("   ✅ 状态管理工具导入成功")
        
        # 测试模板工具
        from src.tools.template_tools import all_template_tools
        print(f"   ✅ 模板工具导入成功: {len(all_template_tools)} 个工具")
        
        # 测试专家工具
        from src.tools.experts import get_visual_expert_tool, get_audio_expert_tool, get_video_expert_tool
        print("   ✅ 专家工具导入成功")
        
        # 测试规划工具
        from src.tools.planning import planner_tool, reviser_tool
        print("   ✅ 规划工具导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Master Agent工具测试失败: {e}")
        return False

def test_enhanced_models():
    """测试增强模型"""
    print("\n📊 测试增强模型...")
    
    try:
        from src.graph_v2.enhanced_models import EnhancedPlan, EnhancedStep, StepType
        
        # 创建测试步骤
        step = EnhancedStep(
            step_id="test_step",
            name="测试步骤",
            description="用于测试的步骤",
            tool_to_use="visual_expert",
            step_type=StepType.CONTENT_CREATION
        )
        
        print(f"   ✅ EnhancedStep创建成功: {step.get_user_friendly_status()}")
        
        # 测试错误处理
        step.add_error("测试错误", "network_error")
        step.status = "failed"
        
        if step.should_retry_on_failure():
            print(f"   ✅ 智能重试逻辑正常: {step.get_user_friendly_status()}")
        else:
            print("   ❌ 重试逻辑异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 增强模型测试失败: {e}")
        return False

def test_visualization_ready():
    """测试可视化组件准备情况"""
    print("\n🎨 测试可视化组件...")
    
    try:
        from rich.console import Console
        from rich.panel import Panel
        from rich.table import Table
        
        console = Console()
        
        # 测试基本显示
        console.print("[dim]测试Rich显示组件...[/dim]")
        
        # 测试表格
        table = Table(title="测试表格")
        table.add_column("列1")
        table.add_column("列2")
        table.add_row("数据1", "数据2")
        
        print("   ✅ Rich组件正常工作")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 可视化组件测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🦌 DeerFlow Interactive Chat 准备情况检查")
    print("=" * 60)
    
    tests = [
        ("导入检查", test_interactive_imports),
        ("图构建", test_graph_building),
        ("模板系统", test_template_integration),
        ("Master Agent工具", test_master_agent_tools),
        ("增强模型", test_enhanced_models),
        ("可视化组件", test_visualization_ready)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试崩溃: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 60)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 准备情况检查: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 Interactive Chat 已准备就绪！")
        print(f"\n📋 可以进行的真实测试:")
        print(f"✅ 模板推荐和使用")
        print(f"✅ 智能计划生成")
        print(f"✅ 串行步骤执行")
        print(f"✅ 参数解析和传递")
        print(f"✅ 错误处理和重试")
        print(f"✅ 用户友好的进度显示")
        
        print(f"\n🚀 建议测试命令:")
        print(f"   python interactive_chat.py")
        print(f"\n💡 建议测试输入:")
        print(f"   • '做一个哪吒鬼畜视频'")
        print(f"   • '创建一个产品宣传视频'")
        print(f"   • '画北京、上海、成都三张海报'")
        
        return True
    else:
        print(f"\n❌ 发现问题，需要修复后再进行真实测试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
