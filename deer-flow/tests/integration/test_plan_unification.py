#!/usr/bin/env python3
"""
测试Plan模型统一修复
验证所有组件都正确使用EnhancedPlan
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_plan_converter():
    """测试Plan转换器"""
    print("🔄 测试Plan转换器...")
    
    try:
        from src.graph_v2.models import Plan, Step
        from src.graph_v2.enhanced_models import EnhancedPlan
        from src.graph_v2.plan_converter import (
            convert_legacy_plan_to_enhanced,
            ensure_enhanced_plan,
            validate_enhanced_plan
        )
        
        # 创建Legacy Plan
        legacy_plan = Plan(
            original_task="测试任务",
            steps=[
                Step(
                    step_id=1,
                    description="第一步",
                    tool_to_use="visual_expert",
                    inputs={"prompt": "画一只猫"},
                    dependencies=[]
                ),
                Step(
                    step_id=2,
                    description="第二步",
                    tool_to_use="audio_expert",
                    inputs={"text": "猫叫声"},
                    dependencies=[1]
                )
            ]
        )
        
        print(f"   Legacy Plan: {len(legacy_plan.steps)} 步骤")
        
        # 转换为Enhanced Plan
        enhanced_plan = convert_legacy_plan_to_enhanced(legacy_plan)
        print(f"   Enhanced Plan: {len(enhanced_plan.steps)} 步骤")
        print(f"   步骤ID: {[s.step_id for s in enhanced_plan.steps]}")
        print(f"   依赖关系: {[s.dependencies for s in enhanced_plan.steps]}")
        
        # 验证转换结果
        issues = validate_enhanced_plan(enhanced_plan)
        if issues:
            print(f"   ❌ 验证失败: {issues}")
            return False
        else:
            print(f"   ✅ 转换和验证成功")
        
        # 测试ensure_enhanced_plan
        result1 = ensure_enhanced_plan(legacy_plan)
        result2 = ensure_enhanced_plan(enhanced_plan)
        
        if isinstance(result1, EnhancedPlan) and isinstance(result2, EnhancedPlan):
            print(f"   ✅ ensure_enhanced_plan 工作正常")
        else:
            print(f"   ❌ ensure_enhanced_plan 失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Plan转换器测试失败: {e}")
        return False

def test_state_management_tools():
    """测试状态管理工具"""
    print("\n🛠️  测试状态管理工具...")
    
    try:
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        from src.tools.state_management import (
            get_current_plan, 
            get_next_pending_step, 
            update_step_status,
            resolve_step_inputs
        )
        from src.graph_v2.types import State
        
        load_builtin_templates()
        
        # 创建测试计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "测试", "style": "modern", "duration": 30},
            "user_context": "测试状态管理工具"
        })
        
        if not plan_result['success']:
            print(f"   ❌ 创建测试计划失败: {plan_result.get('error')}")
            return False
        
        plan = plan_result['plan']
        
        # 创建测试状态
        test_state: State = {
            "messages": [],
            "plan": plan,
            "template_id": "ai_parody_video",
            "template_params": {},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None
        }
        
        # 测试get_current_plan
        print("   测试 get_current_plan...")
        current_plan_result = get_current_plan.invoke({"state": test_state})
        if "error" in current_plan_result.lower():
            print(f"   ❌ get_current_plan 失败: {current_plan_result}")
            return False
        else:
            print(f"   ✅ get_current_plan 成功")
        
        # 测试get_next_pending_step
        print("   测试 get_next_pending_step...")
        next_step_result = get_next_pending_step.invoke({"state": test_state})
        if "error" in next_step_result.lower():
            print(f"   ❌ get_next_pending_step 失败: {next_step_result}")
            return False
        else:
            print(f"   ✅ get_next_pending_step 成功")
            
            # 解析步骤信息
            import json
            try:
                step_info = json.loads(next_step_result)
                step_id = step_info['step_id']
                print(f"   下一步ID: {step_id}")
                
                # 测试resolve_step_inputs
                print("   测试 resolve_step_inputs...")
                resolve_result = resolve_step_inputs.invoke({
                    "state": test_state,
                    "step_id": step_id
                })
                
                if resolve_result.get('success'):
                    print(f"   ✅ resolve_step_inputs 成功")
                else:
                    print(f"   ⚠️  resolve_step_inputs 有问题: {resolve_result.get('error')}")
                
                # 测试update_step_status
                print("   测试 update_step_status...")
                mock_result = {
                    "success": True,
                    "content": "测试完成",
                    "assets": {"images": ["test.jpg"]},
                    "metadata": {"test": True}
                }
                
                update_result = update_step_status.invoke({
                    "state": test_state,
                    "step_id": step_id,
                    "status": "completed",
                    "result": mock_result
                })
                
                if update_result.get("error"):
                    print(f"   ❌ update_step_status 失败: {update_result.get('error')}")
                    return False
                elif "plan" in update_result:
                    print(f"   ✅ update_step_status 成功")
                else:
                    print(f"   ❌ update_step_status 返回格式异常: {update_result}")
                    return False
                
            except json.JSONDecodeError:
                print(f"   ⚠️  无法解析步骤信息，但工具调用成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 状态管理工具测试失败: {e}")
        return False

def test_execution_engine():
    """测试执行引擎"""
    print("\n⚙️  测试执行引擎...")
    
    try:
        from src.graph_v2.execution_engine import should_use_execution_engine
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        from src.graph_v2.types import State
        
        load_builtin_templates()
        
        # 测试多步计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "悟空", "style": "cartoon", "duration": 45},
            "user_context": "测试执行引擎"
        })
        
        plan = plan_result['plan']
        
        state: State = {
            "messages": [],
            "plan": plan,
            "template_id": "ai_parody_video",
            "template_params": {},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None
        }
        
        # 测试should_use_execution_engine
        should_use = should_use_execution_engine(state)
        print(f"   多步模板计划应该使用执行引擎: {should_use}")
        
        if not should_use:
            print(f"   ❌ 多步模板计划应该使用执行引擎")
            return False
        
        # 测试无计划情况
        empty_state: State = {
            "messages": [],
            "plan": None,
            "template_id": None,
            "template_params": None,
            "template_mode": False,
            "execution_mode": "auto",
            "template_context": None
        }
        
        should_use_empty = should_use_execution_engine(empty_state)
        print(f"   无计划应该使用执行引擎: {should_use_empty}")
        
        if should_use_empty:
            print(f"   ❌ 无计划不应该使用执行引擎")
            return False
        
        print(f"   ✅ 执行引擎判断逻辑正确")
        return True
        
    except Exception as e:
        print(f"   ❌ 执行引擎测试失败: {e}")
        return False

def test_graph_building():
    """测试图构建"""
    print("\n🏗️  测试图构建...")
    
    try:
        from src.graph_v2.builder import GraphBuilder
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        
        # 创建图构建器
        builder = GraphBuilder()
        
        # 创建检查点
        checkpointer = AsyncSqliteSaver.from_conn_string(":memory:")
        
        # 构建图
        graph = builder.build(checkpointer)
        
        print("   ✅ 图构建成功")
        print(f"   节点列表: {list(graph.nodes.keys())}")
        
        # 验证节点
        expected_nodes = {"master_agent", "execution_engine"}
        actual_nodes = set(graph.nodes.keys())
        
        if expected_nodes.issubset(actual_nodes):
            print("   ✅ 所有必需节点都存在")
            return True
        else:
            missing = expected_nodes - actual_nodes
            print(f"   ❌ 缺少节点: {missing}")
            return False
        
    except Exception as e:
        print(f"   ❌ 图构建测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🦌 DeerFlow Plan模型统一测试")
    print("=" * 50)
    
    tests = [
        ("Plan转换器", test_plan_converter),
        ("状态管理工具", test_state_management_tools),
        ("执行引擎", test_execution_engine),
        ("图构建", test_graph_building)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试崩溃: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 50)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 Plan模型统一修复成功！")
        print(f"\n📋 修复成果:")
        print(f"✅ State类型统一：只接受EnhancedPlan")
        print(f"✅ 状态管理工具：支持EnhancedPlan + 自动转换")
        print(f"✅ 执行引擎：统一使用EnhancedPlan")
        print(f"✅ 图构建：正常工作")
        print(f"✅ 向后兼容：Legacy Plan自动转换")
        
        print(f"\n🚀 现在可以进行真实测试了！")
        return True
    else:
        print(f"\n❌ 发现问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
