#!/usr/bin/env python3
"""
Test script for asset management focused output.
"""

import asyncio
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.syntax import Syntax
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
from src.graph_v2.builder import GraphBuilder

console = Console()

async def test_asset_management():
    """测试资产管理功能."""
    console.print(Panel.fit(
        "[bold blue]🗂️ 资产管理测试[/bold blue]\n"
        "[cyan]测试子Agent是否能正确命名和描述生成的资产[/cyan]",
        title="测试开始",
        border_style="blue"
    ))
    
    os.makedirs("db", exist_ok=True)
    
    async with AsyncSqliteSaver.from_conn_string("db/test_assets.sqlite") as memory:
        builder = GraphBuilder()
        graph = builder.build(checkpointer=memory)
        config = {"configurable": {"thread_id": "asset-test"}}
        
        # 测试用例
        test_cases = [
            {
                "name": "简单图片生成",
                "input": "画一只在花园里玩耍的小狗",
                "expected_assets": ["images"]
            },
            {
                "name": "风格化图片",
                "input": "创建一张日式动漫风格的樱花飞舞场景",
                "expected_assets": ["images"]
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            console.print(f"\n[bold yellow]测试 {i}: {test_case['name']}[/bold yellow]")
            console.print(f"输入: {test_case['input']}")
            
            inputs = {"messages": [("human", test_case['input'])]}
            
            try:
                final_assets = None
                step_count = 0
                
                async for event in graph.astream(inputs, config=config, stream_mode="updates"):
                    step_count += 1
                    node_name, node_output = next(iter(event.items()))
                    
                    console.print(f"  📊 步骤 {step_count}: {node_name}")
                    
                    # 检查消息
                    messages = node_output.get("messages", [])
                    if messages:
                        last_message = messages[-1]
                        
                        # 检查工具输出（资产信息）
                        if hasattr(last_message, 'name') and last_message.name:
                            console.print(f"    📤 工具输出: {last_message.name}")
                            
                            # 解析工具输出
                            try:
                                import json
                                tool_output = json.loads(last_message.content)
                                
                                if tool_output.get('success'):
                                    console.print(f"    ✅ 任务成功完成")
                                    
                                    # 显示资产信息
                                    assets = tool_output.get('assets', {})
                                    if assets:
                                        console.print(f"    📁 生成的资产:")
                                        
                                        # 创建资产表格
                                        table = Table(title="资产详情", show_header=True, header_style="bold magenta")
                                        table.add_column("类型", style="cyan", width=8)
                                        table.add_column("名称", style="green", width=20)
                                        table.add_column("描述", style="white", width=30)
                                        table.add_column("URL", style="blue", width=20)
                                        
                                        for asset_type, asset_list in assets.items():
                                            if asset_type.startswith('primary_'):
                                                continue  # 跳过primary_字段
                                            
                                            if isinstance(asset_list, list):
                                                for asset in asset_list:
                                                    if isinstance(asset, dict):
                                                        table.add_row(
                                                            asset_type,
                                                            asset.get('name', '未命名'),
                                                            asset.get('description', '无描述')[:30] + "...",
                                                            asset.get('url', '')[-20:] + "..."
                                                        )
                                                    else:
                                                        table.add_row(asset_type, "简单URL", "无详细信息", str(asset)[-20:] + "...")
                                        
                                        console.print(table)
                                        final_assets = assets
                                    else:
                                        console.print(f"    ⚠️  没有生成资产")
                                else:
                                    console.print(f"    ❌ 任务失败: {tool_output.get('error', '未知错误')}")
                                    
                            except json.JSONDecodeError:
                                console.print(f"    ⚠️  非JSON格式输出")
                                # 显示原始内容的一部分
                                content_preview = last_message.content[:100] + "..." if len(last_message.content) > 100 else last_message.content
                                console.print(f"    📝 内容预览: {content_preview}")
                    
                    # 限制步骤数
                    if step_count >= 3:
                        console.print("    ⚠️  限制在3个步骤")
                        break
                
                # 分析资产管理质量
                if final_assets:
                    console.print(f"\n  📊 资产管理分析:")
                    
                    total_assets = 0
                    named_assets = 0
                    described_assets = 0
                    
                    for asset_type, asset_list in final_assets.items():
                        if asset_type.startswith('primary_'):
                            continue
                        
                        if isinstance(asset_list, list):
                            for asset in asset_list:
                                if isinstance(asset, dict):
                                    total_assets += 1
                                    if asset.get('name') and asset.get('name') != '未命名':
                                        named_assets += 1
                                    if asset.get('description') and asset.get('description') != '无描述':
                                        described_assets += 1
                    
                    console.print(f"    总资产数: {total_assets}")
                    console.print(f"    已命名资产: {named_assets}/{total_assets}")
                    console.print(f"    已描述资产: {described_assets}/{total_assets}")
                    
                    if total_assets > 0:
                        naming_rate = (named_assets / total_assets) * 100
                        description_rate = (described_assets / total_assets) * 100
                        console.print(f"    命名完成率: {naming_rate:.1f}%")
                        console.print(f"    描述完成率: {description_rate:.1f}%")
                        
                        if naming_rate >= 80 and description_rate >= 80:
                            console.print(f"    🎉 资产管理质量: 优秀")
                        elif naming_rate >= 60 and description_rate >= 60:
                            console.print(f"    👍 资产管理质量: 良好")
                        else:
                            console.print(f"    ⚠️  资产管理质量: 需要改进")
                else:
                    console.print(f"  ❌ 没有获取到资产信息")
                
                console.print(f"  🎉 测试 {i} 完成\n")
                
            except Exception as e:
                console.print(f"  💥 测试 {i} 失败: {e}")
                console.print_exception()

async def test_extraction_function():
    """测试资产提取函数."""
    console.print(Panel.fit(
        "[bold green]🔧 资产提取函数测试[/bold green]",
        title="提取测试",
        border_style="green"
    ))
    
    try:
        from src.tools.experts import _extract_structured_from_natural_output
        
        # 测试用例
        test_outputs = [
            {
                "name": "标准格式输出",
                "output": """我成功为您创作了一张可爱的小狗图片！

**生成的作品**：
- **文件名**：花园里的快乐小狗
- **描述**：一只金毛犬在花园里开心地玩耍，阳光明媚，色彩鲜艳
- **文件**：https://example.com/happy_dog.jpg

这张图片采用了温暖的色调，突出了小狗的活泼可爱。""",
                "expected_name": "花园里的快乐小狗",
                "expected_desc": "一只金毛犬在花园里开心地玩耍，阳光明媚，色彩鲜艳"
            },
            {
                "name": "简单格式输出",
                "output": """好的，这是为您创作的樱花场景。

![樱花飞舞](https://example.com/sakura.jpg)

画面中樱花花瓣在风中飞舞，营造出浪漫的日式动漫氛围。""",
                "expected_name": "樱花飞舞",
                "expected_desc": "画面中樱花花瓣在风中飞舞，营造出浪漫的日式动漫氛围"
            }
        ]
        
        for i, test in enumerate(test_outputs, 1):
            console.print(f"\n[bold cyan]提取测试 {i}: {test['name']}[/bold cyan]")
            
            try:
                result = _extract_structured_from_natural_output(test['output'], "visual")
                
                console.print(f"  ✅ 提取成功")
                console.print(f"  成功状态: {result.get('success')}")
                
                # 检查资产信息
                assets = result.get('assets', {})
                if 'images' in assets and assets['images']:
                    image_asset = assets['images'][0]
                    extracted_name = image_asset.get('name', '')
                    extracted_desc = image_asset.get('description', '')
                    
                    console.print(f"  提取的名称: '{extracted_name}'")
                    console.print(f"  提取的描述: '{extracted_desc}'")
                    
                    # 简单的匹配检查
                    name_match = test['expected_name'] in extracted_name or extracted_name in test['expected_name']
                    desc_match = len(extracted_desc) > 10  # 至少有一定长度的描述
                    
                    console.print(f"  名称匹配: {'✅' if name_match else '❌'}")
                    console.print(f"  描述质量: {'✅' if desc_match else '❌'}")
                else:
                    console.print(f"  ❌ 没有提取到图片资产")
                
            except Exception as e:
                console.print(f"  ❌ 提取失败: {e}")
        
        console.print(f"\n[green]✅ 资产提取测试完成[/green]")
        
    except Exception as e:
        console.print(f"[red]❌ 提取测试失败: {e}[/red]")
        console.print_exception()

async def main():
    """主函数."""
    try:
        await test_extraction_function()
        await test_asset_management()
        
        console.print(Panel.fit(
            "[bold green]🎉 资产管理测试完成[/bold green]\n"
            "[cyan]子Agent现在专注于资产的命名和描述[/cyan]",
            title="测试完成",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel.fit(
            f"[bold red]❌ 测试失败[/bold red]\n"
            f"[red]错误: {e}[/red]",
            title="测试失败",
            border_style="red"
        ))
        console.print_exception(show_locals=True)

if __name__ == "__main__":
    asyncio.run(main())
