#!/usr/bin/env python3
"""
测试串行执行优化
模拟完整的用户体验：从用户输入到模板推荐到计划执行
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def simulate_master_agent_workflow():
    """模拟Master Agent的完整工作流程"""
    print("🤖 模拟Master Agent工作流程")
    print("=" * 50)
    
    # 初始化
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.state_management import get_current_plan, get_next_pending_step, update_step_status, resolve_step_inputs
    from src.tools.template_tools import recommend_template, create_plan_from_template
    from src.graph_v2.types import State
    
    load_builtin_templates()
    
    # 模拟用户输入
    user_input = "做一个哪吒鬼畜视频"
    print(f"👤 用户输入: {user_input}")
    
    # 初始状态
    state: State = {
        "messages": [("human", user_input)],
        "plan": None,
        "template_id": None,
        "template_params": None,
        "template_mode": False,
        "execution_mode": "auto",
        "template_context": None
    }
    
    print(f"\n🔄 Master Agent工作流程开始...")
    
    # Step 1: 感知 - 检查当前计划
    print(f"\n1️⃣ 感知阶段：检查当前计划")
    current_plan = get_current_plan.invoke({"state": state})
    print(f"   当前计划状态: {current_plan}")
    
    # Step 2: 决策 - 没有计划，需要创建
    print(f"\n2️⃣ 决策阶段：分析用户需求")
    
    # 2.1 智能推荐模式
    print(f"   2.1 尝试模板推荐...")
    recommendation = recommend_template.invoke({"user_input": user_input})
    print(f"   推荐结果: {recommendation['recommended_template']}")
    print(f"   置信度: {recommendation['confidence']}")
    
    if recommendation['confidence'] > 0.8:
        print(f"   ✅ 高置信度推荐，使用模板: {recommendation['recommended_template']}")
        
        # 2.2 使用推荐的模板创建计划
        print(f"   2.2 从模板创建计划...")
        template_params = {
            "character": "哪吒",
            "style": "modern",
            "duration": 30,
            "effect_intensity": "high",
            "background_music": True
        }
        
        plan_result = create_plan_from_template.invoke({
            "template_id": recommendation['recommended_template'],
            "params": template_params,
            "user_context": user_input
        })
        
        if plan_result['success']:
            state["plan"] = plan_result['plan']
            state["template_id"] = recommendation['recommended_template']
            state["template_params"] = template_params
            state["template_mode"] = True
            print(f"   ✅ 计划创建成功，包含 {len(plan_result['plan'].steps)} 个步骤")
        else:
            print(f"   ❌ 计划创建失败: {plan_result['error']}")
            return False
    else:
        print(f"   ⚠️  置信度较低，应该使用传统规划模式")
        return False
    
    # Step 3: 执行循环
    print(f"\n3️⃣ 执行阶段：串行执行计划")
    
    step_count = 0
    max_steps = 3  # 限制测试步骤数，避免实际API调用
    
    while step_count < max_steps:
        step_count += 1
        print(f"\n   --- 执行循环 {step_count} ---")
        
        # 3.1 获取下一个待执行步骤
        print(f"   3.{step_count}.1 获取下一个待执行步骤...")
        next_step_result = get_next_pending_step.invoke({"state": state})
        
        if "No" in next_step_result or "complete" in next_step_result.lower():
            print(f"   ✅ 计划执行完成或无更多步骤")
            break
        
        # 解析步骤信息
        import json
        try:
            step_info = json.loads(next_step_result)
            step_id = step_info['step_id']
            step_name = step_info['name']
            tool_to_use = step_info['tool_to_use']
            print(f"   下一步: {step_id} - {step_name}")
            print(f"   使用工具: {tool_to_use}")
        except:
            print(f"   ❌ 无法解析步骤信息: {next_step_result}")
            break
        
        # 3.2 解析步骤输入
        print(f"   3.{step_count}.2 解析步骤输入...")
        resolved_inputs = resolve_step_inputs.invoke({
            "state": state,
            "step_id": step_id
        })
        
        if resolved_inputs['success']:
            print(f"   ✅ 参数解析成功: {len(resolved_inputs['resolved_inputs'])} 个参数")
            for key, value in resolved_inputs['resolved_inputs'].items():
                print(f"     • {key}: {str(value)[:50]}...")
        else:
            print(f"   ⚠️  参数解析有问题: {resolved_inputs.get('unresolved_references', [])}")
        
        # 3.3 模拟工具执行（避免实际API调用）
        print(f"   3.{step_count}.3 模拟执行 {tool_to_use}...")
        
        # 模拟成功的工具执行结果
        mock_result = {
            "success": True,
            "content": f"成功完成 {step_name}",
            "assets": {
                "images": [f"{step_id}_result.jpg"],
                "metadata": {"step_id": step_id, "tool": tool_to_use}
            },
            "metadata": {
                "execution_time": 2.5,
                "quality_score": 0.95
            }
        }
        
        print(f"   ✅ 模拟执行成功: {mock_result['content']}")
        
        # 3.4 更新步骤状态
        print(f"   3.{step_count}.4 更新步骤状态...")
        update_result = update_step_status.invoke({
            "state": state,
            "step_id": step_id,
            "status": "completed",
            "result": mock_result
        })
        
        # 更新状态
        state = {**state, **update_result}
        print(f"   ✅ 步骤状态已更新为 completed")
        
        # 显示当前进度
        if hasattr(state["plan"], "get_execution_summary"):
            summary = state["plan"].get_execution_summary()
            print(f"   📊 执行进度: {summary['progress_percentage']:.1f}%")
    
    print(f"\n🎉 串行执行测试完成！")
    return True

def test_execution_edge_cases():
    """测试执行过程中的边界情况"""
    print(f"\n🧪 测试执行边界情况")
    print("=" * 30)
    
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.template_tools import create_plan_from_template
    from src.tools.state_management import get_next_pending_step, update_step_status
    from src.graph_v2.types import State
    
    load_builtin_templates()
    
    # 创建测试计划
    plan_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "测试", "style": "modern", "duration": 30},
        "user_context": "测试边界情况"
    })
    
    state: State = {
        "messages": [],
        "plan": plan_result['plan'],
        "template_id": "ai_parody_video",
        "template_params": {},
        "template_mode": True,
        "execution_mode": "template",
        "template_context": None
    }
    
    # 测试1: 步骤失败处理
    print(f"\n1️⃣ 测试步骤失败处理")
    next_step = get_next_pending_step.invoke({"state": state})
    
    if "step_id" in next_step:
        import json
        step_info = json.loads(next_step)
        step_id = step_info['step_id']
        
        # 模拟失败
        mock_failure = {
            "success": False,
            "content": "模拟的执行失败",
            "error": "网络连接超时",
            "assets": {},
            "metadata": {"error_code": "TIMEOUT"}
        }
        
        update_result = update_step_status.invoke({
            "state": state,
            "step_id": step_id,
            "status": "failed",
            "result": mock_failure
        })
        
        state = {**state, **update_result}
        print(f"   ✅ 失败状态更新成功")
        
        # 检查计划状态
        if hasattr(state["plan"], "get_execution_summary"):
            summary = state["plan"].get_execution_summary()
            print(f"   失败步骤数: {summary['status_counts'].get('failed', 0)}")
    
    # 测试2: 依赖关系验证
    print(f"\n2️⃣ 测试依赖关系")
    plan = state["plan"]
    if hasattr(plan, "steps"):
        for step in plan.steps:
            if step.dependencies:
                print(f"   步骤 {step.step_id} 依赖: {step.dependencies}")
                
                # 验证依赖步骤存在
                for dep in step.dependencies:
                    dep_step = plan.get_step(dep)
                    if dep_step:
                        print(f"     ✅ 依赖 {dep} 存在")
                    else:
                        print(f"     ❌ 依赖 {dep} 不存在")
    
    print(f"   ✅ 依赖关系验证完成")
    
    return True

def main():
    """主测试流程"""
    print("🦌 DeerFlow 串行执行优化测试")
    print("=" * 60)
    
    tests = [
        ("Master Agent工作流程", simulate_master_agent_workflow),
        ("执行边界情况", test_execution_edge_cases)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 60)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 串行执行优化验证成功！")
        print(f"\n📋 验证的功能:")
        print(f"✅ 完整的Master Agent工作流程")
        print(f"✅ 模板推荐和计划创建")
        print(f"✅ 串行步骤执行")
        print(f"✅ 参数解析和传递")
        print(f"✅ 状态更新和进度跟踪")
        print(f"✅ 错误处理和依赖验证")
        
        print(f"\n🚀 系统已准备好为用户提供完整的模板化视频创作体验！")
        return True
    else:
        print(f"\n❌ 发现问题，需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
