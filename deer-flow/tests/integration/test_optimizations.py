#!/usr/bin/env python3
"""
测试立即优化的效果
验证模板引用修复、错误处理增强、用户反馈优化
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_template_reference_fix():
    """测试模板引用修复"""
    print("🔧 测试模板引用修复...")
    
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.template_tools import create_plan_from_template
    from src.graph_v2.parameter_resolver import ParameterResolver
    
    load_builtin_templates()
    
    # 创建计划
    plan_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "哪吒", "style": "modern", "duration": 30},
        "user_context": "测试引用修复"
    })
    
    if not plan_result['success']:
        print(f"   ❌ 计划创建失败: {plan_result['error']}")
        return False
    
    plan = plan_result['plan']
    
    # 模拟第一步完成，使用标准格式
    first_step = plan.steps[0]
    first_step.status = "completed"
    first_step.result = {
        "success": True,
        "content": "素材收集完成",
        "assets": {
            "images": ["nezha_1.jpg", "nezha_2.jpg", "nezha_3.jpg"]
        },
        "metadata": {
            "step_id": first_step.step_id,
            "tool": "visual_expert",
            "count": 3
        }
    }
    plan.execution_context.update_step_output(first_step.step_id, first_step.result)
    
    # 测试第二步的参数解析
    resolver = ParameterResolver(plan)
    second_step = plan.steps[1]
    
    print(f"   第二步: {second_step.step_id}")
    print(f"   原始输入: {second_step.inputs}")
    
    try:
        resolved = resolver.resolve(second_step.inputs)
        print(f"   ✅ 参数解析成功: {len(resolved)} 个参数")
        
        # 检查关键引用是否正确解析
        if "source_materials" in resolved:
            source_materials = resolved["source_materials"]
            if "nezha_1.jpg" in str(source_materials):
                print(f"   ✅ 步骤输出引用正确解析")
            else:
                print(f"   ❌ 步骤输出引用解析错误: {source_materials}")
                return False
        
        # 检查是否还有未解析的引用
        unresolved_count = 0
        for key, value in resolved.items():
            if isinstance(value, str) and ('{{' in value or '${' in value):
                print(f"   ⚠️  未解析的引用: {key} = {value}")
                unresolved_count += 1
        
        if unresolved_count == 0:
            print(f"   ✅ 所有引用都已正确解析")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 参数解析失败: {e}")
        return False

def test_enhanced_error_handling():
    """测试增强的错误处理"""
    print("\n🛡️  测试增强的错误处理...")
    
    from src.graph_v2.enhanced_models import EnhancedStep, RetryConfig, StepType
    
    # 创建带有重试配置的步骤
    retry_config = RetryConfig(
        max_retries=3,
        retry_delay=2,
        exponential_backoff=True,
        retry_on_errors=["timeout", "network_error", "rate_limit"]
    )
    
    step = EnhancedStep(
        step_id="test_step",
        name="测试步骤",
        description="用于测试错误处理",
        tool_to_use="test_tool",
        step_type=StepType.CONTENT_CREATION,
        retry_config=retry_config
    )
    
    print(f"   初始状态: {step.get_user_friendly_status()}")
    
    # 测试不同类型的错误
    error_tests = [
        ("timeout", True, "网络超时"),
        ("validation_error", False, "输入验证错误"),
        ("rate_limit", True, "API限流"),
        ("permission_denied", False, "权限不足")
    ]
    
    for error_type, should_retry, error_msg in error_tests:
        # 重置步骤
        step.status = "failed"
        step.retry_count = 0
        step.add_error(error_msg, error_type)
        
        can_retry = step.should_retry_on_failure()
        retry_delay = step.get_retry_delay_seconds()
        
        print(f"   错误类型: {error_type}")
        print(f"     应该重试: {should_retry}, 实际: {can_retry}")
        print(f"     重试延迟: {retry_delay}秒")
        print(f"     用户状态: {step.get_user_friendly_status()}")
        
        if can_retry != should_retry:
            print(f"   ❌ 重试逻辑错误")
            return False
    
    # 测试重试次数限制
    step.retry_count = 3  # 达到最大重试次数
    step.add_error("又一次超时", "timeout")
    
    if step.should_retry_on_failure():
        print(f"   ❌ 应该停止重试但仍然允许重试")
        return False
    
    print(f"   ✅ 重试次数限制正确: {step.get_user_friendly_status()}")
    
    # 测试指数退避
    delays = []
    for i in range(4):
        delay = retry_config.get_retry_delay(i)
        delays.append(delay)
    
    print(f"   指数退避延迟: {delays}")
    if delays == [2, 4, 8, 16]:  # 2 * 2^i
        print(f"   ✅ 指数退避计算正确")
    else:
        print(f"   ❌ 指数退避计算错误")
        return False
    
    return True

def test_user_friendly_feedback():
    """测试用户友好的反馈"""
    print("\n😊 测试用户友好的反馈...")
    
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.template_tools import create_plan_from_template
    from src.tools.state_management import get_current_plan, get_next_pending_step
    from src.graph_v2.types import State
    
    load_builtin_templates()
    
    # 创建计划
    plan_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "悟空", "style": "cartoon", "duration": 45},
        "user_context": "测试用户反馈"
    })
    
    plan = plan_result['plan']
    state: State = {
        "messages": [],
        "plan": plan,
        "template_id": "ai_parody_video",
        "template_params": {},
        "template_mode": True,
        "execution_mode": "template",
        "template_context": None
    }
    
    # 测试初始状态的用户反馈
    print(f"   初始进度: {plan.execution_context.get_user_friendly_progress()}")
    print(f"   当前活动: {plan.execution_context.get_current_activity()}")
    print(f"   执行摘要: {plan.execution_context.get_execution_summary_text()}")
    
    # 获取当前计划信息
    current_plan_info = get_current_plan.invoke({"state": state})
    import json
    plan_data = json.loads(current_plan_info)
    
    print(f"   ✅ 用户友好进度: {plan_data.get('user_friendly_progress')}")
    print(f"   ✅ 当前活动描述: {plan_data.get('current_activity')}")
    
    # 获取下一步信息
    next_step_info = get_next_pending_step.invoke({"state": state})
    step_data = json.loads(next_step_info)
    
    print(f"   ✅ 下一步友好状态: {step_data.get('user_friendly_status')}")
    
    # 模拟步骤执行进度
    completed_steps = 0
    total_steps = len(plan.steps)
    
    for i in range(min(3, total_steps)):  # 模拟前3步
        step = plan.steps[i]
        step.status = "completed"
        plan.execution_context.completed_steps += 1
        
        progress = plan.execution_context.get_user_friendly_progress()
        print(f"   步骤 {i+1} 完成: {progress}")
    
    # 测试失败步骤的反馈
    if len(plan.steps) > 3:
        failed_step = plan.steps[3]
        failed_step.status = "failed"
        failed_step.add_error("模拟网络错误", "network_error")
        failed_step.retry_count = 1
        
        print(f"   失败步骤状态: {failed_step.get_user_friendly_status()}")
    
    return True

def main():
    """主测试流程"""
    print("🦌 DeerFlow 立即优化测试")
    print("=" * 50)
    
    tests = [
        ("模板引用修复", test_template_reference_fix),
        ("增强错误处理", test_enhanced_error_handling),
        ("用户友好反馈", test_user_friendly_feedback)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 50)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 所有优化都成功实现！")
        print(f"\n📋 优化成果:")
        print(f"✅ 模板引用统一：使用实际的工具输出格式")
        print(f"✅ 智能错误处理：基于错误类型的重试策略")
        print(f"✅ 指数退避重试：避免过度重试")
        print(f"✅ 用户友好反馈：清晰的进度和状态描述")
        print(f"✅ 中文本地化：适合中文用户的状态描述")
        
        print(f"\n🚀 系统用户体验显著提升！")
        return True
    else:
        print(f"\n❌ 部分优化需要进一步调整")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
