#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Phase 1 端到端测试：验证模板系统的核心流程

测试流程：
1. 用户输入 → 模板推荐
2. 模板推荐 → 模板实例化  
3. 模板实例化 → 生成可执行Plan
4. Plan → Master Agent能够理解和执行
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_template_recommendation():
    """测试模板推荐功能"""
    print("🔍 测试1: 模板推荐功能")
    
    try:
        from src.tools.template_tools import recommend_template
        from src.templates.builtin_templates import load_builtin_templates
        
        # 加载模板
        load_builtin_templates()
        
        # 测试用例
        test_cases = [
            "做一个哪吒鬼畜视频",
            "制作产品宣传视频", 
            "画北京上海广州的海报",
            "生成一首音乐"  # 应该没有匹配的模板
        ]
        
        for user_input in test_cases:
            result = recommend_template.invoke({"user_input": user_input})
            
            print(f"   输入: '{user_input}'")
            if result.get("recommended_template"):
                print(f"   ✅ 推荐: {result['recommended_template']} (置信度: {result['confidence']:.2f})")
                if result.get("suggested_params"):
                    print(f"   📝 建议参数: {result['suggested_params']}")
            else:
                print(f"   ⚪ 无推荐: {result['reason']}")
            print()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模板推荐测试失败: {e}")
        return False


def test_template_instantiation():
    """测试模板实例化功能"""
    print("🎨 测试2: 模板实例化功能")
    
    try:
        from src.tools.template_tools import create_plan_from_template
        from src.templates.builtin_templates import load_builtin_templates
        
        # 加载模板
        load_builtin_templates()
        
        # 测试AI鬼畜视频模板实例化
        result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {
                "character": "哪吒",
                "style": "modern", 
                "duration": 30,
                "effect_intensity": "high",
                "background_music": True
            },
            "user_context": "做一个现代风格的哪吒鬼畜视频"
        })
        
        if result.get("success"):
            plan = result["plan"]
            print(f"   ✅ 模板实例化成功")
            print(f"   📋 计划类型: {type(plan).__name__}")
            print(f"   📊 步骤数量: {len(plan.steps)}")
            print(f"   🎯 原始任务: {plan.original_task}")
            
            # 显示步骤信息
            print("   📝 步骤列表:")
            for i, step in enumerate(plan.steps, 1):
                print(f"      {i}. {step.step_id}: {step.name}")
                if hasattr(step, 'dependencies') and step.dependencies:
                    print(f"         依赖: {step.dependencies}")
                if hasattr(step, 'parallel_group') and step.parallel_group:
                    print(f"         并行组: {step.parallel_group}")
            
            return plan
        else:
            print(f"   ❌ 模板实例化失败: {result.get('error')}")
            return None
            
    except Exception as e:
        print(f"   ❌ 模板实例化测试失败: {e}")
        return None


def test_plan_execution_readiness(plan):
    """测试Plan是否可以被Master Agent执行"""
    print("⚙️  测试3: Plan执行就绪性")
    
    try:
        if not plan:
            print("   ❌ 没有Plan可供测试")
            return False
        
        # 检查Plan的基本属性
        print(f"   📋 Plan ID: {getattr(plan, 'plan_id', 'N/A')}")
        print(f"   📝 原始任务: {plan.original_task}")
        print(f"   📊 步骤总数: {len(plan.steps)}")
        
        # 检查第一个步骤是否可执行
        if hasattr(plan, 'get_next_executable_steps'):
            # 增强Plan模型
            executable_steps = plan.get_next_executable_steps()
            print(f"   🚀 可执行步骤: {len(executable_steps)}")
            
            if executable_steps:
                first_step = executable_steps[0]
                print(f"   🎯 下一步: {first_step.step_id} - {first_step.name}")
                print(f"   🔧 工具: {first_step.tool_to_use}")
                print(f"   📥 输入参数数量: {len(first_step.inputs)}")
                
                # 检查参数是否包含模板引用
                has_template_refs = any(
                    isinstance(v, str) and ('{' in v or '$' in v) 
                    for v in first_step.inputs.values()
                )
                print(f"   🔗 包含参数引用: {'是' if has_template_refs else '否'}")
        else:
            # 传统Plan模型
            if hasattr(plan, 'get_next_pending_step'):
                next_step = plan.get_next_pending_step()
                if next_step:
                    print(f"   🎯 下一步: {next_step.step_id} - {next_step.description}")
                    print(f"   🔧 工具: {next_step.tool_to_use}")
                else:
                    print("   ⚪ 没有待执行步骤")
        
        print("   ✅ Plan基本结构正确，可以被Master Agent处理")
        return True
        
    except Exception as e:
        print(f"   ❌ Plan执行就绪性测试失败: {e}")
        return False


def test_state_integration():
    """测试State集成"""
    print("🔄 测试4: State集成")
    
    try:
        from src.graph_v2.types import State
        
        # 创建包含模板信息的State
        test_state: State = {
            "messages": [("human", "做一个哪吒鬼畜视频")],
            "plan": None,
            "template_id": "ai_parody_video",
            "template_params": {
                "character": "哪吒",
                "style": "modern",
                "duration": 30
            },
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None
        }
        
        print("   ✅ State结构扩展正常")
        print(f"   📋 模板ID: {test_state.get('template_id')}")
        print(f"   📝 模板参数: {test_state.get('template_params')}")
        print(f"   🎯 执行模式: {test_state.get('execution_mode')}")
        print(f"   🔄 模板模式: {test_state.get('template_mode')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ State集成测试失败: {e}")
        return False


def test_master_agent_tools():
    """测试Master Agent工具集成"""
    print("🤖 测试5: Master Agent工具集成")
    
    try:
        from src.tools.template_tools import template_tools
        
        print(f"   ✅ 模板工具数量: {len(template_tools)}")
        
        tool_names = [tool.name for tool in template_tools]
        expected_tools = [
            "recommend_template",
            "get_available_templates", 
            "create_plan_from_template",
            "validate_template_params"
        ]
        
        for tool_name in expected_tools:
            if tool_name in tool_names:
                print(f"   ✅ {tool_name}: 已集成")
            else:
                print(f"   ❌ {tool_name}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Master Agent工具集成测试失败: {e}")
        return False


def main():
    """运行Phase 1端到端测试"""
    print("🦌 DeerFlow Phase 1 端到端测试")
    print("验证模板系统核心流程")
    print("=" * 50)
    
    tests = [
        ("模板推荐", test_template_recommendation),
        ("模板实例化", test_template_instantiation),
        ("State集成", test_state_integration),
        ("Master Agent工具集成", test_master_agent_tools)
    ]
    
    results = {}
    plan = None
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_name == "模板实例化":
                result = test_func()
                if isinstance(result, bool):
                    results[test_name] = result
                else:
                    # 返回了plan对象
                    plan = result
                    results[test_name] = plan is not None
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 如果有plan，测试执行就绪性
    if plan:
        print(f"\nPlan执行就绪性:")
        results["Plan执行就绪性"] = test_plan_execution_readiness(plan)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 Phase 1 端到端测试全部通过！")
        print("\n📋 Phase 1 完成状态:")
        print("✅ 模板推荐系统正常工作")
        print("✅ 模板实例化功能正常")
        print("✅ State扩展集成成功")
        print("✅ Master Agent工具集成完成")
        print("✅ 生成的Plan可以被执行")
        
        print("\n🚀 可以开始Phase 2: 增强Plan模型和参数解析")
        return True
    else:
        print(f"❌ 还有 {total - passed} 个问题需要解决")
        print("🔧 建议先修复这些问题再进入Phase 2")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
