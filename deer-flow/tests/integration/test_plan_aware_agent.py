#!/usr/bin/env python3
"""
测试方案A：Master Agent感知计划
验证执行引擎和Master Agent的协作
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_state_current_step():
    """测试State的current_step字段"""
    print("🔍 测试State的current_step字段...")
    
    try:
        from src.graph_v2.types import State
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        
        load_builtin_templates()
        
        # 创建测试计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "测试", "style": "modern", "duration": 30},
            "user_context": "测试current_step"
        })
        
        plan = plan_result['plan']
        
        # 创建包含current_step的状态
        test_state: State = {
            "messages": [],
            "plan": plan,
            "template_id": "ai_parody_video",
            "template_params": {},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None,
            "current_step": "collect_materials"  # 新字段
        }
        
        print(f"   ✅ State支持current_step字段")
        print(f"   当前步骤: {test_state.get('current_step')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ State测试失败: {e}")
        return False

def test_execution_engine_step_instruction():
    """测试执行引擎的步骤指令生成"""
    print("\n⚙️  测试执行引擎步骤指令...")
    
    try:
        from src.graph_v2.execution_engine import execution_engine_node
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        from src.graph_v2.types import State
        from langchain_core.messages import HumanMessage
        
        load_builtin_templates()
        
        # 创建测试计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "悟空", "style": "cartoon", "duration": 45},
            "user_context": "测试步骤指令"
        })
        
        plan = plan_result['plan']
        
        # 创建初始状态
        test_state: State = {
            "messages": [HumanMessage(content="做一个悟空鬼畜视频")],
            "plan": plan,
            "template_id": "ai_parody_video",
            "template_params": {},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None,
            "current_step": None
        }
        
        print(f"   初始状态: current_step = {test_state.get('current_step')}")
        print(f"   计划步骤数: {len(plan.steps)}")
        
        # 模拟执行引擎的第一次调用（不实际调用master_agent_node）
        # 我们只测试执行引擎的逻辑，不调用实际的Master Agent
        
        # 获取第一个可执行步骤
        executable_steps = plan.get_next_executable_steps()
        if executable_steps:
            next_step = executable_steps[0]
            print(f"   下一步: {next_step.step_id} - {next_step.name}")
            
            # 模拟执行引擎设置current_step
            test_state["current_step"] = next_step.step_id
            
            # 模拟执行引擎生成指令
            step_instruction = (
                f"请执行计划中的步骤：\n"
                f"步骤ID: {next_step.step_id}\n"
                f"步骤名称: {next_step.name}\n"
                f"描述: {next_step.description}\n"
                f"使用工具: {next_step.tool_to_use}\n"
                f"执行完成后，请调用update_step_status更新步骤状态。"
            )
            
            print(f"   ✅ 生成步骤指令成功")
            print(f"   current_step设置为: {test_state.get('current_step')}")
            print(f"   指令内容: {step_instruction[:100]}...")
            
            return True
        else:
            print(f"   ❌ 没有可执行步骤")
            return False
        
    except Exception as e:
        print(f"   ❌ 执行引擎测试失败: {e}")
        return False

def test_master_agent_tools_with_current_step():
    """测试Master Agent工具在current_step模式下的工作"""
    print("\n🤖 测试Master Agent工具...")
    
    try:
        from src.tools.state_management import resolve_step_inputs, update_step_status
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        from src.graph_v2.types import State
        
        load_builtin_templates()
        
        # 创建测试计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "哪吒", "style": "modern", "duration": 30},
            "user_context": "测试工具协作"
        })
        
        plan = plan_result['plan']
        first_step = plan.steps[0]
        
        # 创建带current_step的状态
        test_state: State = {
            "messages": [],
            "plan": plan,
            "template_id": "ai_parody_video",
            "template_params": {},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None,
            "current_step": first_step.step_id
        }
        
        print(f"   当前步骤: {test_state.get('current_step')}")
        
        # 测试resolve_step_inputs
        print("   测试 resolve_step_inputs...")
        resolve_result = resolve_step_inputs.invoke({
            "state": test_state,
            "step_id": first_step.step_id
        })
        
        if resolve_result.get('success'):
            print(f"   ✅ resolve_step_inputs 成功")
            print(f"   解析的参数: {len(resolve_result.get('resolved_inputs', {}))} 个")
        else:
            print(f"   ⚠️  resolve_step_inputs 有问题: {resolve_result.get('error')}")
        
        # 测试update_step_status
        print("   测试 update_step_status...")
        mock_result = {
            "success": True,
            "content": "步骤执行完成",
            "assets": {"images": ["test_image.jpg"]},
            "metadata": {"execution_time": 2.5}
        }
        
        update_result = update_step_status.invoke({
            "state": test_state,
            "step_id": first_step.step_id,
            "status": "completed",
            "result": mock_result
        })
        
        if update_result.get("error"):
            print(f"   ❌ update_step_status 失败: {update_result.get('error')}")
            return False
        elif "plan" in update_result:
            print(f"   ✅ update_step_status 成功")
            
            # 检查步骤状态是否更新
            updated_plan = update_result["plan"]
            updated_step = updated_plan.get_step(first_step.step_id)
            if updated_step and updated_step.status == "completed":
                print(f"   ✅ 步骤状态正确更新为: {updated_step.status}")
            else:
                print(f"   ❌ 步骤状态更新失败")
                return False
        else:
            print(f"   ❌ update_step_status 返回格式异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Master Agent工具测试失败: {e}")
        return False

def test_workflow_simulation():
    """模拟完整的工作流程"""
    print("\n🔄 模拟完整工作流程...")
    
    try:
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import create_plan_from_template
        from src.tools.state_management import resolve_step_inputs, update_step_status
        from src.graph_v2.types import State
        
        load_builtin_templates()
        
        # 创建测试计划
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "孙悟空", "style": "modern", "duration": 30},
            "user_context": "完整工作流程测试"
        })
        
        plan = plan_result['plan']
        
        # 初始状态
        state: State = {
            "messages": [],
            "plan": plan,
            "template_id": "ai_parody_video",
            "template_params": {},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None,
            "current_step": None
        }
        
        print(f"   计划包含 {len(plan.steps)} 个步骤")
        
        # 模拟执行前3个步骤
        for i in range(min(3, len(plan.steps))):
            print(f"\n   --- 模拟执行步骤 {i+1} ---")
            
            # 1. 执行引擎获取下一步
            executable_steps = plan.get_next_executable_steps()
            if not executable_steps:
                print(f"   没有更多可执行步骤")
                break
            
            next_step = executable_steps[0]
            print(f"   执行步骤: {next_step.step_id}")
            
            # 2. 执行引擎设置current_step
            state["current_step"] = next_step.step_id
            
            # 3. Master Agent解析参数
            resolve_result = resolve_step_inputs.invoke({
                "state": state,
                "step_id": next_step.step_id
            })
            print(f"   参数解析: {'成功' if resolve_result.get('success') else '失败'}")
            
            # 4. 模拟Master Agent执行工具（这里跳过实际工具调用）
            mock_tool_result = {
                "success": True,
                "content": f"步骤 {next_step.step_id} 执行完成",
                "assets": {"images": [f"{next_step.step_id}_result.jpg"]},
                "metadata": {"step_id": next_step.step_id}
            }
            
            # 5. Master Agent更新步骤状态
            update_result = update_step_status.invoke({
                "state": state,
                "step_id": next_step.step_id,
                "status": "completed",
                "result": mock_tool_result
            })
            
            if "plan" in update_result:
                state["plan"] = update_result["plan"]
                print(f"   步骤状态更新: 成功")
            else:
                print(f"   步骤状态更新: 失败")
                return False
            
            # 6. 执行引擎清除current_step
            state["current_step"] = None
        
        # 检查最终状态
        completed_steps = len([s for s in state["plan"].steps if s.status == "completed"])
        print(f"\n   ✅ 工作流程模拟完成")
        print(f"   完成步骤数: {completed_steps}")
        print(f"   计划是否完成: {state['plan'].is_complete()}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 工作流程模拟失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🦌 DeerFlow 方案A：Master Agent感知计划测试")
    print("=" * 60)
    
    tests = [
        ("State current_step字段", test_state_current_step),
        ("执行引擎步骤指令", test_execution_engine_step_instruction),
        ("Master Agent工具协作", test_master_agent_tools_with_current_step),
        ("完整工作流程模拟", test_workflow_simulation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试崩溃: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 60)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 方案A实现成功！")
        print(f"\n📋 实现的功能:")
        print(f"✅ State支持current_step字段")
        print(f"✅ 执行引擎设置当前步骤并生成指令")
        print(f"✅ Master Agent工具在计划感知模式下正常工作")
        print(f"✅ 完整的执行引擎↔Master Agent协作流程")
        
        print(f"\n🎯 状态管理工具的明确用途:")
        print(f"✅ resolve_step_inputs: Master Agent解析当前步骤参数")
        print(f"✅ update_step_status: Master Agent更新当前步骤状态")
        print(f"✅ get_current_plan: 信息查询和调试")
        print(f"✅ get_next_pending_step: 恢复和错误处理场景")
        
        print(f"\n🚀 现在可以进行真实测试了！")
        return True
    else:
        print(f"\n❌ 发现问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
