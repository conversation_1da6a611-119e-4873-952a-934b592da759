#!/usr/bin/env python3
"""
测试执行循环修复效果
验证Master Agent是否会继续执行直到计划完成
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_should_continue_logic():
    """测试修复后的should_continue逻辑"""
    print("🔧 测试should_continue逻辑修复...")
    
    from src.graph_v2.builder import should_continue
    from src.graph_v2.types import State
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.template_tools import create_plan_from_template
    from langchain_core.messages import AIMessage
    
    load_builtin_templates()
    
    # 创建测试计划
    plan_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "哪吒", "style": "modern", "duration": 30},
        "user_context": "测试执行循环"
    })
    
    plan = plan_result['plan']
    
    # 测试场景1：计划未完成，Agent给出状态更新
    print("   场景1：计划未完成，Agent给出状态更新")
    state1: State = {
        "messages": [
            ("human", "做一个哪吒鬼畜视频"),
            AIMessage(content="我已经完成了第一步素材收集，现在开始第二步...")  # 没有tool_calls
        ],
        "plan": plan,
        "template_id": "ai_parody_video",
        "template_params": {},
        "template_mode": True,
        "execution_mode": "template",
        "template_context": None
    }
    
    result1 = should_continue(state1)
    print(f"   结果: {result1}")
    if result1 == "master_agent":
        print("   ✅ 正确：应该继续执行")
    else:
        print("   ❌ 错误：不应该停止")
        return False
    
    # 测试场景2：计划完成，Agent给出最终总结
    print("\n   场景2：计划完成，Agent给出最终总结")
    
    # 标记所有步骤为完成
    for step in plan.steps:
        step.status = "completed"
    plan.execution_context.completed_steps = len(plan.steps)
    
    state2: State = {
        "messages": [
            ("human", "做一个哪吒鬼畜视频"),
            AIMessage(content="所有步骤都已完成！您的哪吒鬼畜视频制作完成。")  # 没有tool_calls
        ],
        "plan": plan,
        "template_id": "ai_parody_video", 
        "template_params": {},
        "template_mode": True,
        "execution_mode": "template",
        "template_context": None
    }
    
    result2 = should_continue(state2)
    print(f"   结果: {result2}")
    if result2 == "__end__":
        print("   ✅ 正确：应该结束")
    else:
        print("   ❌ 错误：应该结束但没有结束")
        return False
    
    # 测试场景3：没有计划
    print("\n   场景3：没有计划")
    state3: State = {
        "messages": [
            ("human", "你好"),
            AIMessage(content="您好！我可以帮您创建视频内容。")
        ],
        "plan": None,
        "template_id": None,
        "template_params": None,
        "template_mode": False,
        "execution_mode": "auto",
        "template_context": None
    }
    
    result3 = should_continue(state3)
    print(f"   结果: {result3}")
    if result3 == "master_agent":
        print("   ✅ 正确：应该继续让Agent处理")
    else:
        print("   ❌ 错误：应该继续")
        return False
    
    return True

def test_plan_completion_check():
    """测试计划完成检查"""
    print("\n📊 测试计划完成检查...")
    
    from src.templates.builtin_templates import load_builtin_templates
    from src.tools.template_tools import create_plan_from_template
    
    load_builtin_templates()
    
    # 创建测试计划
    plan_result = create_plan_from_template.invoke({
        "template_id": "ai_parody_video",
        "params": {"character": "悟空", "style": "cartoon", "duration": 45},
        "user_context": "测试计划完成"
    })
    
    plan = plan_result['plan']
    
    # 测试初始状态
    print(f"   初始状态：{plan.is_complete()}")
    if plan.is_complete():
        print("   ❌ 错误：新计划不应该是完成状态")
        return False
    
    # 逐步完成步骤
    completed_count = 0
    for i, step in enumerate(plan.steps):
        step.status = "completed"
        plan.execution_context.completed_steps += 1
        completed_count += 1
        
        is_complete = plan.is_complete()
        print(f"   完成 {completed_count}/{len(plan.steps)} 步骤：{is_complete}")
        
        if i < len(plan.steps) - 1:  # 不是最后一步
            if is_complete:
                print(f"   ❌ 错误：步骤{i+1}完成后不应该显示计划完成")
                return False
        else:  # 最后一步
            if not is_complete:
                print(f"   ❌ 错误：所有步骤完成后应该显示计划完成")
                return False
    
    print("   ✅ 计划完成检查正常")
    return True

def main():
    """主测试流程"""
    print("🦌 DeerFlow 执行循环修复测试")
    print("=" * 50)
    
    tests = [
        ("should_continue逻辑", test_should_continue_logic),
        ("计划完成检查", test_plan_completion_check)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 50)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 执行循环修复成功！")
        print(f"\n📋 修复效果:")
        print(f"✅ 计划未完成时会继续执行")
        print(f"✅ 计划完成时会正确结束")
        print(f"✅ 无计划时会正常处理")
        print(f"✅ 完全兼容现有框架")
        
        print(f"\n🚀 现在可以测试完整的执行流程了！")
        print(f"   建议运行: python interactive_chat.py")
        print(f"   输入: '做一个哪吒鬼畜视频'")
        print(f"   预期: 系统会自动执行所有5个步骤")
        
        return True
    else:
        print(f"\n❌ 修复验证失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
