#!/usr/bin/env python3
"""
Phase 2 最小化验证：核心功能测试
避免复杂的导入，专注验证核心功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_enhanced_models():
    """测试增强模型的核心功能"""
    print("🧪 测试增强模型...")
    
    try:
        from graph_v2.enhanced_models import EnhancedPlan, EnhancedStep, ExecutionContext, StepType
        import uuid
        
        # 创建执行上下文
        context = ExecutionContext(
            plan_id="test_001",
            template_params={"character": "哪吒", "style": "modern"},
            shared_variables={"quality": "high"}
        )
        
        # 创建步骤
        step1 = EnhancedStep(
            step_id="collect_materials",
            name="素材收集",
            description="收集哪吒相关素材",
            tool_to_use="visual_expert",
            step_type=StepType.DATA_COLLECTION,
            inputs={"character": "{character}", "style": "{style}"}
        )
        
        step2 = EnhancedStep(
            step_id="create_video",
            name="视频制作",
            description="制作视频",
            tool_to_use="video_expert",
            step_type=StepType.FINALIZATION,
            dependencies=["collect_materials"],
            inputs={"materials": "{{collect_materials.assets.images}}", "quality": "${quality}"}
        )
        
        # 创建计划
        plan = EnhancedPlan(
            plan_id="test_001",
            original_task="制作哪吒视频",
            steps=[step1, step2],
            execution_context=context
        )
        
        print("   ✅ 增强模型创建成功")
        print(f"   步骤数量: {len(plan.steps)}")
        print(f"   第一步类型: {plan.steps[0].step_type.value}")
        
        # 测试执行流程
        executable = plan.get_next_executable_steps()
        print(f"   可执行步骤: {[s.step_id for s in executable]}")
        
        # 模拟执行第一步
        if executable:
            first_step = executable[0]
            first_step.status = "completed"
            first_step.result = {"success": True, "assets": {"images": ["test.jpg"]}}
            plan.execution_context.update_step_output(first_step.step_id, first_step.result)
            
            # 检查下一步
            next_executable = plan.get_next_executable_steps()
            print(f"   完成后下一步: {[s.step_id for s in next_executable]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 增强模型测试失败: {e}")
        return False

def test_parameter_resolution():
    """测试参数解析功能"""
    print("🔧 测试参数解析...")
    
    try:
        from graph_v2.enhanced_models import EnhancedPlan, EnhancedStep, ExecutionContext, StepType
        from graph_v2.parameter_resolver import ParameterResolver
        
        # 创建带有参数引用的计划
        context = ExecutionContext(
            plan_id="test_002",
            template_params={"character": "孙悟空", "style": "cartoon"},
            shared_variables={"quality": "high"}
        )
        
        # 添加第一步的输出
        context.update_step_output("collect_materials", {
            "success": True,
            "assets": {"images": ["wukong1.jpg", "wukong2.jpg"]},
            "metadata": {"count": 2}
        })
        
        step = EnhancedStep(
            step_id="create_character",
            name="角色创建",
            description="创建角色",
            tool_to_use="visual_expert",
            step_type=StepType.CONTENT_CREATION,
            inputs={
                "character_name": "{character}",
                "style": "{style}",
                "source_images": "{{collect_materials.assets.images}}",
                "quality_setting": "${quality}",
                "image_count": "{{collect_materials.metadata.count}}"
            }
        )
        
        plan = EnhancedPlan(
            plan_id="test_002",
            original_task="测试参数解析",
            steps=[step],
            execution_context=context
        )
        
        # 测试参数解析
        resolver = ParameterResolver(plan)
        resolved = resolver.resolve(step.inputs)
        
        print("   ✅ 参数解析成功")
        print(f"   原始参数数量: {len(step.inputs)}")
        print(f"   解析后参数数量: {len(resolved)}")
        
        # 验证解析结果
        expected_character = "孙悟空"
        expected_quality = "high"
        
        if resolved.get("character_name") == expected_character:
            print("   ✅ 模板参数解析正确")
        if resolved.get("quality_setting") == expected_quality:
            print("   ✅ 全局变量解析正确")
        if "wukong1.jpg" in str(resolved.get("source_images", "")):
            print("   ✅ 步骤输出引用解析正确")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 参数解析测试失败: {e}")
        return False

def test_template_models():
    """测试模板模型"""
    print("🎨 测试模板模型...")
    
    try:
        from graph_v2.template_models import PlanTemplate, StepTemplate, ParameterSchema, ParameterType
        from graph_v2.enhanced_models import StepType
        
        # 创建简单模板
        template = PlanTemplate(
            template_id="test_template",
            name="测试模板",
            description="用于测试的模板",
            category="test",
            parameters={
                "name": ParameterSchema(type=ParameterType.STRING, required=True),
                "count": ParameterSchema(type=ParameterType.INTEGER, default=1)
            },
            step_templates=[
                StepTemplate(
                    template_step_id="step1",
                    name="第一步",
                    description_template="处理{name}",
                    tool_to_use="test_tool",
                    step_type=StepType.CONTENT_CREATION,
                    input_template={"name": "{name}", "count": "{count}"}
                )
            ]
        )
        
        print("   ✅ 模板创建成功")
        print(f"   模板ID: {template.template_id}")
        print(f"   参数数量: {len(template.parameters)}")
        print(f"   步骤数量: {len(template.step_templates)}")
        
        # 测试参数验证
        valid_params = {"name": "测试", "count": 5}
        validated = template.validate_parameters(valid_params)
        print(f"   ✅ 参数验证成功: {validated}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模板模型测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🦌 DeerFlow Phase 2 最小化验证")
    print("=" * 50)
    
    tests = [
        ("增强模型", test_enhanced_models),
        ("参数解析", test_parameter_resolution),
        ("模板模型", test_template_models)
    ]
    
    results = {}
    
    for name, test_func in tests:
        print(f"\n{name}:")
        try:
            results[name] = test_func()
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            results[name] = False
    
    # 总结
    print(f"\n" + "=" * 50)
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    for name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {name}")
    
    if passed == total:
        print(f"\n🎉 Phase 2 核心功能验证通过！")
        print(f"✅ 增强Plan模型工作正常")
        print(f"✅ 参数解析系统工作正常") 
        print(f"✅ 模板模型工作正常")
        print(f"\n🚀 Phase 2 实现成功！")
        print(f"\n📋 总结：")
        print(f"   • Phase 1: 模板推荐和基础实例化 ✅")
        print(f"   • Phase 2: 增强模型和参数解析 ✅")
        print(f"   • 核心功能: 端到端流程验证 ✅")
        return True
    else:
        print(f"\n❌ 发现问题，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
