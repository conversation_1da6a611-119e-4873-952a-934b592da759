#!/usr/bin/env python3
"""
调试当前系统的问题
识别和记录所有bug，制定修复计划
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_template_flow():
    """测试基础模板流程，识别问题"""
    print("🔍 测试基础模板流程...")
    
    issues = []
    
    try:
        # 1. 测试模板加载
        print("   1. 测试模板加载...")
        from src.templates.builtin_templates import load_builtin_templates
        from src.tools.template_tools import get_template_registry

        load_builtin_templates()

        # 直接检查内部注册表
        from src.tools.template_tools import _template_registry

        if len(_template_registry) == 0:
            issues.append("❌ 模板注册失败：模板库为空")
            print(f"   调试信息：注册表内容 = {_template_registry}")
        else:
            print(f"   ✅ 模板加载成功：{len(_template_registry)}个模板")
            print(f"   模板列表：{list(_template_registry.keys())}")
        
        # 2. 测试模板推荐
        print("   2. 测试模板推荐...")
        from src.tools.template_tools import recommend_template
        
        result = recommend_template.invoke({"user_input": "做一个哪吒鬼畜视频"})
        if not result.get('recommended_template'):
            issues.append("❌ 模板推荐失败：没有返回推荐结果")
        else:
            print(f"   ✅ 模板推荐成功：{result['recommended_template']}")
        
        # 3. 测试模板实例化
        print("   3. 测试模板实例化...")
        from src.tools.template_tools import create_plan_from_template
        
        plan_result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"character": "哪吒", "style": "modern", "duration": 30},
            "user_context": "做一个哪吒鬼畜视频"
        })
        
        if not plan_result.get('success'):
            issues.append(f"❌ 模板实例化失败：{plan_result.get('error')}")
        else:
            plan = plan_result['plan']
            print(f"   ✅ 模板实例化成功：{len(plan.steps)}个步骤")
            
            # 4. 测试计划执行逻辑
            print("   4. 测试计划执行逻辑...")
            
            # 检查步骤ID是否正确
            step_ids = [s.step_id for s in plan.steps]
            print(f"   步骤ID: {step_ids}")
            
            # 检查依赖关系
            for step in plan.steps:
                if step.dependencies:
                    print(f"   {step.step_id} 依赖: {step.dependencies}")
                    # 验证依赖的步骤是否存在
                    for dep in step.dependencies:
                        if dep not in step_ids:
                            issues.append(f"❌ 依赖关系错误：{step.step_id}依赖不存在的步骤{dep}")
            
            # 5. 测试参数解析
            print("   5. 测试参数解析...")
            from src.graph_v2.parameter_resolver import ParameterResolver
            
            resolver = ParameterResolver(plan)
            
            # 模拟第一步完成
            first_step = plan.steps[0]
            first_step.status = "completed"
            first_step.result = {
                "success": True,
                "assets": {"images": ["test1.jpg", "test2.jpg"]},
                "metadata": {"count": 2}
            }
            plan.execution_context.update_step_output(first_step.step_id, first_step.result)
            
            # 测试第二步的参数解析
            if len(plan.steps) > 1:
                second_step = plan.steps[1]
                try:
                    resolved = resolver.resolve(second_step.inputs)
                    print(f"   ✅ 参数解析成功：{len(resolved)}个参数")
                    
                    # 检查是否有未解析的引用
                    for key, value in resolved.items():
                        if isinstance(value, str) and ('{{' in value or '${' in value or '{' in value):
                            issues.append(f"❌ 参数解析不完整：{key}={value}")
                            
                except Exception as e:
                    issues.append(f"❌ 参数解析失败：{e}")
            
            # 6. 测试状态管理工具
            print("   6. 测试状态管理工具...")
            from src.tools.state_management import get_current_plan, get_next_pending_step
            from src.graph_v2.types import State

            # 创建正确的State对象
            test_state: State = {
                "messages": [],
                "plan": plan,
                "template_id": None,
                "template_params": None,
                "template_mode": False,
                "execution_mode": "auto",
                "template_context": None
            }

            try:
                current_plan = get_current_plan.invoke({"state": test_state})
                if "error" in str(current_plan).lower():
                    issues.append(f"❌ 获取当前计划失败：{current_plan}")
                else:
                    print(f"   ✅ 获取当前计划成功")

                next_step = get_next_pending_step.invoke({"state": test_state})
                if "error" in str(next_step).lower():
                    issues.append(f"❌ 获取下一步失败：{next_step}")
                else:
                    print(f"   ✅ 获取下一步成功")

            except Exception as e:
                issues.append(f"❌ 状态管理工具错误：{e}")
        
    except Exception as e:
        issues.append(f"❌ 基础流程测试失败：{e}")
    
    return issues

def test_master_agent_integration():
    """测试Master Agent集成"""
    print("\n🤖 测试Master Agent集成...")
    
    issues = []
    
    try:
        # 测试工具导入
        print("   1. 测试工具导入...")
        from graph_v2.nodes import master_agent_node
        print("   ✅ Master Agent节点导入成功")
        
        # 测试State兼容性
        print("   2. 测试State兼容性...")
        from graph_v2.types import State
        
        test_state: State = {
            "messages": [("human", "做一个哪吒鬼畜视频")],
            "plan": None,
            "template_id": "ai_parody_video",
            "template_params": {"character": "哪吒"},
            "template_mode": True,
            "execution_mode": "template",
            "template_context": None
        }
        print("   ✅ State结构兼容")
        
        # 测试图构建
        print("   3. 测试图构建...")
        from graph_v2.builder import GraphBuilder

        builder = GraphBuilder()
        graph = builder.build(checkpointer=None)  # 传入None作为checkpointer
        print("   ✅ 图构建成功")
        
    except Exception as e:
        issues.append(f"❌ Master Agent集成错误：{e}")
    
    return issues

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    issues = []
    
    try:
        # 1. 测试无效模板ID
        print("   1. 测试无效模板ID...")
        from src.tools.template_tools import create_plan_from_template
        
        result = create_plan_from_template.invoke({
            "template_id": "nonexistent_template",
            "params": {},
            "user_context": "测试"
        })
        
        if result.get('success'):
            issues.append("❌ 无效模板ID应该失败但成功了")
        else:
            print("   ✅ 无效模板ID正确处理")
        
        # 2. 测试无效参数
        print("   2. 测试无效参数...")
        result = create_plan_from_template.invoke({
            "template_id": "ai_parody_video",
            "params": {"invalid_param": "value"},  # 缺少必需参数
            "user_context": "测试"
        })
        
        if result.get('success'):
            issues.append("❌ 无效参数应该失败但成功了")
        else:
            print("   ✅ 无效参数正确处理")
        
        # 3. 测试空计划
        print("   3. 测试空计划...")
        from src.tools.state_management import get_next_pending_step
        from src.graph_v2.types import State

        empty_state: State = {
            "messages": [],
            "plan": None,
            "template_id": None,
            "template_params": None,
            "template_mode": False,
            "execution_mode": "auto",
            "template_context": None
        }
        result = get_next_pending_step.invoke({"state": empty_state})
        
        if "error" not in str(result).lower() and "no plan" not in str(result).lower():
            issues.append("❌ 空计划应该返回错误信息")
        else:
            print("   ✅ 空计划正确处理")
            
    except Exception as e:
        issues.append(f"❌ 边界情况测试失败：{e}")
    
    return issues

def main():
    """主调试流程"""
    print("🦌 DeerFlow 系统Bug调试")
    print("=" * 50)
    
    all_issues = []
    
    # 运行所有测试
    tests = [
        ("基础模板流程", test_basic_template_flow),
        ("Master Agent集成", test_master_agent_integration),
        ("边界情况", test_edge_cases)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"❌ {test_name}测试崩溃：{e}")
    
    # 总结问题
    print(f"\n" + "=" * 50)
    print(f"🐛 发现的问题总结")
    print(f"=" * 50)
    
    if not all_issues:
        print("🎉 没有发现明显问题！系统基本功能正常。")
    else:
        print(f"发现 {len(all_issues)} 个问题：")
        for i, issue in enumerate(all_issues, 1):
            print(f"{i}. {issue}")
    
    # 生成修复计划
    print(f"\n📋 修复优先级建议：")
    
    critical_issues = [i for i in all_issues if "失败" in i or "错误" in i or "崩溃" in i]
    warning_issues = [i for i in all_issues if "不完整" in i or "应该" in i]
    
    if critical_issues:
        print(f"🔥 高优先级（{len(critical_issues)}个）：")
        for issue in critical_issues:
            print(f"   • {issue}")
    
    if warning_issues:
        print(f"⚠️  中优先级（{len(warning_issues)}个）：")
        for issue in warning_issues:
            print(f"   • {issue}")
    
    return len(all_issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
