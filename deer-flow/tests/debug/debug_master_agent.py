#!/usr/bin/env python3
"""
Debug script to test Master Agent behavior.
"""

import asyncio
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rich.console import Console
from rich.panel import Panel
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
from src.graph_v2.builder import GraphBuilder

console = Console()

async def debug_master_agent():
    """Debug Master Agent behavior."""
    console.print(Panel.fit(
        "[bold blue]🔍 Master Agent 调试[/bold blue]\n"
        "[cyan]检查工具调用和提示词执行[/cyan]",
        title="调试模式",
        border_style="blue"
    ))
    
    os.makedirs("db", exist_ok=True)
    
    async with AsyncSqliteSaver.from_conn_string("db/debug.sqlite") as memory:
        builder = GraphBuilder()
        graph = builder.build(checkpointer=memory)
        config = {"configurable": {"thread_id": "debug-session"}}
        
        # Test simple task
        test_input = "画一只猫"
        console.print(f"\n[bold yellow]测试输入: {test_input}[/bold yellow]")
        
        inputs = {"messages": [("human", test_input)]}
        
        console.print("\n[bold cyan]详细执行过程:[/bold cyan]")
        
        step_count = 0
        async for event in graph.astream(inputs, config=config, stream_mode="updates"):
            step_count += 1
            node_name, node_output = next(iter(event.items()))
            
            console.print(f"\n[bold magenta]步骤 {step_count}: {node_name}[/bold magenta]")
            
            # 检查消息
            messages = node_output.get("messages", [])
            if messages:
                last_message = messages[-1]
                console.print(f"消息类型: {last_message.type}")
                
                if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                    console.print(f"[green]✅ 工具调用数量: {len(last_message.tool_calls)}[/green]")
                    for i, tool_call in enumerate(last_message.tool_calls):
                        console.print(f"  工具 {i+1}: {tool_call['name']}")
                        console.print(f"  参数: {tool_call.get('args', {})}")
                else:
                    console.print(f"[red]❌ 没有工具调用[/red]")
                    if hasattr(last_message, 'content'):
                        content_preview = last_message.content[:200] + "..." if len(last_message.content) > 200 else last_message.content
                        console.print(f"内容: {content_preview}")
            
            # 检查计划
            plan = node_output.get("plan")
            if plan:
                console.print(f"[blue]📋 计划状态: 存在，{len(plan.steps) if hasattr(plan, 'steps') else 0} 个步骤[/blue]")
            else:
                console.print(f"[yellow]📋 计划状态: 不存在[/yellow]")
            
            # 限制步骤数
            if step_count >= 5:
                console.print("[dim]⚠️  限制在5个步骤后停止[/dim]")
                break

async def test_tools_directly():
    """直接测试工具是否可用."""
    console.print(Panel.fit(
        "[bold green]🔧 直接工具测试[/bold green]",
        title="工具测试",
        border_style="green"
    ))
    
    try:
        # 测试导入
        from src.tools.state_management import get_current_plan, state_management_tools
        from src.tools.experts import get_visual_expert_tool
        
        console.print("[green]✅ 工具导入成功[/green]")
        console.print(f"状态管理工具数量: {len(state_management_tools)}")
        
        # 列出工具
        console.print("\n[bold cyan]可用的状态管理工具:[/bold cyan]")
        for tool in state_management_tools:
            console.print(f"  • {tool.name}")
        
        # 测试 visual expert
        visual_tool = get_visual_expert_tool()
        console.print(f"\n[bold cyan]Visual Expert 工具:[/bold cyan]")
        console.print(f"  • 名称: {visual_tool.name}")
        console.print(f"  • 描述: {visual_tool.description[:100]}...")
        
    except Exception as e:
        console.print(f"[red]❌ 工具测试失败: {e}[/red]")
        console.print_exception()

async def test_prompt_template():
    """测试提示词模板."""
    console.print(Panel.fit(
        "[bold purple]📝 提示词模板测试[/bold purple]",
        title="提示词测试",
        border_style="purple"
    ))
    
    try:
        from src.prompts.template import get_prompt_template, apply_prompt_template
        
        # 获取原始模板
        raw_template = get_prompt_template("master_agent_prompt")
        console.print(f"[green]✅ 模板加载成功[/green]")
        console.print(f"模板长度: {len(raw_template)} 字符")
        
        # 显示模板开头
        lines = raw_template.split('\n')[:10]
        console.print("\n[bold cyan]模板开头:[/bold cyan]")
        for line in lines:
            if line.strip():
                console.print(f"  {line[:80]}...")
                break
        
        # 测试应用模板
        test_state = {"messages": [("human", "画一只猫")]}
        formatted_prompt = apply_prompt_template("master_agent_prompt", test_state)
        console.print(f"\n[green]✅ 模板应用成功[/green]")
        console.print(f"格式化后消息数量: {len(formatted_prompt)}")
        
    except Exception as e:
        console.print(f"[red]❌ 提示词测试失败: {e}[/red]")
        console.print_exception()

async def main():
    """主函数."""
    try:
        await test_tools_directly()
        await test_prompt_template()
        await debug_master_agent()
        
        console.print(Panel.fit(
            "[bold green]🎉 调试完成[/bold green]\n"
            "[cyan]请查看上面的输出分析问题[/cyan]",
            title="调试结果",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel.fit(
            f"[bold red]❌ 调试失败[/bold red]\n"
            f"[red]错误: {e}[/red]",
            title="调试失败",
            border_style="red"
        ))
        console.print_exception(show_locals=True)

if __name__ == "__main__":
    asyncio.run(main())
