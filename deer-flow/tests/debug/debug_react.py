#!/usr/bin/env python3
"""
调试React Agent过程

专门用于调试为什么没有显示工具调用过程的脚本
"""

import asyncio
import sys
import os
import json
from rich.console import Console
from rich.panel import Panel

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

from src.graph_v2.builder import GraphBuilder
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver

console = Console()

def debug_message(msg, level="INFO"):
    """调试消息输出"""
    colors = {
        "INFO": "blue",
        "WARNING": "yellow", 
        "ERROR": "red",
        "SUCCESS": "green"
    }
    color = colors.get(level, "white")
    console.print(f"[{color}][DEBUG {level}][/{color}] {msg}")

def analyze_message(msg, index):
    """分析单个消息"""
    console.print(f"\n[bold cyan]📧 消息 {index}:[/bold cyan]")
    console.print(f"• 类型: {msg.type}")
    console.print(f"• 内容长度: {len(msg.content) if hasattr(msg, 'content') else 'N/A'}")
    
    if hasattr(msg, 'name'):
        console.print(f"• 名称: {msg.name}")
    
    if hasattr(msg, 'tool_calls') and msg.tool_calls:
        console.print(f"• 工具调用数量: {len(msg.tool_calls)}")
        for i, tool_call in enumerate(msg.tool_calls):
            console.print(f"  - 工具 {i+1}: {tool_call.get('name', 'Unknown')}")
    else:
        console.print("• 工具调用: 无")
    
    # 显示内容预览
    if hasattr(msg, 'content') and msg.content:
        preview = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
        console.print(Panel(
            preview,
            title=f"内容预览 ({msg.type})",
            border_style="dim"
        ))

async def debug_simple_task():
    """调试简单任务执行"""
    console.print(Panel(
        "[bold]🔍 调试简单任务执行过程[/bold]\n\n"
        "我们将执行一个简单的图像生成任务，\n"
        "并详细分析每个步骤的消息内容。",
        title="[bold blue]调试开始[/bold blue]",
        border_style="blue"
    ))
    
    # 确保数据库目录存在
    os.makedirs("db", exist_ok=True)
    
    async with AsyncSqliteSaver.from_conn_string("db/debug_react.sqlite") as memory:
        # 创建工作流图
        builder = GraphBuilder()
        graph = builder.build(checkpointer=memory)
        
        # 配置
        config = {"configurable": {"thread_id": "debug_001"}}
        
        # 输入
        inputs = {"messages": [("human", "画一只可爱的小猫")]}
        
        debug_message("开始执行任务...")
        
        step_count = 0
        all_messages = []
        
        try:
            async for event in graph.astream(inputs, config=config, stream_mode="updates"):
                step_count += 1
                node_name, node_output = next(iter(event.items()))
                
                debug_message(f"步骤 {step_count}: 节点 {node_name}")
                
                # 获取消息
                messages = node_output.get("messages", [])
                debug_message(f"节点 {node_name} 包含 {len(messages)} 条消息")
                
                # 分析每条消息
                for i, msg in enumerate(messages):
                    analyze_message(msg, f"{step_count}.{i+1}")
                    all_messages.append((step_count, node_name, msg))
                
                console.print("[dim]" + "─" * 60 + "[/dim]")
        
        except Exception as e:
            debug_message(f"执行出错: {e}", "ERROR")
            import traceback
            traceback.print_exc()
        
        # 总结分析
        console.print(Panel(
            f"[bold]📊 执行总结[/bold]\n\n"
            f"• 总步骤数: {step_count}\n"
            f"• 总消息数: {len(all_messages)}\n"
            f"• AI消息数: {sum(1 for _, _, msg in all_messages if msg.type == 'ai')}\n"
            f"• 工具消息数: {sum(1 for _, _, msg in all_messages if msg.type == 'tool')}\n"
            f"• 人类消息数: {sum(1 for _, _, msg in all_messages if msg.type == 'human')}",
            title="[bold green]执行总结[/bold green]",
            border_style="green"
        ))
        
        # 检查工具调用
        tool_calls_found = []
        tool_results_found = []
        
        for step, node, msg in all_messages:
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                for tool_call in msg.tool_calls:
                    tool_calls_found.append((step, node, tool_call.get('name', 'Unknown')))
            
            if msg.type == 'tool':
                tool_results_found.append((step, node, getattr(msg, 'name', 'Unknown')))
        
        console.print(Panel(
            f"[bold]🔧 工具调用分析[/bold]\n\n"
            f"• 发现的工具调用: {len(tool_calls_found)}\n" +
            "\n".join([f"  - 步骤{step}.{node}: {tool}" for step, node, tool in tool_calls_found]) +
            f"\n\n• 发现的工具结果: {len(tool_results_found)}\n" +
            "\n".join([f"  - 步骤{step}.{node}: {tool}" for step, node, tool in tool_results_found]),
            title="[bold yellow]工具调用分析[/bold yellow]",
            border_style="yellow"
        ))
        
        # 如果没有发现工具调用，给出可能的原因
        if not tool_calls_found:
            console.print(Panel(
                "[bold red]⚠️ 没有发现工具调用！[/bold red]\n\n"
                "[yellow]可能的原因:[/yellow]\n"
                "1. AI直接给出了答案，没有调用工具\n"
                "2. 工具调用被合并在单个消息中\n"
                "3. 流式更新没有捕获中间步骤\n"
                "4. 消息处理逻辑有问题\n\n"
                "[cyan]建议:[/cyan]\n"
                "• 检查AI的提示词设置\n"
                "• 尝试使用stream_mode='debug'获取更多信息\n"
                "• 检查工具是否正确注册",
                title="[bold red]问题诊断[/bold red]",
                border_style="red"
            ))

async def debug_with_different_modes():
    """使用不同的流式模式进行调试"""
    console.print(Panel(
        "[bold]🔍 使用不同流式模式调试[/bold]\n\n"
        "我们将使用不同的stream_mode来查看\n"
        "是否能捕获到更多信息。",
        title="[bold blue]多模式调试[/bold blue]",
        border_style="blue"
    ))
    
    os.makedirs("db", exist_ok=True)
    
    async with AsyncSqliteSaver.from_conn_string("db/debug_modes.sqlite") as memory:
        builder = GraphBuilder()
        graph = builder.build(checkpointer=memory)
        config = {"configurable": {"thread_id": "debug_modes_001"}}
        inputs = {"messages": [("human", "画一只可爱的小猫")]}
        
        modes = ["updates", "messages", "debug"]
        
        for mode in modes:
            console.print(f"\n[bold cyan]🔄 测试 stream_mode='{mode}'[/bold cyan]")
            
            try:
                if mode == "messages":
                    async for msg, metadata in graph.astream(inputs, config=config, stream_mode=mode):
                        console.print(f"[dim]消息:[/dim] {type(msg).__name__}")
                        if hasattr(msg, 'content'):
                            preview = msg.content[:50] + "..." if len(msg.content) > 50 else msg.content
                            console.print(f"[dim]内容:[/dim] {preview}")
                        console.print(f"[dim]元数据:[/dim] {metadata}")
                        console.print("[dim]---[/dim]")
                else:
                    async for chunk in graph.astream(inputs, config=config, stream_mode=mode):
                        console.print(f"[dim]Chunk类型:[/dim] {type(chunk)}")
                        if isinstance(chunk, dict):
                            for key, value in chunk.items():
                                console.print(f"[dim]{key}:[/dim] {type(value)}")
                                if isinstance(value, dict) and 'messages' in value:
                                    console.print(f"  消息数量: {len(value['messages'])}")
                        console.print("[dim]---[/dim]")
                        
            except Exception as e:
                console.print(f"[red]模式 {mode} 执行出错: {e}[/red]")

def main():
    """主函数"""
    console.print("""
[bold cyan]🐛 React Agent 调试工具[/bold cyan]

这个工具将帮助我们诊断为什么没有看到
完整的React Agent工具调用过程。

[yellow]调试内容:[/yellow]
1. 详细分析每个消息的内容和类型
2. 检查工具调用是否被正确捕获
3. 使用不同的流式模式进行对比
4. 提供问题诊断和解决建议
    """)
    
    choice = input("\n选择调试模式 (1=详细分析, 2=多模式对比, 3=两者都做): ").strip()
    
    async def run_debug():
        if choice in ['1', '3']:
            await debug_simple_task()
        
        if choice in ['2', '3']:
            await debug_with_different_modes()
    
    try:
        asyncio.run(run_debug())
    except KeyboardInterrupt:
        console.print("\n[yellow]调试被用户中断[/yellow]")
    except Exception as e:
        console.print(f"\n[red]调试执行出错: {e}[/red]")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
