#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查MiniMax配置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parents[1]
sys.path.insert(0, str(project_root))

def check_environment_variables():
    """检查环境变量"""
    print("🔍 检查环境变量...")
    
    minimax_api_key = os.getenv('MINIMAX_API_KEY')
    minimax_group_id = os.getenv('MINIMAX_GROUP_ID')
    
    print(f"MINIMAX_API_KEY: {'已设置' if minimax_api_key else '❌ 未设置'}")
    print(f"MINIMAX_GROUP_ID: {'已设置' if minimax_group_id else '❌ 未设置'}")
    
    if minimax_api_key:
        print(f"API Key 前缀: {minimax_api_key[:10]}...")
    if minimax_group_id:
        print(f"Group ID: {minimax_group_id}")
    
    return bool(minimax_api_key and minimax_group_id)


def check_configuration_loading():
    """检查配置加载"""
    print("\n🔧 检查配置加载...")
    
    try:
        from src.config.configuration import Configuration
        config = Configuration()
        
        print(f"配置对象创建: ✅")
        print(f"minimax_api_key: {'已配置' if config.minimax_api_key else '❌ 未配置'}")
        print(f"minimax_group_id: {'已配置' if config.minimax_group_id else '❌ 未配置'}")
        
        if hasattr(config, 'cos_bucket'):
            print(f"cos_bucket: {'已配置' if config.cos_bucket else '❌ 未配置'}")
        if hasattr(config, 'cos_region'):
            print(f"cos_region: {'已配置' if config.cos_region else '❌ 未配置'}")
        
        return bool(config.minimax_api_key and config.minimax_group_id)
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def suggest_configuration():
    """建议配置方法"""
    print("\n📝 配置建议:")
    print("1. 设置环境变量:")
    print("   export MINIMAX_API_KEY='your_api_key_here'")
    print("   export MINIMAX_GROUP_ID='your_group_id_here'")
    print()
    print("2. 或者创建 .env 文件:")
    print("   echo 'MINIMAX_API_KEY=your_api_key_here' > .env")
    print("   echo 'MINIMAX_GROUP_ID=your_group_id_here' >> .env")
    print()
    print("3. 或者直接在 conf.yaml 中设置:")
    print("   MINIMAX_AUDIO:")
    print("     api_key: 'your_api_key_here'")
    print("     group_id: 'your_group_id_here'")


def main():
    """主函数"""
    print("=" * 60)
    print("🔧 MiniMax 配置检查")
    print("=" * 60)
    
    env_ok = check_environment_variables()
    config_ok = check_configuration_loading()
    
    print("\n" + "=" * 60)
    print("📊 检查结果:")
    print("=" * 60)
    
    if env_ok and config_ok:
        print("✅ 配置完整，可以进行真实API测试")
        return True
    else:
        print("❌ 配置不完整")
        suggest_configuration()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
