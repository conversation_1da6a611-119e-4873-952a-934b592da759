#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新版语音克隆工具测试
测试新开发的 voice_clone.py 模块
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parents[1]
sys.path.insert(0, str(project_root))

from src.config.configuration import Configuration
from src.tools.audio.voice_clone import get_voice_clone_tool


def test_tool_creation():
    """测试工具创建"""
    print("🔧 测试工具创建...")
    
    config = Configuration()
    
    # 检查配置
    print(f"MINIMAX_API_KEY: {'已配置' if config.minimax_api_key else '未配置'}")
    print(f"MINIMAX_GROUP_ID: {'已配置' if config.minimax_group_id else '未配置'}")
    
    # 创建工具
    tool = get_voice_clone_tool(config)
    
    if tool:
        print(f"✅ 工具创建成功")
        print(f"📝 工具名称: {tool.name}")
        print(f"📋 工具描述: {tool.description[:100]}...")
        return True
    else:
        print("❌ 工具创建失败 - 可能缺少必要的配置")
        return False


def test_tool_schema():
    """测试工具输入模式"""
    print("\n🔍 测试工具输入模式...")
    
    config = Configuration()
    tool = get_voice_clone_tool(config)
    
    if not tool:
        print("❌ 无法获取工具")
        return False
    
    # 检查输入模式
    schema = tool.args_schema
    if schema:
        print(f"✅ 输入模式: {schema.__name__}")
        
        # 检查必需字段
        fields = schema.__fields__
        required_fields = [name for name, field in fields.items() if field.required]
        optional_fields = [name for name, field in fields.items() if not field.required]
        
        print(f"📋 必需字段: {required_fields}")
        print(f"📋 可选字段: {optional_fields}")
        
        return True
    else:
        print("❌ 无输入模式")
        return False


def test_tool_validation():
    """测试工具参数验证"""
    print("\n✅ 测试参数验证...")
    
    config = Configuration()
    tool = get_voice_clone_tool(config)
    
    if not tool:
        print("❌ 无法获取工具")
        return False
    
    # 测试有效参数
    try:
        valid_params = {
            "audio_file_path": "/path/to/test.mp3",
            "voice_id": "TestVoice123",
            "test_text": "这是一个测试",
            "enable_noise_reduction": True,
            "enable_volume_normalization": True
        }
        
        # 验证参数（不实际执行）
        schema = tool.args_schema
        validated = schema(**valid_params)
        print(f"✅ 有效参数验证通过: {validated.voice_id}")
        
    except Exception as e:
        print(f"❌ 参数验证失败: {e}")
        return False
    
    # 测试无效参数
    try:
        invalid_params = {
            "audio_file_path": "/path/to/test.mp3",
            "voice_id": "123",  # 太短，应该失败
        }
        
        schema = tool.args_schema
        schema(**invalid_params)
        print("❌ 无效参数应该被拒绝")
        return False
        
    except Exception as e:
        print(f"✅ 无效参数正确被拒绝: {str(e)[:50]}...")
    
    return True


def test_tool_integration():
    """测试工具集成"""
    print("\n🔗 测试工具集成...")
    
    try:
        # 测试从主工具模块导入
        from src.tools import voice_clone_tool
        
        if voice_clone_tool:
            print(f"✅ 工具已正确集成到主模块")
            print(f"📝 工具名称: {voice_clone_tool.name}")
            return True
        else:
            print("❌ 工具未在主模块中找到")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🎭 新版语音克隆工具测试")
    print("=" * 60)
    
    tests = [
        ("工具创建", test_tool_creation),
        ("输入模式", test_tool_schema),
        ("参数验证", test_tool_validation),
        ("工具集成", test_tool_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新版语音克隆工具已准备就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和代码。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
