#!/usr/bin/env python3
"""
测试多人TTS工具静态描述修复
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_static_description_fix():
    """测试静态描述修复"""
    logger.info("🔧 测试多人TTS工具静态描述修复...")
    
    try:
        # 读取修复后的文件
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复内容
        fix_checks = {
            "移除了动态函数调用": "_generate_complete_voice_description()" not in content,
            "包含静态音色描述": "🎭 **完整音色库 (46个，男声23个，女声21个)：**" in content,
            "包含青年音色信息": "• 青年音色(8个):" in content,
            "包含专业主持人信息": "• 专业主持人(2个):" in content,
            "包含推荐组合信息": "💡 **推荐声音组合：**" in content,
            "包含professional组合": "• professional: presenter_male + presenter_female" in content,
            "移除了动态函数定义": "def _generate_complete_voice_description" not in content,
            "添加了修复注释": "移除了_generate_complete_voice_description函数" in content
        }
        
        logger.info("🔍 静态描述修复检查:")
        all_passed = True
        for check_name, passed in fix_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}: {'完成' if passed else '未完成'}")
            if not passed:
                all_passed = False
        
        # 检查描述长度
        # 查找工具描述部分
        desc_start = content.find('description=(')
        if desc_start != -1:
            desc_end = content.find('),', desc_start)
            if desc_end != -1:
                description = content[desc_start:desc_end]
                desc_length = len(description)
                logger.info(f"📏 工具描述长度: {desc_length} 字符")
                
                # 检查是否包含问题字符
                problem_chars = {
                    "包含f字符串": "f\"" in description or "f'" in description,
                    "包含函数调用": "()" in description and "generate" in description,
                    "包含动态内容": "{" in description and "}" in description and "f\"" in description
                }
                
                logger.info("🔍 描述内容检查:")
                for check_name, has_problem in problem_chars.items():
                    status = "❌" if has_problem else "✅"
                    logger.info(f"   {status} {check_name}: {'存在问题' if has_problem else '正常'}")
                    if has_problem:
                        all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 静态描述修复测试失败: {e}")
        return False

def test_import_without_circular_dependency():
    """测试导入是否还有循环依赖"""
    logger.info("🔄 测试导入循环依赖...")
    
    try:
        # 尝试直接导入工具创建函数
        import sys
        import importlib
        
        # 清除可能的缓存
        modules_to_clear = [name for name in sys.modules.keys() if name.startswith('src.tools')]
        for module_name in modules_to_clear:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        # 尝试导入
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        
        logger.info("✅ 导入成功，无循环依赖")
        
        # 检查函数是否可调用
        if callable(get_multi_speaker_tts_tool):
            logger.info("✅ 工具创建函数可调用")
        else:
            logger.error("❌ 工具创建函数不可调用")
            return False
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 导入失败，可能仍有循环依赖: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始多人TTS工具静态描述修复测试...")
    
    # 测试步骤
    tests = [
        ("静态描述修复", test_static_description_fix),
        ("导入循环依赖", test_import_without_circular_dependency)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 多人TTS工具静态描述修复测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 多人TTS工具静态描述修复完全成功！")
        logger.info("")
        logger.info("🚀 **修复效果:**")
        logger.info("   ✅ 移除了动态内容生成函数调用")
        logger.info("   ✅ 使用静态的音色描述内容")
        logger.info("   ✅ 避免了运行时动态生成导致的问题")
        logger.info("   ✅ 消除了循环导入问题")
        logger.info("   ✅ 保持了完整的音色信息")
        logger.info("")
        logger.info("🎯 **预期效果:**")
        logger.info("   • 不再出现间歇性JSON解析错误")
        logger.info("   • Audio Agent调用更加稳定")
        logger.info("   • 工具描述内容一致且可预测")
        logger.info("   • 相声剧本任务应该能稳定处理")
        logger.info("")
        logger.info("🎭 现在可以重新测试Audio Agent的多人TTS功能了！")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
