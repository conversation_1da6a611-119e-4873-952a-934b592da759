#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立测试语音克隆工具（避免其他模块依赖）
"""

import os
import sys
from pathlib import Path

# 手动加载.env文件
env_path = Path(__file__).parent / ".env"
if env_path.exists():
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"').strip("'")
                if key in ['MINIMAX_API_KEY', 'MINIMAX_GROUP_ID']:
                    os.environ[key] = value

def test_voice_clone_isolated():
    """独立测试语音克隆工具"""
    print("🛠️ 独立测试语音克隆工具...")
    
    try:
        # 直接导入配置和语音克隆模块
        from src.config.configuration import Configuration
        
        # 直接导入语音克隆模块，避开tools.__init__.py
        sys.path.insert(0, str(Path(__file__).parent / "src" / "tools" / "audio"))
        from voice_clone import get_voice_clone_tool, VoiceCloneInput
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        print(f"配置状态:")
        print(f"- MINIMAX_API_KEY: {'已配置' if config.minimax_api_key else '❌ 未配置'}")
        print(f"- MINIMAX_GROUP_ID: {'已配置' if config.minimax_group_id else '❌ 未配置'}")
        
        if not config.minimax_api_key or not config.minimax_group_id:
            print("❌ 配置不完整，无法测试")
            return False
        
        # 创建工具
        tool = get_voice_clone_tool(config)
        if not tool:
            print("❌ 工具创建失败")
            return False
        
        print(f"✅ 工具创建成功: {tool.name}")
        print(f"📝 工具描述: {tool.description[:100]}...")
        
        # 测试参数验证
        print(f"\n📋 测试参数验证...")
        test_params = {
            "audio_file_path": "https://example.com/test_voice.mp3",
            "voice_id": "IsolatedTest2024",
            "test_text": "这是独立测试语音克隆工具的效果。",
            "enable_noise_reduction": True,
            "enable_volume_normalization": True
        }
        
        # 验证参数
        input_obj = VoiceCloneInput(**test_params)
        print(f"✅ 参数验证通过:")
        print(f"   - 音频URL: {input_obj.audio_file_path}")
        print(f"   - 音色ID: {input_obj.voice_id}")
        print(f"   - 试听文本: {input_obj.test_text}")
        print(f"   - 模型: {input_obj.model}")
        print(f"   - 降噪: {input_obj.enable_noise_reduction}")
        print(f"   - 音量归一化: {input_obj.enable_volume_normalization}")
        
        # 询问是否进行真实API测试
        response = input("\n是否进行真实API测试？需要提供音频URL (y/N): ").strip().lower()
        if response != 'y':
            print("⏭️ 跳过真实API测试")
            return True
        
        # 获取音频URL
        print("\n请提供一个测试音频URL:")
        print("建议：10-30秒的清晰人声，mp3/wav格式，≤20MB")
        print("例如：https://example.com/voice_sample.mp3")
        audio_url = input("音频URL: ").strip()
        
        if not audio_url:
            print("❌ 需要提供音频URL")
            return False
        
        # 更新测试参数
        import time
        test_params["audio_file_path"] = audio_url
        test_params["voice_id"] = f"IsolatedTest_{int(time.time())}"
        
        print(f"\n🧪 开始真实API调用...")
        print(f"参数:")
        for key, value in test_params.items():
            print(f"   - {key}: {value}")
        
        # 调用工具
        print(f"\n⏳ 正在调用MiniMax API...")
        print("   - 上传音频文件...")
        print("   - 处理音色克隆...")
        print("   - 生成试听音频...")
        
        result = tool.func(**test_params)
        
        print(f"\n📊 API调用结果:")
        if isinstance(result, dict):
            if result.get("success"):
                print("🎉 语音克隆成功!")
                print(f"   - 音色ID: {result.get('voice_id')}")
                print(f"   - 主文件ID: {result.get('main_file_id', 'N/A')}")
                print(f"   - 输入敏感检测: {result.get('input_sensitive', 'N/A')}")
                print(f"   - 消息: {result.get('message', '')}")
                
                if result.get('demo_audio_url'):
                    print(f"   - 试听音频: {result.get('demo_audio_url')}")
                    print(f"   - 试听文本: {result.get('demo_audio_text', 'N/A')}")
                
                # 显示使用信息
                usage_info = result.get('usage_info', {})
                if usage_info:
                    print(f"\n📋 使用信息:")
                    print(f"   - 音色ID: {usage_info.get('voice_id', 'N/A')}")
                    print(f"   - 有效期: {usage_info.get('valid_period', 'N/A')}")
                    print(f"   - 使用说明: {usage_info.get('usage_note', 'N/A')}")
                    compatible_apis = usage_info.get('compatible_apis', [])
                    if compatible_apis:
                        print(f"   - 兼容API: {', '.join(compatible_apis)}")
                
                print(f"\n🎯 后续使用:")
                print(f"   1. 在语音合成工具中使用音色ID: {result.get('voice_id')}")
                print(f"   2. 音色将在7天后自动删除")
                print(f"   3. 可用于同步和异步语音合成")
                
                return True
            else:
                print("❌ 语音克隆失败:")
                error_msg = result.get('error', 'Unknown error')
                print(f"   - 错误: {error_msg}")
                
                # 提供错误解决建议
                if "文件格式" in error_msg:
                    print("   💡 建议：请使用mp3、m4a或wav格式的音频文件")
                elif "文件太大" in error_msg:
                    print("   💡 建议：请使用小于20MB的音频文件")
                elif "下载失败" in error_msg:
                    print("   💡 建议：请检查URL是否可访问，或尝试其他音频URL")
                elif "API" in error_msg:
                    print("   💡 建议：请检查MiniMax API配置和网络连接")
                
                return False
        else:
            print(f"❌ 意外的返回格式: {type(result)}")
            print(f"   - 内容: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 独立工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🎭 语音克隆工具独立测试")
    print("=" * 60)
    
    success = test_voice_clone_isolated()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print("=" * 60)
    
    if success:
        print("🎉 语音克隆工具测试成功！")
        print("\n📝 确认项目:")
        print("- ✅ 配置正确加载")
        print("- ✅ 工具创建成功")
        print("- ✅ 参数验证正确")
        print("- ✅ 支持URL音频输入")
        print("- ✅ 模型固定为 speech-02-hd")
        print("- ✅ 真实API功能正常")
        print("- ✅ 返回格式完整")
        print("- ✅ 错误处理完善")
        print("\n🚀 工具已准备好在DeerFlow系统中使用！")
        print("\n📋 下一步:")
        print("1. 安装缺少的依赖：pip install soundfile")
        print("2. 在Agent中测试完整集成")
        print("3. 在实际创作任务中使用")
        return True
    else:
        print("❌ 测试失败，请检查配置和网络连接。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
