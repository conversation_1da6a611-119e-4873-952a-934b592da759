#!/usr/bin/env python3
"""
测试音频理解工具的集成
"""

import logging
import asyncio

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_audio_understanding_tool_creation():
    """测试音频理解工具创建"""
    logger.info("🎤 测试音频理解工具创建...")
    
    try:
        from src.config.configuration import Configuration
        from src.tools.understanding.audio_understanding import get_audio_understanding_tool
        
        config = Configuration.from_runnable_config()
        
        # 检查配置
        has_api_key = bool(getattr(config, 'openai_api_key', None))
        logger.info(f"📋 API密钥配置: {'有' if has_api_key else '无'}")
        
        # 尝试创建工具
        tool = get_audio_understanding_tool(config)
        
        if tool is None:
            logger.warning("⚠️ 音频理解工具返回None（可能缺少API配置）")
            if not has_api_key:
                logger.info("📋 原因: 缺少API密钥配置（这是正常的）")
                return True
            else:
                logger.error("📋 原因: 未知（配置存在但工具仍返回None）")
                return False
        else:
            logger.info("✅ 音频理解工具创建成功")
            logger.info(f"📋 工具名称: {tool.name}")
            logger.info(f"📋 工具类型: {type(tool).__name__}")
            logger.info(f"📋 描述长度: {len(tool.description)} 字符")
            
            # 检查工具的基本属性
            attrs_to_check = ['name', 'description', 'func', 'coroutine', 'args_schema']
            for attr in attrs_to_check:
                if hasattr(tool, attr):
                    value = getattr(tool, attr)
                    logger.info(f"📋 {attr}: {type(value).__name__}")
                else:
                    logger.warning(f"⚠️ 缺少属性: {attr}")
            
            # 测试args_schema
            if hasattr(tool, 'args_schema'):
                try:
                    schema = tool.args_schema
                    logger.info(f"✅ args_schema: {schema.__name__}")
                    
                    # 测试创建一个实例
                    test_instance = schema(
                        audio_url="https://example.com/test.mp3",
                        analysis_type="transcription"
                    )
                    logger.info("✅ args_schema实例创建成功")
                    
                    # 测试序列化
                    instance_dict = test_instance.dict()
                    import json
                    json_str = json.dumps(instance_dict, ensure_ascii=False)
                    logger.info(f"✅ args_schema JSON序列化成功: {len(json_str)} 字符")
                    
                except Exception as e:
                    logger.error(f"❌ args_schema测试失败: {e}")
                    return False
            
            return True
        
    except Exception as e:
        logger.error(f"❌ 音频理解工具创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_understanding_module_integration():
    """测试understanding模块集成"""
    logger.info("📦 测试understanding模块集成...")
    
    try:
        # 测试从understanding模块导入
        from src.tools.understanding import get_audio_understanding_tool
        logger.info("✅ 从understanding模块导入成功")
        
        # 测试__all__列表
        from src.tools.understanding import __all__
        if 'get_audio_understanding_tool' in __all__:
            logger.info("✅ get_audio_understanding_tool在__all__列表中")
        else:
            logger.error("❌ get_audio_understanding_tool不在__all__列表中")
            return False
        
        # 测试工具创建
        from src.config.configuration import Configuration
        config = Configuration.from_runnable_config()
        tool = get_audio_understanding_tool(config)
        
        if tool is None:
            logger.info("✅ 工具正确返回None（配置缺失时的预期行为）")
        else:
            logger.info("✅ 工具创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ understanding模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_v2_architecture_integration():
    """测试V2架构集成"""
    logger.info("🏗️ 测试V2架构集成...")
    
    try:
        from src.graph_v2.builder import GraphBuilder
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        import os
        
        # 确保db目录存在
        os.makedirs("db", exist_ok=True)
        
        async with AsyncSqliteSaver.from_conn_string("db/test_audio_checkpoints.sqlite") as memory:
            # 创建V2图
            builder = GraphBuilder()
            graph = builder.build(checkpointer=memory)
            
            logger.info("✅ V2 Graph构建成功")
            
            # 测试简单调用
            test_input = {
                "messages": [("human", "请分析这个音频文件的内容")]
            }
            
            config = {"configurable": {"thread_id": "test_audio_thread"}}
            
            logger.info("🚀 开始测试V2 Graph调用...")
            
            # 只运行一步来检查工具是否可用
            step_count = 0
            async for event in graph.astream(test_input, config=config, stream_mode="updates"):
                step_count += 1
                node_name, node_output = next(iter(event.items()))
                
                logger.info(f"📋 步骤 {step_count}: {node_name}")
                
                # 检查消息
                messages = node_output.get("messages", [])
                if messages:
                    last_message = messages[-1]
                    
                    # 检查工具调用
                    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                        for tool_call in last_message.tool_calls:
                            tool_name = tool_call['name']
                            logger.info(f"🔧 调用工具: {tool_name}")
                            
                            # 检查是否有音频理解相关的工具
                            if 'audio' in tool_name.lower():
                                logger.info("✅ 发现音频相关工具调用")
                                return True
                
                # 限制测试步骤数
                if step_count >= 3:
                    logger.info("📋 测试步骤已足够，停止测试")
                    break
            
            logger.info("✅ V2架构集成测试完成")
            return True
        
    except Exception as e:
        logger.error(f"❌ V2架构集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_description_optimization():
    """测试工具描述优化"""
    logger.info("📝 测试工具描述优化...")
    
    try:
        from src.config.configuration import Configuration
        from src.tools.understanding.audio_understanding import get_audio_understanding_tool
        
        config = Configuration.from_runnable_config()
        tool = get_audio_understanding_tool(config)
        
        if tool is None:
            logger.info("✅ 工具正确返回None，跳过描述测试")
            return True
        
        description = tool.description
        desc_length = len(description)
        
        logger.info(f"📏 描述长度: {desc_length} 字符")
        logger.info(f"📝 描述内容: {repr(description[:100])}...")
        
        # 检查描述特征
        checks = {
            "长度合理": desc_length <= 1000,
            "包含audio": "audio" in description.lower(),
            "包含understanding": "understanding" in description.lower(),
            "包含transcription": "transcription" in description.lower(),
            "不包含emoji": not any(ord(c) > 1000 for c in description),
            "不包含中文": not any('\u4e00' <= c <= '\u9fff' for c in description),
            "不包含复杂格式": '**' not in description and '•' not in description,
            "使用英文": any(c.isalpha() for c in description)
        }
        
        logger.info("🔍 描述特征检查:")
        all_good = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                all_good = False
        
        return all_good
        
    except Exception as e:
        logger.error(f"❌ 工具描述优化测试失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🎯 开始音频理解工具集成测试...")
    logger.info("目标: 验证音频理解工具的完整集成")
    
    tests = [
        ("音频理解工具创建", test_audio_understanding_tool_creation, False),
        ("understanding模块集成", test_understanding_module_integration, False),
        ("工具描述优化", test_tool_description_optimization, False),
        ("V2架构集成", test_v2_architecture_integration, True)
    ]
    
    results = {}
    
    for test_name, test_func, is_async in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 音频理解工具集成测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("\n🎉 音频理解工具集成完全成功！")
        logger.info("")
        logger.info("🚀 **新增功能:**")
        logger.info("   ✅ 音频转录（Whisper API）")
        logger.info("   ✅ 音频内容分析")
        logger.info("   ✅ 情感分析")
        logger.info("   ✅ 场景识别")
        logger.info("   ✅ 综合分析")
        logger.info("")
        logger.info("🔧 **技术特性:**")
        logger.info("   • 支持多种音频格式（mp3, wav, m4a, flac, ogg）")
        logger.info("   • 基于OpenAI Whisper API进行转录")
        logger.info("   • 使用Chat API进行智能内容分析")
        logger.info("   • 异步支持和错误处理")
        logger.info("   • 符合DeerFlow开发规范")
        logger.info("")
        logger.info("🎭 **现在可以使用了！**")
        logger.info("   在interactive_chat.py中测试:")
        logger.info("   • '请分析这个音频文件的内容'")
        logger.info("   • '帮我转录这段语音'")
        logger.info("   • '分析这个音频的情感色彩'")
        logger.info("")
        logger.info("🌟 **与其他工具的协同:**")
        logger.info("   • 可以与multi_speaker_tts配合使用")
        logger.info("   • 支持音频内容理解后的二次创作")
        logger.info("   • 为音频处理工作流提供理解能力")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
