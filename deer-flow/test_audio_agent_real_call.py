#!/usr/bin/env python3
"""
模拟实际的Audio Agent调用，检查multi_speaker_tts工具是否真的可用
"""

import logging
import asyncio

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_real_audio_agent_call():
    """模拟真实的Audio Agent调用"""
    logger.info("🎵 模拟真实Audio Agent调用...")
    
    try:
        # 导入Audio Agent
        from src.agents.agents import audio_creator_agent
        
        logger.info("✅ Audio Agent导入成功")
        
        # 创建测试输入 - 明确要求多人TTS
        test_messages = [
            {
                "role": "user", 
                "content": "帮我生成一个两人相声剧本的音频，一个是逗哏的，一个是捧哏的，各说几句话"
            }
        ]
        
        # 创建初始状态
        initial_state = {
            "messages": test_messages
        }
        
        logger.info("📋 测试输入:")
        logger.info(f"   用户请求: {test_messages[0]['content']}")
        
        # 尝试调用Audio Agent
        logger.info("\n🚀 开始调用Audio Agent...")
        
        try:
            # 使用invoke方法调用
            result = await audio_creator_agent.ainvoke(initial_state)
            
            logger.info("✅ Audio Agent调用成功")
            logger.info(f"📋 返回结果类型: {type(result)}")
            
            # 检查返回结果
            if isinstance(result, dict):
                if 'messages' in result:
                    messages = result['messages']
                    logger.info(f"📋 返回消息数量: {len(messages)}")
                    
                    # 查看最后的消息
                    if messages:
                        last_message = messages[-1]
                        logger.info(f"📋 最后消息角色: {last_message.get('role', 'unknown')}")
                        content = last_message.get('content', '')
                        logger.info(f"📋 最后消息内容长度: {len(content)} 字符")
                        logger.info(f"📋 最后消息内容预览: {content[:200]}...")
                        
                        # 检查是否提到了multi_speaker_tts
                        if 'multi_speaker_tts' in content.lower():
                            logger.info("✅ 发现multi_speaker_tts工具被使用")
                        else:
                            logger.warning("⚠️ 未发现multi_speaker_tts工具被使用")
                        
                        # 检查是否有错误信息
                        if 'error' in content.lower() or 'failed' in content.lower():
                            logger.warning(f"⚠️ 可能的错误信息: {content}")
                
                # 检查其他字段
                for key, value in result.items():
                    if key != 'messages':
                        logger.info(f"📋 结果字段 {key}: {type(value)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Audio Agent调用失败: {e}")
            
            # 检查错误信息中是否有工具相关的信息
            error_str = str(e)
            if 'tool' in error_str.lower():
                logger.error(f"🔧 工具相关错误: {error_str}")
            if 'json' in error_str.lower():
                logger.error(f"📄 JSON相关错误: {error_str}")
            if 'multi_speaker' in error_str.lower():
                logger.error(f"🎭 multi_speaker相关错误: {error_str}")
            
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试准备失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_agent_tools_inspection():
    """检查Audio Agent的工具配置"""
    logger.info("🔍 检查Audio Agent的工具配置...")
    
    try:
        from src.agents.agents import audio_creator_agent
        
        # 检查Agent的类型和属性
        logger.info(f"📋 Agent类型: {type(audio_creator_agent)}")
        
        # 尝试不同方法获取工具信息
        methods = [
            ('tools', lambda agent: getattr(agent, 'tools', None)),
            ('graph.nodes', lambda agent: getattr(agent.graph, 'nodes', None) if hasattr(agent, 'graph') else None),
            ('runnable', lambda agent: getattr(agent, 'runnable', None)),
            ('config', lambda agent: getattr(agent, 'config', None))
        ]
        
        for method_name, method_func in methods:
            try:
                result = method_func(audio_creator_agent)
                if result is not None:
                    logger.info(f"✅ {method_name}: {type(result)} - {len(result) if hasattr(result, '__len__') else 'N/A'}")
                    
                    # 如果是工具列表，显示工具名称
                    if method_name == 'tools' and hasattr(result, '__iter__'):
                        for i, tool in enumerate(result):
                            tool_name = getattr(tool, 'name', f'Tool_{i}')
                            logger.info(f"   {i+1}. {tool_name}")
                    
                    # 如果是graph节点，显示节点名称
                    elif method_name == 'graph.nodes' and hasattr(result, 'keys'):
                        logger.info(f"   节点: {list(result.keys())}")
                else:
                    logger.info(f"❌ {method_name}: None")
            except Exception as e:
                logger.warning(f"⚠️ {method_name}: 获取失败 - {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent工具检查失败: {e}")
        return False

def test_direct_tool_call():
    """直接测试multi_speaker_tts工具调用"""
    logger.info("🎭 直接测试multi_speaker_tts工具调用...")
    
    try:
        from src.config.configuration import Configuration
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        
        config = Configuration.from_runnable_config()
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            logger.warning("⚠️ multi_speaker_tts工具为None，跳过直接调用测试")
            return True
        
        logger.info("✅ multi_speaker_tts工具创建成功")
        
        # 准备测试输入
        test_input = {
            "dialogue_script": [
                {"speaker": "逗哏", "text": "今天天气真不错啊"},
                {"speaker": "捧哏", "text": "是啊，阳光明媚的"}
            ]
        }
        
        logger.info("📋 准备测试调用...")
        logger.info(f"   输入: {test_input}")
        
        # 注意：这里我们不真正调用，因为可能需要API密钥
        # 但我们可以测试工具的基本结构
        
        # 检查工具是否有正确的调用方法
        if hasattr(tool, 'func'):
            logger.info("✅ 工具有func方法")
        if hasattr(tool, 'coroutine'):
            logger.info("✅ 工具有coroutine方法")
        if hasattr(tool, 'args_schema'):
            logger.info("✅ 工具有args_schema")
            
            # 测试args_schema验证
            try:
                schema = tool.args_schema
                validated_input = schema(**test_input)
                logger.info("✅ 输入验证成功")
                logger.info(f"   验证后的输入: {validated_input.dict()}")
            except Exception as e:
                logger.error(f"❌ 输入验证失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 直接工具调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    logger.info("🎯 开始Audio Agent真实调用测试...")
    logger.info("目标: 模拟实际使用场景，找出multi_speaker_tts工具问题")
    
    tests = [
        ("Agent工具配置检查", test_audio_agent_tools_inspection, False),
        ("直接工具调用测试", test_direct_tool_call, False),
        ("真实Agent调用测试", test_real_audio_agent_call, True)
    ]
    
    results = {}
    
    for test_name, test_func, is_async in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 Audio Agent真实调用测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count < total_count:
        logger.warning("\n⚠️ 发现问题！")
        logger.info("🔍 可能的原因:")
        logger.info("   1. multi_speaker_tts工具没有被正确加载到Agent")
        logger.info("   2. Agent在运行时过滤掉了某些工具")
        logger.info("   3. 工具调用过程中出现错误")
        logger.info("   4. LangGraph配置或路由问题")
        logger.info("   5. JSON序列化仍然有问题")
    else:
        logger.info("\n🎉 所有测试通过！")
        logger.info("如果multi_speaker_tts仍然不工作，可能是运行时的其他问题")

if __name__ == "__main__":
    asyncio.run(main())
