#!/usr/bin/env python3
"""
验证音色数量统计是否正确
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 直接从配置文件复制的音色配置
MINIMAX_SYSTEM_VOICES = {
    # === 男性青年音色 ===
    "male-qn-qingse": {"name": "青涩青年音色", "gender": "male", "category": "youth"},
    "male-qn-jingying": {"name": "精英青年音色", "gender": "male", "category": "youth"},
    "male-qn-badao": {"name": "霸道青年音色", "gender": "male", "category": "youth"},
    "male-qn-daxuesheng": {"name": "青年大学生音色", "gender": "male", "category": "youth"},

    # === 女性青年音色 ===
    "female-shaonv": {"name": "少女音色", "gender": "female", "category": "youth"},
    "female-yujie": {"name": "御姐音色", "gender": "female", "category": "youth"},
    "female-chengshu": {"name": "成熟女性音色", "gender": "female", "category": "youth"},
    "female-tianmei": {"name": "甜美女性音色", "gender": "female", "category": "youth"},

    # === 主持人音色 ===
    "presenter_male": {"name": "男性主持人", "gender": "male", "category": "presenter"},
    "presenter_female": {"name": "女性主持人", "gender": "female", "category": "presenter"},

    # === 有声书音色 ===
    "audiobook_male_1": {"name": "男性有声书1", "gender": "male", "category": "audiobook"},
    "audiobook_male_2": {"name": "男性有声书2", "gender": "male", "category": "audiobook"},
    "audiobook_female_1": {"name": "女性有声书1", "gender": "female", "category": "audiobook"},
    "audiobook_female_2": {"name": "女性有声书2", "gender": "female", "category": "audiobook"},

    # === 精品版音色(beta) ===
    "male-qn-qingse-jingpin": {"name": "青涩青年音色-beta", "gender": "male", "category": "premium"},
    "male-qn-jingying-jingpin": {"name": "精英青年音色-beta", "gender": "male", "category": "premium"},
    "male-qn-badao-jingpin": {"name": "霸道青年音色-beta", "gender": "male", "category": "premium"},
    "male-qn-daxuesheng-jingpin": {"name": "青年大学生音色-beta", "gender": "male", "category": "premium"},
    "female-shaonv-jingpin": {"name": "少女音色-beta", "gender": "female", "category": "premium"},
    "female-yujie-jingpin": {"name": "御姐音色-beta", "gender": "female", "category": "premium"},
    "female-chengshu-jingpin": {"name": "成熟女性音色-beta", "gender": "female", "category": "premium"},
    "female-tianmei-jingpin": {"name": "甜美女性音色-beta", "gender": "female", "category": "premium"},

    # === 儿童音色 ===
    "clever_boy": {"name": "聪明男童", "gender": "male", "category": "child"},
    "cute_boy": {"name": "可爱男童", "gender": "male", "category": "child"},
    "lovely_girl": {"name": "萌萌女童", "gender": "female", "category": "child"},

    # === 角色扮演音色 ===
    "cartoon_pig": {"name": "卡通猪小琪", "gender": "neutral", "category": "character"},
    "bingjiao_didi": {"name": "病娇弟弟", "gender": "male", "category": "character"},
    "junlang_nanyou": {"name": "俊朗男友", "gender": "male", "category": "character"},
    "chunzhen_xuedi": {"name": "纯真学弟", "gender": "male", "category": "character"},
    "lengdan_xiongzhang": {"name": "冷淡学长", "gender": "male", "category": "character"},
    "badao_shaoye": {"name": "霸道少爷", "gender": "male", "category": "character"},
    "tianxin_xiaoling": {"name": "甜心小玲", "gender": "female", "category": "character"},
    "qiaopi_mengmei": {"name": "俏皮萌妹", "gender": "female", "category": "character"},
    "wumei_yujie": {"name": "妩媚御姐", "gender": "female", "category": "character"},
    "diadia_xuemei": {"name": "嗲嗲学妹", "gender": "female", "category": "character"},
    "danya_xuejie": {"name": "淡雅学姐", "gender": "female", "category": "character"},

    # === 英文角色 ===
    "Santa_Claus": {"name": "Santa Claus", "gender": "male", "category": "english"},
    "Grinch": {"name": "Grinch", "gender": "male", "category": "english"},
    "Rudolph": {"name": "Rudolph", "gender": "male", "category": "english"},
    "Arnold": {"name": "Arnold", "gender": "male", "category": "english"},
    "Charming_Santa": {"name": "Charming Santa", "gender": "male", "category": "english"},
    "Charming_Lady": {"name": "Charming Lady", "gender": "female", "category": "english"},
    "Sweet_Girl": {"name": "Sweet Girl", "gender": "female", "category": "english"},
    "Cute_Elf": {"name": "Cute Elf", "gender": "neutral", "category": "english"},
    "Attractive_Girl": {"name": "Attractive Girl", "gender": "female", "category": "english"},
    "Serene_Woman": {"name": "Serene Woman", "gender": "female", "category": "english"}
}

def verify_voice_counts():
    """验证音色数量统计"""
    logger.info("🔍 验证音色数量统计...")
    
    # 总数统计
    total_count = len(MINIMAX_SYSTEM_VOICES)
    male_count = len([v for v in MINIMAX_SYSTEM_VOICES.values() if v["gender"] == "male"])
    female_count = len([v for v in MINIMAX_SYSTEM_VOICES.values() if v["gender"] == "female"])
    neutral_count = len([v for v in MINIMAX_SYSTEM_VOICES.values() if v["gender"] == "neutral"])
    
    logger.info(f"📊 总体统计:")
    logger.info(f"   总音色数: {total_count}")
    logger.info(f"   男声: {male_count}")
    logger.info(f"   女声: {female_count}")
    logger.info(f"   中性: {neutral_count}")
    
    # 按分类统计
    categories = {}
    for voice_id, info in MINIMAX_SYSTEM_VOICES.items():
        category = info["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append(voice_id)
    
    logger.info(f"\n📋 分类统计:")
    for category, voices in categories.items():
        logger.info(f"   {category}: {len(voices)}个")
        logger.info(f"      {', '.join(voices)}")
    
    # 验证工具描述中的数量
    expected_counts = {
        "total": 46,
        "male": 23,
        "female": 21,
        "neutral": 2,
        "youth": 8,
        "presenter": 2,
        "audiobook": 4,
        "premium": 8,
        "child": 3,
        "character": 11,
        "english": 10
    }
    
    actual_counts = {
        "total": total_count,
        "male": male_count,
        "female": female_count,
        "neutral": neutral_count,
        "youth": len(categories.get("youth", [])),
        "presenter": len(categories.get("presenter", [])),
        "audiobook": len(categories.get("audiobook", [])),
        "premium": len(categories.get("premium", [])),
        "child": len(categories.get("child", [])),
        "character": len(categories.get("character", [])),
        "english": len(categories.get("english", []))
    }
    
    logger.info(f"\n✅ 数量验证:")
    all_correct = True
    for key, expected in expected_counts.items():
        actual = actual_counts[key]
        status = "✅" if actual == expected else "❌"
        logger.info(f"   {status} {key}: 期望{expected}, 实际{actual}")
        if actual != expected:
            all_correct = False
    
    return all_correct

def main():
    """主函数"""
    logger.info("🎯 开始音色数量验证...")
    
    result = verify_voice_counts()
    
    if result:
        logger.info("\n🎉 音色数量统计完全正确！")
        logger.info("✅ 工具描述中的数量与实际配置完全匹配")
    else:
        logger.warning("\n⚠️ 音色数量统计有误，需要更新工具描述")

if __name__ == "__main__":
    main()
