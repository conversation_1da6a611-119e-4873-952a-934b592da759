# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
factory.py

This module contains the factory function for creating standard ReAct agents.
It is separated to prevent circular dependencies between the 'agents' and 'tools' modules.
"""

from langgraph.prebuilt import create_react_agent

from src.config.agents import AGENT_LLM_MAP
from src.llms.llm import get_llm_by_type
from src.prompts.template import apply_prompt_template


def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template_name: str):
    """
    Factory function to create agents with consistent configuration.
    
    This function creates a ReAct-style agent, which is a common pattern for agents
    that need to reason and use tools in a loop.
    """
    return create_react_agent(
        name=agent_name,
        model=get_llm_by_type(AGENT_LLM_MAP[agent_type]),
        tools=tools,
        prompt=lambda state: apply_prompt_template(prompt_template_name, state),
    ) 