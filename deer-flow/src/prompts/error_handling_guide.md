# DeerFlow 错误处理指南

## 通用错误处理原则

### 1. 错误分类
**API错误**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：认证失败
- `403 Forbidden`：权限不足
- `429 Too Many Requests`：请求频率限制
- `500 Internal Server Error`：服务器内部错误

**系统错误**：
- 网络连接问题
- 文件访问错误
- 内存不足
- 超时错误

**逻辑错误**：
- 工具选择错误
- 参数构建错误
- 依赖关系错误

### 2. 错误处理策略

#### 立即重试 (Immediate Retry)
**适用场景**：
- 网络临时中断
- 服务器临时不可用
- 随机性错误

**重试参数**：
- 最大重试次数：3次
- 重试间隔：1秒、3秒、10秒（指数退避）

#### 参数调整重试 (Parameter Adjustment Retry)
**适用场景**：
- 参数格式错误
- 数值超出范围
- 枚举值不正确

**调整策略**：
- 检查必填字段
- 修正数值范围
- 使用默认值替换无效值

#### 工具切换 (Tool Switching)
**适用场景**：
- 特定工具不可用
- API配额耗尽
- 功能限制

**切换原则**：
- 选择功能最相似的备用工具
- 调整参数以适配新工具
- 保持输出质量标准

#### 降级处理 (Graceful Degradation)
**适用场景**：
- 所有重试失败
- 无可用备用工具
- 严重系统错误

**降级策略**：
- 简化需求
- 使用基础功能
- 提供部分结果

## 具体错误处理方案

### Master Agent 错误处理

#### 状态管理工具错误
```
错误：get_current_plan 调用失败
处理：
1. 重试3次
2. 如果仍失败，假设无计划状态
3. 继续正常流程
```

#### 专家工具调用错误
```
错误：visual_expert 返回失败
处理：
1. 分析错误类型
2. 调整参数重试
3. 如果仍失败，更新步骤状态为"failed"
4. 在下次循环中重新规划
```

### 专家Agent 错误处理

#### 图像生成工具错误
```
错误：jmeng_image_generation 返回400
处理：
1. 检查prompt长度和格式
2. 验证width/height值
3. 调整scale参数到默认值
4. 重试调用
```

#### 文件访问错误
```
错误：无法访问base_image
处理：
1. 检查URL格式
2. 尝试重新下载
3. 如果失败，报告错误并建议用户重新上传
```

### 规划工具错误处理

#### 计划生成失败
```
错误：planner_tool 生成无效JSON
处理：
1. 重新调用planner_tool
2. 简化任务描述
3. 如果仍失败，切换到直接执行模式
```

## 错误报告格式

### 标准错误报告
```json
{
  "success": false,
  "error": {
    "type": "api_error",
    "code": "400",
    "message": "Invalid parameter: prompt too long",
    "details": {
      "tool": "jmeng_image_generation",
      "parameter": "prompt",
      "max_length": 1000,
      "actual_length": 1500
    }
  },
  "retry_info": {
    "attempted": 2,
    "max_retries": 3,
    "next_retry_in": 3
  },
  "suggestions": [
    "Shorten the prompt to under 1000 characters",
    "Split complex descriptions into multiple calls",
    "Use more concise language"
  ]
}
```

### 用户友好错误消息
```
技术错误：API参数过长
用户消息：您的描述太详细了，我需要简化一下描述来生成图片。
建议操作：我会自动调整描述长度并重试。
```

## 性能优化策略

### 1. 缓存机制
- 缓存常用模板
- 缓存API响应（适当时）
- 缓存文件下载

### 2. 并发控制
- 限制同时进行的API调用
- 实现请求队列
- 避免API频率限制

### 3. 超时管理
- 设置合理的超时时间
- 实现超时重试
- 提供进度反馈

### 4. 资源管理
- 及时清理临时文件
- 监控内存使用
- 优化大文件处理

## 监控和日志

### 错误监控指标
- 错误率（按工具分类）
- 重试成功率
- 平均响应时间
- 资源使用情况

### 日志记录要求
- 记录所有错误详情
- 记录重试过程
- 记录性能指标
- 保护敏感信息

## 最佳实践

1. **预防性检查**：在调用工具前验证参数
2. **优雅降级**：确保系统在部分功能失败时仍可用
3. **用户体验**：提供清晰的错误信息和解决建议
4. **持续改进**：基于错误模式优化系统
5. **文档更新**：及时更新错误处理文档
