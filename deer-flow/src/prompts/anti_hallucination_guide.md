# DeerFlow 反幻觉指导文档

## 🚨 核心问题：Agent幻觉

### 问题描述
子Agent经常出现**幻觉行为**：
- 没有实际调用工具
- 返回虚假的mock URL
- 编造不存在的文件路径
- 伪造工具执行结果

### 根本原因
1. **错误的示例引导**：prompt中包含mock URL示例
2. **缺乏强制约束**：没有明确要求必须先调用工具
3. **输出格式设计缺陷**：允许Agent先描述再输出结果

## 🎯 反幻觉策略

### 1. 强制工具调用原则

**在每个专家Agent的prompt开头添加**：
```markdown
**🚨 核心原则：工具优先，结果真实**
- 你**必须**先调用相应的工具完成实际操作
- 你**绝对禁止**在没有调用工具的情况下编造URL、文件路径或其他虚假信息
- 你的所有输出必须基于工具的真实返回结果
```

### 2. 移除所有Mock示例

**错误示例（会导致幻觉）**：
```json
{
  "url": "https://example.com/image.jpg",
  "audio": "http://path/to/audio.mp3",
  "video": "https://xxx.cdn.com/video.mp4"
}
```

**正确示例**：
```json
{
  "url": "[工具实际返回的真实URL]",
  "audio": "[工具实际返回的真实音频URL]", 
  "video": "[工具实际返回的真实视频URL]"
}
```

### 3. 强化输出验证

**在资产信息部分强调**：
```markdown
> **🚨 重要**: 此JSON必须基于工具的**实际执行结果**填充。**绝对禁止**编造URL或路径。
```

### 4. 工具调用流程强制化

**标准流程**：
```
1. 分析任务需求
2. 选择合适工具
3. 构建正确参数
4. 调用工具执行
5. 基于真实结果组织回复
6. 输出标准化资产信息
```

**禁止的行为**：
```
❌ 直接编写自然语言描述
❌ 跳过工具调用步骤
❌ 使用示例或模板URL
❌ 伪造执行结果
```

## 🔧 具体修复措施

### Visual Expert修复
```markdown
### **重要：工具调用优先原则**

**你必须严格遵循以下流程**：
1. **首先**：根据任务需求调用相应的工具
2. **然后**：基于工具的**真实返回结果**组织你的回复
3. **绝对禁止**：在没有调用工具的情况下编造URL或文件路径
```

### Audio Expert修复
```markdown
**🚨 核心原则：工具优先，结果真实**
- 你**必须**先调用相应的工具完成实际音频生成
- 你**绝对禁止**在没有调用工具的情况下编造音频URL、文件路径或voice_id
- 你的所有输出必须基于工具的真实返回结果
```

### Video Expert修复
```markdown
**🚨 核心原则：工具优先，结果真实**
- 你**必须**先调用相应的工具完成实际视频生成
- 你**绝对禁止**在没有调用工具的情况下编造视频URL、文件路径或其他虚假信息
- 你的所有输出必须基于工具的真实返回结果
```

## 🧪 测试验证

### 幻觉检测测试
```python
def test_no_hallucination(agent_response):
    """检测Agent是否产生幻觉"""
    
    # 检查是否包含mock URL
    mock_patterns = [
        r'example\.com',
        r'xxx\.cdn\.com',
        r'path/to/',
        r'http://.*\.mp3',
        r'https://.*\.jpg'
    ]
    
    for pattern in mock_patterns:
        if re.search(pattern, agent_response):
            return False, f"发现mock URL: {pattern}"
    
    # 检查是否实际调用了工具
    if not has_tool_call_in_response(agent_response):
        return False, "没有检测到工具调用"
    
    return True, "通过幻觉检测"
```

### 工具调用验证
```python
def verify_tool_execution(agent_response, expected_tool):
    """验证Agent是否正确调用了工具"""
    
    # 检查工具调用记录
    tool_calls = extract_tool_calls(agent_response)
    
    if not tool_calls:
        return False, "没有工具调用记录"
    
    if expected_tool not in [call.name for call in tool_calls]:
        return False, f"没有调用期望的工具: {expected_tool}"
    
    # 检查返回结果是否基于真实工具输出
    for call in tool_calls:
        if not validate_tool_output(call):
            return False, f"工具 {call.name} 的输出无效"
    
    return True, "工具调用验证通过"
```

## 📊 监控指标

### 关键指标
- **幻觉率**：返回mock URL的比例
- **工具调用率**：实际调用工具的比例  
- **结果真实性**：输出URL的可访问性
- **任务完成率**：成功完成任务的比例

### 监控代码
```python
class HallucinationMonitor:
    def __init__(self):
        self.hallucination_count = 0
        self.total_responses = 0
        self.mock_url_patterns = [
            r'example\.com',
            r'xxx\.cdn\.com', 
            r'path/to/',
            r'http://.*\.(mp3|jpg|mp4)',
        ]
    
    def check_response(self, response):
        self.total_responses += 1
        
        for pattern in self.mock_url_patterns:
            if re.search(pattern, response):
                self.hallucination_count += 1
                self.log_hallucination(response, pattern)
                return False
        
        return True
    
    def get_hallucination_rate(self):
        if self.total_responses == 0:
            return 0
        return self.hallucination_count / self.total_responses
```

## 🎯 成功标准

### 短期目标（1周内）
- [ ] 幻觉率降低到5%以下
- [ ] 工具调用率达到95%以上
- [ ] 所有mock URL示例已移除

### 中期目标（1个月内）
- [ ] 幻觉率降低到1%以下
- [ ] 建立自动化幻觉检测机制
- [ ] 完善监控和告警系统

### 长期目标（3个月内）
- [ ] 零幻觉目标
- [ ] 建立反幻觉最佳实践
- [ ] 形成标准化的验证流程

## 🔄 持续改进

### 定期审查
- 每周审查幻觉案例
- 分析幻觉产生的根本原因
- 持续优化prompt设计

### 反馈循环
- 收集用户反馈
- 监控系统指标
- 及时调整策略

### 知识积累
- 记录典型幻觉案例
- 总结有效的防范措施
- 建立知识库和最佳实践

---

**记住：真实性是AI系统的生命线，绝不允许任何形式的幻觉行为！**
