# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_models.py

This module defines the Pydantic models for the template system.
Templates are used to create standardized plans for common video creation workflows.
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from .models import Plan, Step, StepStatus
from .enhanced_models import StepType


class ParameterType(str, Enum):
    """Supported parameter types for template parameters."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"


class ParameterSchema(BaseModel):
    """Schema definition for template parameters."""
    type: ParameterType = Field(..., description="Parameter type")
    required: bool = Field(default=True, description="Whether this parameter is required")
    default: Any = Field(default=None, description="Default value if not provided")
    description: str = Field(default="", description="Human-readable description")
    options: Optional[List[Any]] = Field(default=None, description="Allowed values (for enum-like parameters)")
    min_value: Optional[Union[int, float]] = Field(default=None, description="Minimum value for numeric types")
    max_value: Optional[Union[int, float]] = Field(default=None, description="Maximum value for numeric types")
    pattern: Optional[str] = Field(default=None, description="Regex pattern for string validation")


class StepTemplate(BaseModel):
    """Template for a single step in a plan."""
    template_step_id: str = Field(..., description="Unique identifier for this step template")
    name: str = Field(..., description="Human-readable name for this step")
    description_template: str = Field(..., description="Description template with {param} placeholders")
    tool_to_use: str = Field(..., description="Tool name to execute this step")
    input_template: Dict[str, Any] = Field(..., description="Input template with parameter placeholders")
    dependencies: List[str] = Field(default_factory=list, description="List of step template IDs this depends on")
    step_type: StepType = Field(default=StepType.CONTENT_CREATION, description="Type of step")

    # Optional configuration
    is_optional: bool = Field(default=False, description="Whether this step can be skipped")
    parallel_group: Optional[str] = Field(default=None, description="Group ID for parallel execution")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    timeout: Optional[int] = Field(default=None, description="Timeout in seconds")
    estimated_duration: Optional[int] = Field(default=None, description="Estimated execution time in seconds")


class PlanTemplate(BaseModel):
    """Template for creating standardized plans."""
    template_id: str = Field(..., description="Unique identifier for this template")
    name: str = Field(..., description="Human-readable template name")
    description: str = Field(..., description="Detailed description of what this template does")
    category: str = Field(..., description="Template category (e.g., 'video_creation', 'image_series')")
    tags: List[str] = Field(default_factory=list, description="Tags for searching and filtering")
    
    # Template structure
    step_templates: List[StepTemplate] = Field(..., description="List of step templates")
    parameters: Dict[str, ParameterSchema] = Field(default_factory=dict, description="Template parameters")
    
    # Metadata
    estimated_duration: Optional[int] = Field(default=None, description="Total estimated duration in seconds")
    difficulty_level: str = Field(default="medium", description="Difficulty level: easy, medium, hard")
    success_rate: Optional[float] = Field(default=None, description="Historical success rate (0.0-1.0)")
    usage_count: int = Field(default=0, description="Number of times this template has been used")
    
    # Version information
    version: str = Field(default="1.0", description="Template version")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    
    def validate_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and process template parameters.
        
        Args:
            params: Raw parameter values from user input
            
        Returns:
            Validated and processed parameters
            
        Raises:
            ValueError: If validation fails
        """
        validated_params = {}
        
        # Check all required parameters are provided
        for param_name, schema in self.parameters.items():
            if param_name in params:
                value = params[param_name]
                validated_params[param_name] = self._validate_single_parameter(value, schema, param_name)
            elif schema.required:
                if schema.default is not None:
                    validated_params[param_name] = schema.default
                else:
                    raise ValueError(f"Required parameter '{param_name}' is missing")
            elif schema.default is not None:
                validated_params[param_name] = schema.default
        
        return validated_params
    
    def _validate_single_parameter(self, value: Any, schema: ParameterSchema, param_name: str) -> Any:
        """Validate a single parameter value against its schema."""
        if schema.type == ParameterType.STRING:
            if not isinstance(value, str):
                raise ValueError(f"Parameter '{param_name}' must be a string, got {type(value).__name__}")
            if schema.pattern and not __import__('re').match(schema.pattern, value):
                raise ValueError(f"Parameter '{param_name}' does not match required pattern")
            if schema.options and value not in schema.options:
                raise ValueError(f"Parameter '{param_name}' must be one of {schema.options}")
                
        elif schema.type == ParameterType.INTEGER:
            if not isinstance(value, int):
                try:
                    value = int(value)
                except (ValueError, TypeError):
                    raise ValueError(f"Parameter '{param_name}' must be an integer")
            if schema.min_value is not None and value < schema.min_value:
                raise ValueError(f"Parameter '{param_name}' must be >= {schema.min_value}")
            if schema.max_value is not None and value > schema.max_value:
                raise ValueError(f"Parameter '{param_name}' must be <= {schema.max_value}")
                
        elif schema.type == ParameterType.FLOAT:
            if not isinstance(value, (int, float)):
                try:
                    value = float(value)
                except (ValueError, TypeError):
                    raise ValueError(f"Parameter '{param_name}' must be a number")
            if schema.min_value is not None and value < schema.min_value:
                raise ValueError(f"Parameter '{param_name}' must be >= {schema.min_value}")
            if schema.max_value is not None and value > schema.max_value:
                raise ValueError(f"Parameter '{param_name}' must be <= {schema.max_value}")
                
        elif schema.type == ParameterType.BOOLEAN:
            if not isinstance(value, bool):
                if isinstance(value, str):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                else:
                    value = bool(value)
                    
        elif schema.type == ParameterType.LIST:
            if not isinstance(value, list):
                raise ValueError(f"Parameter '{param_name}' must be a list")
                
        elif schema.type == ParameterType.DICT:
            if not isinstance(value, dict):
                raise ValueError(f"Parameter '{param_name}' must be a dictionary")
        
        return value


class TemplateRecommendation(BaseModel):
    """Result of template recommendation."""
    recommended_template: str = Field(..., description="ID of the recommended template")
    confidence: float = Field(..., description="Confidence score (0.0-1.0)")
    reason: str = Field(..., description="Human-readable explanation for the recommendation")
    suggested_params: Dict[str, Any] = Field(default_factory=dict, description="Suggested parameter values")
    alternatives: List[str] = Field(default_factory=list, description="Alternative template IDs")


class TemplateValidationResult(BaseModel):
    """Result of template parameter validation."""
    valid: bool = Field(..., description="Whether all parameters are valid")
    errors: List[str] = Field(default_factory=list, description="List of validation errors")
    warnings: List[str] = Field(default_factory=list, description="List of validation warnings")
    suggestions: Dict[str, Any] = Field(default_factory=dict, description="Suggested parameter improvements")


class TemplateInstantiationResult(BaseModel):
    """Result of template instantiation."""
    success: bool = Field(..., description="Whether instantiation was successful")
    plan: Optional[Plan] = Field(default=None, description="The generated plan (if successful)")
    error: Optional[str] = Field(default=None, description="Error message (if failed)")
    warnings: List[str] = Field(default_factory=list, description="Any warnings during instantiation")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
