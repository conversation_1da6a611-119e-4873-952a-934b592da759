# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
models.py

This module defines the Pydantic models for the structured state of the V2 graph,
primarily for the "Plan". This ensures a clear, typed, and robust data
structure for managing the agent's tasks.
"""
from typing import List, Dict, Any, Literal, Optional
from pydantic import BaseModel, Field

# Define the possible statuses a step can have, as per FR-3.1
StepStatus = Literal["pending", "in_progress", "completed", "failed", "skipped"]

class Step(BaseModel):
    """
    Represents a single step in a plan, as per FR-3.1.
    """
    step_id: int = Field(description="A unique identifier for this step.")
    description: str = Field(description="A natural language description of what this step aims to achieve.")
    tool_to_use: str = Field(description="The name of the specific tool that should be used to execute this step.")
    inputs: Dict[str, Any] = Field(description="The dictionary of parameters to pass to the tool for this step.")
    status: StepStatus = Field(default="pending", description="The current execution status of this step.")
    dependencies: List[int] = Field(default_factory=list, description="A list of `step_id`s that must be completed before this step can start.")
    result: Optional[Dict[str, Any]] = Field(default=None, description="A structured dictionary holding the output of the step upon completion.")
    
    class Config:
        """Pydantic config."""
        use_enum_values = True # Ensures the status field uses the string literals

class Plan(BaseModel):
    """
    Represents the overall plan, which is a collection of steps.
    """
    original_task: str = Field(description="The original, high-level task description from the user.")
    steps: List[Step] = Field(description="The sequence of steps that need to be executed to accomplish the task.")

    def get_next_pending_step(self) -> Optional[Step]:
        """
        Finds the next step that is ready to be executed.
        A step is ready if its status is 'pending' and all its dependencies are 'completed'.
        """
        completed_ids = {s.step_id for s in self.steps if s.status == "completed"}
        for step in self.steps:
            if step.status == "pending":
                if all(dep_id in completed_ids for dep_id in step.dependencies):
                    return step
        return None
    
    def is_complete(self) -> bool:
        """
        Checks if the entire plan is complete.
        The plan is considered complete if there are no more pending or in_progress steps.
        """
        return not any(s.status in ["pending", "in_progress"] for s in self.steps) 