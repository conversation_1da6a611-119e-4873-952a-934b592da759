# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
enhanced_models.py

Enhanced Plan and Step models with support for:
- Semantic step IDs
- Parallel execution
- Retry mechanisms
- Conditional logic
- Template integration
"""

from typing import List, Dict, Any, Optional, Set, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from .models import StepStatus  # Import existing StepStatus


class StepType(str, Enum):
    """Types of steps in a plan."""
    DATA_COLLECTION = "data_collection"
    CONTENT_CREATION = "content_creation"
    CONTENT_EDITING = "content_editing"
    QUALITY_REVIEW = "quality_review"
    INTEGRATION = "integration"
    FINALIZATION = "finalization"
    CONDITIONAL = "conditional"
    LOOP = "loop"


class ConditionalLogic(BaseModel):
    """Conditional execution logic for steps."""
    condition: str = Field(..., description="Condition expression to evaluate")
    true_action: str = Field(default="continue", description="Action if condition is true")
    false_action: str = Field(default="skip", description="Action if condition is false")
    condition_type: str = Field(default="simple", description="Type of condition (simple, complex)")


class RetryConfig(BaseModel):
    """Retry configuration for steps."""
    max_retries: int = Field(default=3, description="Maximum number of retry attempts")
    retry_delay: int = Field(default=5, description="Delay between retries in seconds")
    exponential_backoff: bool = Field(default=True, description="Use exponential backoff")
    retry_on_errors: List[str] = Field(
        default_factory=lambda: ["timeout", "network_error", "rate_limit", "temporary_failure"],
        description="Error types to retry on"
    )

    def should_retry(self, error_type: str, retry_count: int) -> bool:
        """Check if we should retry based on error type and current retry count."""
        if retry_count >= self.max_retries:
            return False

        # Always retry on specified error types
        if error_type.lower() in [e.lower() for e in self.retry_on_errors]:
            return True

        # Don't retry on permanent errors
        permanent_errors = ["invalid_input", "permission_denied", "not_found", "validation_error"]
        if error_type.lower() in permanent_errors:
            return False

        # Default: retry on unknown errors (might be temporary)
        return True

    def get_retry_delay(self, retry_count: int) -> int:
        """Calculate delay before next retry."""
        if not self.exponential_backoff:
            return self.retry_delay

        # Exponential backoff: delay * (2 ^ retry_count)
        return min(self.retry_delay * (2 ** retry_count), 300)  # Max 5 minutes


class EnhancedStep(BaseModel):
    """Enhanced step model with advanced features."""
    # Core identification
    step_id: str = Field(..., description="Semantic step ID (e.g., 'collect_materials')")
    name: str = Field(..., description="Human-readable step name")
    description: str = Field(..., description="Detailed step description")
    
    # Execution configuration
    tool_to_use: str = Field(..., description="Tool name to execute this step")
    inputs: Dict[str, Any] = Field(default_factory=dict, description="Step input parameters")
    step_type: StepType = Field(..., description="Type of step")
    
    # Status and results
    status: StepStatus = Field(default="pending", description="Current execution status")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Step execution result")
    
    # Dependencies and relationships
    dependencies: List[str] = Field(default_factory=list, description="List of step IDs this depends on")
    dependents: List[str] = Field(default_factory=list, description="List of step IDs that depend on this")
    
    # Advanced execution features
    parallel_group: Optional[str] = Field(default=None, description="Group ID for parallel execution")
    conditional_logic: Optional[ConditionalLogic] = Field(default=None, description="Conditional execution logic")
    retry_config: RetryConfig = Field(default_factory=RetryConfig, description="Retry configuration")
    
    # Timing and performance
    estimated_duration: Optional[int] = Field(default=None, description="Estimated execution time in seconds")
    timeout: Optional[int] = Field(default=None, description="Maximum execution time in seconds")
    priority: int = Field(default=0, description="Execution priority (higher = more important)")
    
    # Execution tracking
    retry_count: int = Field(default=0, description="Current retry count")
    started_at: Optional[datetime] = Field(default=None, description="Execution start time")
    completed_at: Optional[datetime] = Field(default=None, description="Execution completion time")
    error_history: List[str] = Field(default_factory=list, description="History of errors encountered")
    
    # Template integration
    template_step_id: Optional[str] = Field(default=None, description="Original template step ID")
    is_customizable: bool = Field(default=True, description="Whether this step can be modified")
    template_params: Optional[Dict[str, Any]] = Field(default=None, description="Template parameters used")
    
    def can_execute(self, completed_steps: Set[str]) -> bool:
        """Check if this step can be executed based on dependencies."""
        if self.status != "pending":
            return False

        # Check if all dependencies are completed
        return all(dep in completed_steps for dep in self.dependencies)
    
    def is_ready_for_retry(self) -> bool:
        """Check if this step is ready for retry."""
        return (
            self.status == "failed" and
            self.retry_count < self.retry_config.max_retries
        )
    
    def should_skip_due_to_condition(self, context: Dict[str, Any]) -> bool:
        """Check if this step should be skipped due to conditional logic."""
        if not self.conditional_logic:
            return False
        
        # Simple condition evaluation (can be enhanced with more complex logic)
        condition = self.conditional_logic.condition
        try:
            # Basic condition evaluation - in production, use a safer evaluator
            result = eval(condition, {"__builtins__": {}}, context)
            if not result and self.conditional_logic.false_action == "skip":
                return True
        except Exception:
            # If condition evaluation fails, don't skip
            pass
        
        return False
    
    def get_execution_duration(self) -> Optional[int]:
        """Get the actual execution duration in seconds."""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return None
    
    def add_error(self, error_message: str, error_type: str = "unknown"):
        """Add an error to the error history with type classification."""
        timestamp = datetime.now().isoformat()
        self.error_history.append(f"{timestamp}: [{error_type}] {error_message}")

    def get_last_error_type(self) -> str:
        """Extract error type from the last error."""
        if not self.error_history:
            return "unknown"

        last_error = self.error_history[-1]
        # Extract error type from format: "timestamp: [error_type] message"
        if "[" in last_error and "]" in last_error:
            start = last_error.find("[") + 1
            end = last_error.find("]")
            return last_error[start:end]
        return "unknown"

    def should_retry_on_failure(self) -> bool:
        """Check if this step should be retried based on the last error."""
        if self.status != "failed":
            return False

        last_error_type = self.get_last_error_type()
        return self.retry_config.should_retry(last_error_type, self.retry_count)

    def get_retry_delay_seconds(self) -> int:
        """Get the delay before next retry attempt."""
        return self.retry_config.get_retry_delay(self.retry_count)

    def reset_for_retry(self):
        """Reset step state for retry with intelligent delay."""
        self.status = "pending"
        self.retry_count += 1
        self.started_at = None
        self.completed_at = None
        self.result = None

    def get_user_friendly_status(self) -> str:
        """Get a user-friendly status description."""
        if self.status == "pending":
            if self.retry_count > 0:
                return f"等待重试 (第{self.retry_count}次尝试)"
            return "等待执行"
        elif self.status == "in_progress":
            return "正在执行..."
        elif self.status == "completed":
            duration = self.get_execution_duration()
            if duration:
                return f"已完成 (耗时{duration}秒)"
            return "已完成"
        elif self.status == "failed":
            if self.should_retry_on_failure():
                delay = self.get_retry_delay_seconds()
                return f"执行失败，将在{delay}秒后重试"
            return f"执行失败 (已重试{self.retry_count}次)"
        elif self.status == "skipped":
            return "已跳过"
        else:
            return self.status


class ExecutionContext(BaseModel):
    """Enhanced execution context for plan execution."""
    plan_id: str = Field(..., description="Plan identifier")
    current_step_id: Optional[str] = Field(default=None, description="Currently executing step ID")
    
    # Data management
    shared_variables: Dict[str, Any] = Field(default_factory=dict, description="Global variables")
    step_outputs: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Outputs from each step")
    
    # Execution tracking
    execution_log: List[Dict[str, Any]] = Field(default_factory=list, description="Execution log entries")
    start_time: datetime = Field(default_factory=datetime.now, description="Plan execution start time")
    
    # Template integration
    template_id: Optional[str] = Field(default=None, description="Source template ID")
    template_params: Optional[Dict[str, Any]] = Field(default=None, description="Template parameters")
    
    # Performance metrics
    total_steps: int = Field(default=0, description="Total number of steps")
    completed_steps: int = Field(default=0, description="Number of completed steps")
    failed_steps: int = Field(default=0, description="Number of failed steps")
    
    def add_log_entry(self, level: str, message: str, step_id: Optional[str] = None, metadata: Optional[Dict] = None):
        """Add a log entry to the execution log."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "message": message,
            "step_id": step_id,
            "metadata": metadata or {}
        }
        self.execution_log.append(entry)
    
    def update_step_output(self, step_id: str, output: Dict[str, Any]):
        """Update the output for a specific step."""
        self.step_outputs[step_id] = output
        self.add_log_entry("INFO", f"Updated output for step {step_id}", step_id)
    
    def get_step_context(self, step_id: str) -> Dict[str, Any]:
        """Get context information for a specific step."""
        return {
            "step_id": step_id,
            "shared_variables": self.shared_variables,
            "previous_outputs": self.step_outputs,
            "template_params": self.template_params,
            "execution_metadata": {
                "plan_id": self.plan_id,
                "total_steps": self.total_steps,
                "completed_steps": self.completed_steps
            }
        }
    
    def get_progress_percentage(self) -> float:
        """Get execution progress as a percentage."""
        if self.total_steps == 0:
            return 0.0
        return (self.completed_steps / self.total_steps) * 100

    def get_user_friendly_progress(self) -> str:
        """Get a user-friendly progress description."""
        progress = self.get_progress_percentage()

        if progress == 0:
            return "准备开始执行"
        elif progress < 25:
            return f"刚刚开始 ({progress:.0f}%)"
        elif progress < 50:
            return f"进展顺利 ({progress:.0f}%)"
        elif progress < 75:
            return f"过半完成 ({progress:.0f}%)"
        elif progress < 100:
            return f"即将完成 ({progress:.0f}%)"
        else:
            return "全部完成 ✅"

    def get_execution_summary_text(self) -> str:
        """Get a text summary of execution status."""
        total_time = (datetime.now() - self.start_time).total_seconds()

        if self.completed_steps == 0:
            return "计划已创建，等待开始执行"

        summary_parts = [
            f"已完成 {self.completed_steps}/{self.total_steps} 个步骤",
            f"执行时间 {total_time:.1f} 秒"
        ]

        if self.failed_steps > 0:
            summary_parts.append(f"失败 {self.failed_steps} 个步骤")

        return "，".join(summary_parts)

    def get_current_activity(self) -> str:
        """Get description of current activity."""
        if self.current_step_id:
            return f"正在执行: {self.current_step_id}"
        elif self.completed_steps == self.total_steps:
            return "所有步骤已完成"
        elif self.failed_steps > 0:
            return "等待处理失败的步骤"
        else:
            return "等待执行下一个步骤"


class EnhancedPlan(BaseModel):
    """Enhanced plan model with advanced execution features."""
    # Core identification
    plan_id: str = Field(..., description="Unique plan identifier")
    original_task: str = Field(..., description="Original task description")
    
    # Plan structure
    steps: List[EnhancedStep] = Field(..., description="List of execution steps")
    execution_context: ExecutionContext = Field(..., description="Execution context")
    
    # Template integration
    source_template: Optional[str] = Field(default=None, description="Source template ID")
    template_params: Optional[Dict[str, Any]] = Field(default=None, description="Template parameters used")
    is_from_template: bool = Field(default=False, description="Whether this plan was created from a template")
    
    # Version and metadata
    version: str = Field(default="1.0", description="Plan version")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    modified_at: datetime = Field(default_factory=datetime.now, description="Last modification timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    # Execution configuration
    max_parallel_steps: int = Field(default=3, description="Maximum number of parallel steps")
    global_timeout: Optional[int] = Field(default=None, description="Global plan timeout in seconds")
    
    def __init__(self, **data):
        super().__init__(**data)
        # Initialize execution context if not provided
        if not hasattr(self, 'execution_context') or not self.execution_context:
            self.execution_context = ExecutionContext(
                plan_id=self.plan_id,
                template_id=self.source_template,
                template_params=self.template_params,
                total_steps=len(self.steps)
            )
        
        # Build dependency graph
        self._build_dependency_graph()
    
    def _build_dependency_graph(self):
        """Build the dependency graph for steps."""
        # Build dependents list
        for step in self.steps:
            for dep_id in step.dependencies:
                dep_step = self.get_step(dep_id)
                if dep_step and step.step_id not in dep_step.dependents:
                    dep_step.dependents.append(step.step_id)
    
    def get_step(self, step_id: str) -> Optional[EnhancedStep]:
        """Get a step by its ID."""
        for step in self.steps:
            if step.step_id == step_id:
                return step
        return None
    
    def get_next_executable_steps(self) -> List[EnhancedStep]:
        """Get the next batch of steps that can be executed."""
        completed_steps = {s.step_id for s in self.steps if s.status == "completed"}
        executable_steps = []

        for step in self.steps:
            if step.can_execute(completed_steps):
                # Check conditional logic
                if not step.should_skip_due_to_condition(self.execution_context.shared_variables):
                    executable_steps.append(step)

        # Sort by priority (higher priority first)
        executable_steps.sort(key=lambda s: s.priority, reverse=True)

        # Limit by max parallel steps
        return executable_steps[:self.max_parallel_steps]
    
    def get_parallel_groups(self) -> Dict[str, List[EnhancedStep]]:
        """Get steps grouped by parallel execution groups."""
        groups = {}
        for step in self.steps:
            if step.parallel_group:
                if step.parallel_group not in groups:
                    groups[step.parallel_group] = []
                groups[step.parallel_group].append(step)
        return groups
    
    def is_complete(self) -> bool:
        """Check if the plan execution is complete."""
        return not any(
            s.status in ["pending", "in_progress"]
            for s in self.steps
        )
    
    def has_failed(self) -> bool:
        """Check if the plan has failed (has failed steps with no more retries)."""
        return any(
            s.status == "failed" and not s.is_ready_for_retry()
            for s in self.steps
        )
    
    def get_failed_steps(self) -> List[EnhancedStep]:
        """Get all failed steps."""
        return [s for s in self.steps if s.status == "failed"]
    
    def get_retryable_steps(self) -> List[EnhancedStep]:
        """Get steps that can be retried."""
        return [s for s in self.steps if s.is_ready_for_retry()]
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get a summary of plan execution."""
        status_counts = {}
        all_statuses = ["pending", "in_progress", "completed", "failed", "skipped"]
        for status in all_statuses:
            status_counts[status] = len([s for s in self.steps if s.status == status])

        return {
            "plan_id": self.plan_id,
            "total_steps": len(self.steps),
            "status_counts": status_counts,
            "progress_percentage": self.execution_context.get_progress_percentage(),
            "is_complete": self.is_complete(),
            "has_failed": self.has_failed(),
            "execution_time": (datetime.now() - self.execution_context.start_time).total_seconds(),
            "template_info": {
                "source_template": self.source_template,
                "is_from_template": self.is_from_template
            }
        }
