# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
plan_converter.py

Utilities for converting between Legacy Plan and EnhancedPlan models.
This ensures backward compatibility while migrating to the unified EnhancedPlan model.
"""

from typing import Any, Dict, List
import uuid
from datetime import datetime

from .models import Plan, Step
from .enhanced_models import (
    EnhancedPlan, 
    EnhancedStep, 
    ExecutionContext, 
    StepType, 
    RetryConfig
)


def convert_legacy_plan_to_enhanced(legacy_plan: Plan) -> EnhancedPlan:
    """
    Convert a Legacy Plan to an EnhancedPlan.
    
    Args:
        legacy_plan: The legacy Plan object to convert
        
    Returns:
        An equivalent EnhancedPlan object
    """
    # Convert legacy steps to enhanced steps
    enhanced_steps = []
    
    for legacy_step in legacy_plan.steps:
        enhanced_step = convert_legacy_step_to_enhanced(legacy_step)
        enhanced_steps.append(enhanced_step)
    
    # Generate plan ID
    plan_id = str(uuid.uuid4())

    # Create execution context
    execution_context = ExecutionContext(
        plan_id=plan_id,
        total_steps=len(enhanced_steps),
        completed_steps=len([s for s in enhanced_steps if s.status == "completed"]),
        failed_steps=len([s for s in enhanced_steps if s.status == "failed"]),
        start_time=datetime.now()
    )
    
    # Create enhanced plan
    enhanced_plan = EnhancedPlan(
        plan_id=plan_id,
        original_task=legacy_plan.original_task,
        steps=enhanced_steps,
        execution_context=execution_context,
        is_from_template=False,  # Legacy plans are not from templates
        source_template=None,
        template_params=None,
        version="1.0",
        metadata={"converted_from": "legacy_plan", "conversion_time": datetime.now().isoformat()}
    )
    
    return enhanced_plan


def convert_legacy_step_to_enhanced(legacy_step: Step) -> EnhancedStep:
    """
    Convert a Legacy Step to an EnhancedStep.
    
    Args:
        legacy_step: The legacy Step object to convert
        
    Returns:
        An equivalent EnhancedStep object
    """
    # Convert numeric step_id to string
    step_id = f"step_{legacy_step.step_id}"
    
    # Convert numeric dependencies to string
    dependencies = [f"step_{dep_id}" for dep_id in legacy_step.dependencies]
    
    # Infer step type from tool name
    step_type = infer_step_type_from_tool(legacy_step.tool_to_use)
    
    # Create default retry config
    retry_config = RetryConfig()
    
    enhanced_step = EnhancedStep(
        step_id=step_id,
        name=legacy_step.description,  # Use description as name
        description=legacy_step.description,
        tool_to_use=legacy_step.tool_to_use,
        inputs=legacy_step.inputs,
        status=legacy_step.status,
        dependencies=dependencies,
        step_type=step_type,
        retry_config=retry_config,
        result=legacy_step.result,
        parallel_group=None,  # Legacy steps don't have parallel groups
        conditional_logic=None,  # Legacy steps don't have conditional logic
        estimated_duration=60,  # Default 1 minute
        retry_count=0,
        error_history=[],
        started_at=None,
        completed_at=None,
        metadata={"converted_from": "legacy_step"}
    )
    
    return enhanced_step


def infer_step_type_from_tool(tool_name: str) -> StepType:
    """
    Infer the step type based on the tool name.
    
    Args:
        tool_name: Name of the tool
        
    Returns:
        Appropriate StepType
    """
    tool_type_mapping = {
        "visual_expert": StepType.CONTENT_CREATION,
        "audio_expert": StepType.CONTENT_CREATION,
        "video_expert": StepType.CONTENT_EDITING,
        "planner_tool": StepType.DATA_COLLECTION,
        "reviser_tool": StepType.QUALITY_REVIEW,
    }
    
    return tool_type_mapping.get(tool_name, StepType.CONTENT_CREATION)


def ensure_enhanced_plan(plan: Any) -> EnhancedPlan:
    """
    Ensure that the given plan is an EnhancedPlan.
    If it's a Legacy Plan, convert it. If it's already Enhanced, return as-is.
    
    Args:
        plan: Plan object of unknown type
        
    Returns:
        EnhancedPlan object
        
    Raises:
        ValueError: If the plan is neither Legacy nor Enhanced
    """
    if plan is None:
        raise ValueError("Plan cannot be None")
    
    # Check if it's already an EnhancedPlan
    if isinstance(plan, EnhancedPlan):
        return plan
    
    # Check if it's a Legacy Plan
    if isinstance(plan, Plan):
        return convert_legacy_plan_to_enhanced(plan)
    
    # Check if it's a dict that might be a Legacy Plan
    if isinstance(plan, dict):
        try:
            # Try to create a Legacy Plan from dict
            legacy_plan = Plan(**plan)
            return convert_legacy_plan_to_enhanced(legacy_plan)
        except Exception:
            pass
        
        try:
            # Try to create an EnhancedPlan from dict
            enhanced_plan = EnhancedPlan(**plan)
            return enhanced_plan
        except Exception:
            pass
    
    raise ValueError(f"Unknown plan type: {type(plan)}")


def validate_enhanced_plan(plan: EnhancedPlan) -> List[str]:
    """
    Validate an EnhancedPlan and return any issues found.
    
    Args:
        plan: The EnhancedPlan to validate
        
    Returns:
        List of validation error messages (empty if valid)
    """
    issues = []
    
    # Check basic structure
    if not plan.steps:
        issues.append("Plan has no steps")
        return issues
    
    # Check step IDs are unique
    step_ids = [step.step_id for step in plan.steps]
    if len(step_ids) != len(set(step_ids)):
        issues.append("Duplicate step IDs found")
    
    # Check dependencies
    for step in plan.steps:
        for dep in step.dependencies:
            if dep not in step_ids:
                issues.append(f"Step {step.step_id} has invalid dependency: {dep}")
    
    # Check execution context consistency
    if plan.execution_context:
        expected_total = len(plan.steps)
        actual_total = plan.execution_context.total_steps
        if expected_total != actual_total:
            issues.append(f"Execution context total_steps mismatch: expected {expected_total}, got {actual_total}")
    
    return issues
