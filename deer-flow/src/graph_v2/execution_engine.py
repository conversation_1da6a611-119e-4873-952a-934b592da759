# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
execution_engine.py

执行引擎：负责驱动Master Agent持续执行多步计划直到完成。
执行引擎不直接调用工具，而是循环调用Master Agent，由Master Agent负责具体的工具调用。
"""

from typing import Dict, Any
from langchain_core.messages import AIMessage, HumanMessage

from .types import State
from .nodes import master_agent_node
from .enhanced_models import EnhancedPlan
from .plan_converter import ensure_enhanced_plan


def execution_engine_node(state: State) -> State:
    """
    执行引擎节点：驱动Master Agent持续执行计划直到完成
    
    职责：
    1. 循环调用Master Agent
    2. 监控计划执行进度
    3. 强制继续执行未完成的计划
    4. 防止Master Agent提前停止
    
    Args:
        state: 当前状态，包含计划和消息历史
        
    Returns:
        更新后的状态，计划应该已完成或达到最大迭代次数
    """
    plan = state.get("plan")

    # 安全检查
    if not plan:
        return state

    try:
        # 确保我们有EnhancedPlan
        enhanced_plan = ensure_enhanced_plan(plan)
        # 更新state中的plan为转换后的EnhancedPlan
        state["plan"] = enhanced_plan
        plan = enhanced_plan
    except Exception as e:
        print(f"   ❌ 计划转换失败: {e}")
        return state

    if plan.is_complete():
        return state
    
    # 执行配置
    max_iterations = 20  # 防止无限循环
    iteration = 0
    
    print(f"🔄 执行引擎启动：开始执行计划 (共{len(plan.steps)}个步骤)")
    
    # 主执行循环
    while not plan.is_complete() and iteration < max_iterations:
        iteration += 1
        print(f"   执行循环 {iteration}: 准备执行下一步...")

        # 获取下一个可执行步骤
        executable_steps = plan.get_next_executable_steps()
        if not executable_steps:
            print(f"   ⚠️  没有可执行步骤，退出循环")
            break

        next_step = executable_steps[0]
        print(f"   下一步: {next_step.step_id} - {next_step.name}")

        # 设置当前步骤，让Master Agent知道要执行什么
        state["current_step"] = next_step.step_id

        # 创建明确的执行指令给Master Agent
        step_instruction = (
            f"请执行计划中的步骤：\n"
            f"步骤ID: {next_step.step_id}\n"
            f"步骤名称: {next_step.name}\n"
            f"描述: {next_step.description}\n"
            f"使用工具: {next_step.tool_to_use}\n"
            f"执行完成后，请调用update_step_status更新步骤状态。"
        )
        state["messages"].append(HumanMessage(content=step_instruction))

        # 调用Master Agent执行步骤
        try:
            state = master_agent_node(state)
        except Exception as e:
            print(f"   ❌ Master Agent执行出错: {e}")
            # 标记步骤失败
            next_step.status = "failed"
            next_step.add_error(str(e), "execution_error")
            # 添加错误消息并继续（或退出）
            error_msg = f"步骤 {next_step.step_id} 执行失败: {str(e)}"
            state["messages"].append(AIMessage(content=error_msg))
            break
        
        # 获取更新后的计划
        plan = state.get("plan")
        if not plan:
            print("   ⚠️  计划丢失，退出执行循环")
            break

        # 检查当前步骤是否已完成
        current_step = state.get("current_step")
        if current_step:
            step = plan.get_step(current_step)
            if step:
                print(f"   📊 步骤 {current_step} 状态: {step.status}")

                if step.status == "completed":
                    print(f"   ✅ 步骤 {current_step} 执行成功")
                    # 清除current_step，准备下一轮
                    state["current_step"] = None
                elif step.status == "failed":
                    print(f"   ❌ 步骤 {current_step} 执行失败")
                    # 检查是否可以重试
                    if step.should_retry_on_failure():
                        print(f"   🔄 步骤 {current_step} 将重试")
                        step.reset_for_retry()
                        state["current_step"] = None  # 重新执行
                    else:
                        print(f"   💀 步骤 {current_step} 无法重试，停止执行")
                        break
                else:
                    # 步骤仍在pending或in_progress状态
                    print(f"   ⏳ 步骤 {current_step} 仍在执行中...")

                    # 检查Master Agent是否调用了update_step_status
                    last_message = state["messages"][-1] if state["messages"] else None
                    if isinstance(last_message, AIMessage) and not last_message.tool_calls:
                        # Master Agent没有调用工具，可能忘记更新状态
                        print(f"   ⚠️  Master Agent可能忘记更新步骤状态")
                        reminder_msg = (
                            f"请确保调用 update_step_status 更新步骤 {current_step} 的状态。\n"
                            f"如果步骤执行成功，状态应为 'completed'；如果失败，状态应为 'failed'。"
                        )
                        state["messages"].append(HumanMessage(content=reminder_msg))
                        continue
            else:
                print(f"   ❌ 找不到步骤 {current_step}")
                state["current_step"] = None

        # 检查整体进度
        completed_steps = len([s for s in plan.steps if s.status == "completed"])
        total_steps = len(plan.steps)
        print(f"   📊 总进度: {completed_steps}/{total_steps} 步骤完成")
        
        # 检查是否有新的用户消息（用户中断）
        if _has_new_user_message(state, iteration):
            print("   👤 检测到用户新消息，退出执行引擎")
            break
    
    # 执行完成处理
    if iteration >= max_iterations:
        print(f"   ⚠️  达到最大迭代次数 ({max_iterations})，强制退出")
        timeout_msg = "执行时间过长，已自动停止。如需继续，请重新发起请求。"
        state["messages"].append(AIMessage(content=timeout_msg))
    elif plan and plan.is_complete():
        print("   🎉 计划执行完成！")
    
    return state


def should_use_execution_engine(state: State) -> bool:
    """
    判断是否需要使用执行引擎

    规则：
    1. 没有计划 → 不需要
    2. 单步计划 → 不需要（Master Agent可以处理）
    3. 模板计划 → 需要（预定义的多步流程）
    4. 多步自定义计划 → 需要

    Args:
        state: 当前状态

    Returns:
        是否需要使用执行引擎
    """
    plan = state.get("plan")

    # 没有计划
    if not plan:
        return False

    try:
        # 确保我们有EnhancedPlan来进行判断
        enhanced_plan = ensure_enhanced_plan(plan)

        # 计划已完成
        if enhanced_plan.is_complete():
            return False

        # 单步计划，Master Agent可以处理
        if len(enhanced_plan.steps) <= 1:
            return False

        # 模板计划，通常是多步的，需要执行引擎
        if enhanced_plan.is_from_template:
            return True

        # 多步自定义计划，需要执行引擎
        if len(enhanced_plan.steps) > 2:
            return True

        return False

    except Exception:
        # 如果转换失败，保守地不使用执行引擎
        return False


def _has_new_user_message(state: State, current_iteration: int) -> bool:
    """
    检查是否有新的用户消息（用户中断）
    
    简单实现：检查最近的消息是否是用户消息
    更复杂的实现可以跟踪消息数量变化
    """
    if not state.get("messages"):
        return False
    
    # 检查最后几条消息中是否有用户消息
    recent_messages = state["messages"][-3:]  # 检查最近3条消息
    
    for msg in reversed(recent_messages):
        if hasattr(msg, 'type') and msg.type == "human":
            # 简单判断：如果有用户消息且不是强制继续消息
            if not msg.content.startswith("计划尚未完成"):
                return True
    
    return False


def generate_execution_summary(plan, execution_log: list) -> str:
    """
    生成执行完成的友好总结
    
    Args:
        plan: 执行完成的计划
        execution_log: 执行日志
        
    Returns:
        友好的总结文本
    """
    if not plan:
        return "执行完成。"
    
    completed_steps = len([s for s in plan.steps if s.status == "completed"])
    total_steps = len(plan.steps)
    
    if completed_steps == total_steps:
        summary = f"🎉 所有任务已完成！成功执行了 {total_steps} 个步骤：\n\n"
        
        for i, step in enumerate(plan.steps, 1):
            if step.status == "completed":
                summary += f"{i}. ✅ {step.name}\n"
            else:
                summary += f"{i}. ❌ {step.name} (失败)\n"
        
        if hasattr(plan, 'is_from_template') and plan.is_from_template:
            summary += f"\n📋 基于模板: {getattr(plan, 'source_template', '未知')}"
        
        return summary
    else:
        return f"执行完成。{completed_steps}/{total_steps} 个步骤成功完成。"
