# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
parameter_resolver.py

Unified parameter resolution system that handles:
- {{step_id.field.path}} - Step output references
- ${global_var} - Global variable references  
- {template_param} - Template parameter references
- Nested references and complex data structures
"""

import re
import json
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel

from .enhanced_models import ExecutionContext, EnhancedPlan


class ParameterReference(BaseModel):
    """Represents a parameter reference."""
    original: str
    ref_type: str  # "step", "global", "template"
    path: List[str]
    default_value: Any = None


class ParameterResolver:
    """Unified parameter resolver for all reference types."""
    
    # Regex patterns for different reference types
    STEP_REFERENCE_PATTERN = r'\{\{([^}]+)\}\}'  # {{step_id.field.path}}
    GLOBAL_REFERENCE_PATTERN = r'\$\{([^}]+)\}'  # ${global_var}
    TEMPLATE_REFERENCE_PATTERN = r'\{([^{}]+)\}'  # {template_param}
    
    def __init__(self, plan: EnhancedPlan):
        self.plan = plan
        self.context = plan.execution_context
    
    def resolve(self, value: Any) -> Any:
        """
        Resolve all references in a value.
        
        Args:
            value: The value to resolve (can be string, dict, list, or primitive)
            
        Returns:
            The resolved value with all references replaced
        """
        if isinstance(value, str):
            return self._resolve_string(value)
        elif isinstance(value, dict):
            return self._resolve_dict(value)
        elif isinstance(value, list):
            return self._resolve_list(value)
        else:
            return value
    
    def _resolve_string(self, text: str) -> Any:
        """Resolve references in a string."""
        if not isinstance(text, str):
            return text
        
        # Track if we made any replacements
        original_text = text
        
        # 1. Resolve step references: {{step_id.field.path}}
        text = self._resolve_step_references(text)
        
        # 2. Resolve global variable references: ${global_var}
        text = self._resolve_global_references(text)
        
        # 3. Resolve template parameter references: {template_param}
        text = self._resolve_template_references(text)
        
        # If the entire string was a single reference and got replaced with non-string, return that
        if text != original_text and self._is_single_reference(original_text):
            try:
                # Try to parse as JSON if it looks like structured data
                if text.startswith(('{', '[', '"')) or text in ('true', 'false', 'null'):
                    return json.loads(text)
                # Try to parse as number
                if text.replace('.', '').replace('-', '').isdigit():
                    return float(text) if '.' in text else int(text)
            except (json.JSONDecodeError, ValueError):
                pass
        
        return text
    
    def _resolve_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve references in a dictionary."""
        resolved = {}
        for key, value in data.items():
            resolved_key = self._resolve_string(key) if isinstance(key, str) else key
            resolved_value = self.resolve(value)
            resolved[resolved_key] = resolved_value
        return resolved
    
    def _resolve_list(self, data: List[Any]) -> List[Any]:
        """Resolve references in a list."""
        return [self.resolve(item) for item in data]
    
    def _resolve_step_references(self, text: str) -> str:
        """Resolve step output references: {{step_id.field.path}}"""
        def replace_step_ref(match):
            ref_path = match.group(1).strip()
            try:
                return str(self._get_step_value(ref_path))
            except Exception as e:
                self.context.add_log_entry(
                    "WARNING", 
                    f"Failed to resolve step reference {{{{{{ref_path}}}}}}: {e}"
                )
                return match.group(0)  # Return original if resolution fails
        
        return re.sub(self.STEP_REFERENCE_PATTERN, replace_step_ref, text)
    
    def _resolve_global_references(self, text: str) -> str:
        """Resolve global variable references: ${global_var}"""
        def replace_global_ref(match):
            var_name = match.group(1).strip()
            try:
                value = self.context.shared_variables.get(var_name)
                if value is not None:
                    return str(value)
                else:
                    self.context.add_log_entry(
                        "WARNING",
                        f"Global variable '${{{var_name}}}' not found"
                    )
                    return match.group(0)
            except Exception as e:
                self.context.add_log_entry(
                    "WARNING",
                    f"Failed to resolve global reference ${{{var_name}}}: {e}"
                )
                return match.group(0)
        
        return re.sub(self.GLOBAL_REFERENCE_PATTERN, replace_global_ref, text)
    
    def _resolve_template_references(self, text: str) -> str:
        """Resolve template parameter references: {template_param}"""
        def replace_template_ref(match):
            param_name = match.group(1).strip()
            
            # Skip if this looks like a step or global reference
            if '.' in param_name or param_name.startswith('$'):
                return match.group(0)
            
            try:
                if self.context.template_params and param_name in self.context.template_params:
                    value = self.context.template_params[param_name]
                    return str(value)
                else:
                    self.context.add_log_entry(
                        "WARNING",
                        f"Template parameter '{{{param_name}}}' not found"
                    )
                    return match.group(0)
            except Exception as e:
                self.context.add_log_entry(
                    "WARNING",
                    f"Failed to resolve template reference {{{param_name}}}: {e}"
                )
                return match.group(0)
        
        return re.sub(self.TEMPLATE_REFERENCE_PATTERN, replace_template_ref, text)
    
    def _get_step_value(self, ref_path: str) -> Any:
        """Get value from step output using dot notation path."""
        path_parts = ref_path.split('.')
        
        if len(path_parts) < 2:
            raise ValueError(f"Invalid step reference path: {ref_path}")
        
        step_id = path_parts[0]
        field_path = path_parts[1:]
        
        # Get step output
        step_output = self.context.step_outputs.get(step_id)
        if step_output is None:
            raise ValueError(f"No output found for step: {step_id}")
        
        # Navigate through the field path
        current_value = step_output
        for field in field_path:
            if isinstance(current_value, dict):
                if field in current_value:
                    current_value = current_value[field]
                else:
                    raise ValueError(f"Field '{field}' not found in step {step_id} output")
            elif isinstance(current_value, list):
                try:
                    index = int(field)
                    if 0 <= index < len(current_value):
                        current_value = current_value[index]
                    else:
                        raise ValueError(f"Index {index} out of range for step {step_id} output")
                except ValueError:
                    raise ValueError(f"Invalid list index '{field}' for step {step_id} output")
            else:
                raise ValueError(f"Cannot access field '{field}' on non-dict/list value in step {step_id}")
        
        return current_value
    
    def _is_single_reference(self, text: str) -> bool:
        """Check if the text is a single reference (not mixed with other text)."""
        # Check for step reference
        step_matches = re.findall(self.STEP_REFERENCE_PATTERN, text)
        if len(step_matches) == 1 and text.strip() == f"{{{{{step_matches[0]}}}}}":
            return True
        
        # Check for global reference
        global_matches = re.findall(self.GLOBAL_REFERENCE_PATTERN, text)
        if len(global_matches) == 1 and text.strip() == f"${{{global_matches[0]}}}":
            return True
        
        # Check for template reference
        template_matches = re.findall(self.TEMPLATE_REFERENCE_PATTERN, text)
        if len(template_matches) == 1 and text.strip() == f"{{{template_matches[0]}}}":
            return True
        
        return False
    
    def validate_references(self, value: Any) -> List[str]:
        """
        Validate all references in a value and return list of issues.
        
        Args:
            value: The value to validate
            
        Returns:
            List of validation error messages
        """
        issues = []
        
        def check_string(text: str, path: str = ""):
            if not isinstance(text, str):
                return
            
            # Check step references
            step_refs = re.findall(self.STEP_REFERENCE_PATTERN, text)
            for ref in step_refs:
                try:
                    self._get_step_value(ref)
                except Exception as e:
                    issues.append(f"Invalid step reference {{{{{{ref}}}}}} at {path}: {e}")
            
            # Check global references
            global_refs = re.findall(self.GLOBAL_REFERENCE_PATTERN, text)
            for ref in global_refs:
                if ref not in self.context.shared_variables:
                    issues.append(f"Undefined global variable ${{{ref}}} at {path}")
            
            # Check template references
            template_refs = re.findall(self.TEMPLATE_REFERENCE_PATTERN, text)
            for ref in template_refs:
                if '.' not in ref and not ref.startswith('$'):  # Skip step/global refs
                    if not self.context.template_params or ref not in self.context.template_params:
                        issues.append(f"Undefined template parameter {{{ref}}} at {path}")
        
        def validate_recursive(obj: Any, path: str = "root"):
            if isinstance(obj, str):
                check_string(obj, path)
            elif isinstance(obj, dict):
                for key, val in obj.items():
                    validate_recursive(val, f"{path}.{key}")
            elif isinstance(obj, list):
                for i, val in enumerate(obj):
                    validate_recursive(val, f"{path}[{i}]")
        
        validate_recursive(value)
        return issues
    
    def get_reference_info(self, text: str) -> List[ParameterReference]:
        """Extract information about all references in a text."""
        references = []
        
        # Step references
        for match in re.finditer(self.STEP_REFERENCE_PATTERN, text):
            ref_path = match.group(1).strip()
            path_parts = ref_path.split('.')
            references.append(ParameterReference(
                original=match.group(0),
                ref_type="step",
                path=path_parts
            ))
        
        # Global references
        for match in re.finditer(self.GLOBAL_REFERENCE_PATTERN, text):
            var_name = match.group(1).strip()
            references.append(ParameterReference(
                original=match.group(0),
                ref_type="global",
                path=[var_name]
            ))
        
        # Template references
        for match in re.finditer(self.TEMPLATE_REFERENCE_PATTERN, text):
            param_name = match.group(1).strip()
            if '.' not in param_name and not param_name.startswith('$'):
                references.append(ParameterReference(
                    original=match.group(0),
                    ref_type="template",
                    path=[param_name]
                ))
        
        return references


class ParameterResolverFactory:
    """Factory for creating parameter resolvers."""
    
    @staticmethod
    def create_resolver(plan: EnhancedPlan) -> ParameterResolver:
        """Create a parameter resolver for a plan."""
        return ParameterResolver(plan)
    
    @staticmethod
    def resolve_step_inputs(plan: EnhancedPlan, step_id: str) -> Dict[str, Any]:
        """
        Convenience method to resolve inputs for a specific step.
        
        Args:
            plan: The plan containing the step
            step_id: ID of the step to resolve inputs for
            
        Returns:
            Resolved input dictionary
        """
        step = plan.get_step(step_id)
        if not step:
            raise ValueError(f"Step {step_id} not found in plan")
        
        resolver = ParameterResolver(plan)
        return resolver.resolve(step.inputs)
    
    @staticmethod
    def validate_plan_references(plan: EnhancedPlan) -> Dict[str, List[str]]:
        """
        Validate all references in a plan.
        
        Args:
            plan: The plan to validate
            
        Returns:
            Dictionary mapping step IDs to lists of validation issues
        """
        resolver = ParameterResolver(plan)
        validation_results = {}
        
        for step in plan.steps:
            issues = resolver.validate_references(step.inputs)
            if issues:
                validation_results[step.step_id] = issues
        
        return validation_results
