# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
enums.py

Common enums used across the graph_v2 module to avoid circular imports.
"""

from enum import Enum


class StepType(str, Enum):
    """Types of steps in a plan."""
    DATA_COLLECTION = "data_collection"
    CONTENT_CREATION = "content_creation"
    CONTENT_EDITING = "content_editing"
    QUALITY_REVIEW = "quality_review"
    INTEGRATION = "integration"
    FINALIZATION = "finalization"
    CONDITIONAL = "conditional"
    LOOP = "loop"
