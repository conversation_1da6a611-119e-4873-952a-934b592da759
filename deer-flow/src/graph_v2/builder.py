# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Graph Builder for the V2 architecture.

This file defines the function to create and compile the new, simplified
Master Agent-centric workflow.
"""

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage

from .types import State
from .nodes import master_agent_node
from .execution_engine import execution_engine_node, should_use_execution_engine

def should_continue(state: State) -> str:
    """
    智能路由器：决定使用Master Agent还是执行引擎

    路由逻辑：
    1. 检查是否需要执行引擎（多步计划）
    2. 如果需要且计划未完成 → 执行引擎
    3. 否则使用原有逻辑 → Master Agent或结束
    """
    messages = state["messages"]
    last_message = messages[-1]
    plan = state.get("plan")

    # 检查是否需要执行引擎
    if should_use_execution_engine(state):
        # 多步计划且未完成 → 使用执行引擎
        if plan and not plan.is_complete():
            return "execution_engine"

    # 原有逻辑：简单对话和单步任务
    if isinstance(last_message, AIMessage) and not last_message.tool_calls:
        return END

    return "master_agent"


class GraphBuilder:
    """
    Builds the V2 workflow graph with execution engine support.

    This class creates a graph with two main nodes:
    - master_agent: Handles conversation and single-step tasks
    - execution_engine: Drives multi-step plan execution
    """
    def __init__(self):
        self.builder = StateGraph(State)

        # 添加节点
        self.builder.add_node("master_agent", master_agent_node)
        self.builder.add_node("execution_engine", execution_engine_node)

        # 设置入口点
        self.builder.set_entry_point("master_agent")

        # 添加条件边：从master_agent路由到不同节点
        self.builder.add_conditional_edges(
            "master_agent",
            should_continue,
            {
                "master_agent": "master_agent",
                "execution_engine": "execution_engine",
                "__end__": END
            }
        )

        # 执行引擎完成后直接结束
        self.builder.add_edge("execution_engine", END)

    def build(self, checkpointer):
        """
        Compiles the graph into a runnable, attaching the provided checkpointer.
        """
        return self.builder.compile(checkpointer=checkpointer) 