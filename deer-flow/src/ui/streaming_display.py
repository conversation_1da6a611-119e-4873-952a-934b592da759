"""
Enhanced streaming display system for DeerFlow V2.
Provides real-time feedback for multi-step creative workflows.
"""

import time
from typing import Dict, Any, List, Optional
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskID
from rich.syntax import Syntax
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from rich.columns import Columns
from rich.status import Status
from langchain_core.messages import BaseMessage

class StreamingDisplay:
    """Enhanced streaming display for DeerFlow V2 workflows."""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
        self.current_step = 0
        self.total_steps = 0
        self.step_status = {}  # step_id -> status info
        self.current_llm_output = ""
        self.progress = None
        self.live = None
        
    def start_workflow(self, plan: Optional[Dict] = None):
        """Start a new workflow display."""
        self.current_step = 0
        self.step_status = {}
        self.current_llm_output = ""
        
        if plan and plan.get('steps'):
            self.total_steps = len(plan['steps'])
            self.console.print(Panel(
                f"🎯 开始执行计划：{plan.get('original_task', '未知任务')}\n"
                f"📋 总共 {self.total_steps} 个步骤",
                title="[bold blue]工作流开始[/bold blue]",
                border_style="blue"
            ))
            
            # 显示计划概览
            self._show_plan_overview(plan['steps'])
        else:
            self.console.print(Panel(
                "🚀 开始执行任务...",
                title="[bold blue]任务开始[/bold blue]",
                border_style="blue"
            ))
    
    def _show_plan_overview(self, steps: List[Dict]):
        """显示计划概览."""
        table = Table(title="📋 执行计划", show_header=True, header_style="bold magenta")
        table.add_column("步骤", style="cyan", width=8)
        table.add_column("描述", style="white", min_width=30)
        table.add_column("工具", style="yellow", width=15)
        table.add_column("状态", style="green", width=10)
        
        for i, step in enumerate(steps, 1):
            table.add_row(
                f"步骤 {i}",
                step.get('description', '未知描述')[:50] + ("..." if len(step.get('description', '')) > 50 else ""),
                step.get('tool_to_use', '未知工具'),
                "⏳ 等待中"
            )
        
        self.console.print(table)
        self.console.print()
    
    def update_step_status(self, step_id: int, status: str, description: str = "", tool_name: str = ""):
        """更新步骤状态."""
        self.step_status[step_id] = {
            'status': status,
            'description': description,
            'tool_name': tool_name,
            'timestamp': time.time()
        }
        
        status_emoji = {
            'pending': '⏳',
            'running': '🔄',
            'completed': '✅',
            'failed': '❌'
        }
        
        status_color = {
            'pending': 'yellow',
            'running': 'blue',
            'completed': 'green',
            'failed': 'red'
        }
        
        emoji = status_emoji.get(status, '❓')
        color = status_color.get(status, 'white')
        
        self.console.print(Panel(
            f"{emoji} 步骤 {step_id}: {description}\n"
            f"🔧 工具: {tool_name}" if tool_name else f"{emoji} 步骤 {step_id}: {description}",
            title=f"[bold {color}]步骤状态更新[/bold {color}]",
            border_style=color
        ))
    
    def show_tool_call(self, tool_name: str, args: Dict[str, Any]):
        """显示工具调用."""
        # 格式化参数显示
        args_text = ""
        for key, value in args.items():
            if isinstance(value, str) and len(value) > 100:
                args_text += f"{key}: {value[:100]}...\n"
            else:
                args_text += f"{key}: {value}\n"
        
        self.console.print(Panel(
            Syntax(args_text.strip(), "yaml", theme="monokai"),
            title=f"[bold magenta]🔧 调用工具: {tool_name}[/bold magenta]",
            border_style="magenta"
        ))
    
    def show_tool_output(self, tool_name: str, output: Any, success: bool = True):
        """显示工具输出."""
        if isinstance(output, dict):
            # 结构化输出
            if output.get('success'):
                content = output.get('content', '执行成功')
                assets = output.get('assets', {})
                
                output_text = f"📄 内容: {content}\n"
                
                if assets:
                    output_text += "\n📁 生成的资产:\n"
                    for asset_type, asset_list in assets.items():
                        if isinstance(asset_list, list):
                            for asset in asset_list:
                                output_text += f"  • {asset_type}: {asset}\n"
                        else:
                            output_text += f"  • {asset_type}: {asset_list}\n"
                
                self.console.print(Panel(
                    output_text.strip(),
                    title=f"[bold green]✅ 工具输出: {tool_name}[/bold green]",
                    border_style="green"
                ))
            else:
                error_msg = output.get('error', '未知错误')
                self.console.print(Panel(
                    f"❌ 错误: {error_msg}",
                    title=f"[bold red]工具执行失败: {tool_name}[/bold red]",
                    border_style="red"
                ))
        else:
            # 简单文本输出
            border_style = "green" if success else "red"
            title_prefix = "✅" if success else "❌"
            
            self.console.print(Panel(
                str(output),
                title=f"[bold {border_style}]{title_prefix} 工具输出: {tool_name}[/bold {border_style}]",
                border_style=border_style
            ))
    
    def start_llm_streaming(self, context: str = ""):
        """开始LLM流式输出."""
        self.current_llm_output = ""
        if context:
            self.console.print(Panel(
                context,
                title="[bold cyan]🤖 AI思考中...[/bold cyan]",
                border_style="cyan"
            ))
    
    def update_llm_stream(self, token: str):
        """更新LLM流式输出."""
        self.current_llm_output += token
        # 这里可以实现实时更新显示
        # 为了简化，我们暂时只累积内容
    
    def finish_llm_streaming(self):
        """完成LLM流式输出."""
        if self.current_llm_output:
            self.console.print(Panel(
                self.current_llm_output,
                title="[bold cyan]🤖 AI回复[/bold cyan]",
                border_style="cyan"
            ))
    
    def show_direct_response(self, content: str):
        """显示直接响应（跳过规划的简单任务）."""
        self.console.print(Panel(
            "🚀 检测到简单任务，直接执行专家工具...",
            title="[bold blue]智能路由[/bold blue]",
            border_style="blue"
        ))
    
    def show_planning_mode(self):
        """显示规划模式."""
        self.console.print(Panel(
            "🧠 检测到复杂任务，创建执行计划...",
            title="[bold blue]智能规划[/bold blue]",
            border_style="blue"
        ))
    
    def show_final_result(self, content: str, assets: Optional[Dict] = None):
        """显示最终结果."""
        result_text = content
        
        if assets:
            result_text += "\n\n📁 生成的文件:\n"
            for asset_type, asset_list in assets.items():
                if isinstance(asset_list, list):
                    for asset in asset_list:
                        result_text += f"  • {asset}\n"
                else:
                    result_text += f"  • {asset_list}\n"
        
        self.console.print(Panel(
            result_text,
            title="[bold green]🎉 任务完成[/bold green]",
            border_style="green"
        ))
    
    def show_error(self, error_msg: str, context: str = ""):
        """显示错误信息."""
        error_text = f"❌ {error_msg}"
        if context:
            error_text += f"\n\n📋 上下文: {context}"
        
        self.console.print(Panel(
            error_text,
            title="[bold red]执行错误[/bold red]",
            border_style="red"
        ))
    
    def show_progress(self, current: int, total: int, description: str = ""):
        """显示进度条."""
        if not self.progress:
            self.progress = Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                console=self.console
            )
        
        # 更新或创建进度任务
        # 这里可以根据需要实现更复杂的进度跟踪
        pass
    
    def cleanup(self):
        """清理资源."""
        if self.live:
            self.live.stop()
        if self.progress:
            self.progress.stop()


# 全局显示实例
display = StreamingDisplay()
