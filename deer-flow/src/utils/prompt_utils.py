# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
prompt_utils.py

Provides utility functions for loading and managing prompts.
"""

import os
from langchain_core.prompts import ChatPromptTemplate

_PROMPT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "prompts")


def get_agent_prompt_template(name: str) -> ChatPromptTemplate:
    """
    Loads a prompt template from the prompts directory.

    Args:
        name: The base name of the prompt file (e.g., "planner_prompt_zh").

    Returns:
        A ChatPromptTemplate instance.
    """
    prompt_path = os.path.join(_PROMPT_DIR, f"{name}.md")
    if not os.path.exists(prompt_path):
        raise FileNotFoundError(f"Prompt file not found: {prompt_path}")

    with open(prompt_path, "r", encoding="utf-8") as f:
        prompt_str = f.read()

    return ChatPromptTemplate.from_template(prompt_str) 