# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
experts.py

This module defines high-level "expert tools" that are, under the hood,
fully-fledged agents. This encapsulates the complexity of an agent's
ReAct loop, memory, and multi-tool usage behind a simple, standardized
tool interface.

The Master Agent can then delegate complex, domain-specific tasks to these
experts without needing to know the low-level details of their operation.
"""

from typing import Optional, Dict, Any, List
import json
import re
import time
import logging
from langchain_core.tools import tool, StructuredTool
from pydantic.v1 import BaseModel, Field
from langgraph.config import get_stream_writer

from src.agents.factory import create_agent # <-- Import from the new factory file

logger = logging.getLogger(__name__)

class VisualExpertInput(BaseModel):
    """Input schema for the Visual Expert Tool."""
    task_description: str = Field(
        description="A clear, detailed, and specific description of the visual task to be performed. For example, 'Create a photorealistic image of a golden retriever playing fetch in a sunny park.'"
    )
    context: Optional[str] = Field(
        default=None,
        description="Optional context from previous steps, such as themes, character descriptions, or artistic styles that should be maintained."
    )
    step_inputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Structured input data from previous steps, including file references, parameters, and other relevant data."
    )

class AudioExpertInput(BaseModel):
    """Input schema for the Audio Expert Tool."""
    task_description: str = Field(
        description="A clear, detailed description of the audio task. For example, 'Generate a cheerful, upbeat pop song about summer vacation' or 'Convert the following text to speech using a deep male voice: \"Hello, world!\"'."
    )
    context: Optional[str] = Field(
        default=None,
        description="Optional context from previous steps, such as the mood of a scene, to guide the audio creation."
    )
    step_inputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Structured input data from previous steps, including file references, parameters, and other relevant data."
    )

class VideoExpertInput(BaseModel):
    """Input schema for the Video Expert Tool."""
    task_description: str = Field(
        description="A clear, detailed description of the video task. For example, 'Create a short, cinematic trailer of a knight fighting a dragon' or 'Turn the image at path /assets/hero.png into a 5-second video clip with a slow zoom effect'."
    )
    context: Optional[str] = Field(
        default=None,
        description="Optional context from previous steps, such as asset paths (images, audio) or scene descriptions, to guide the video creation."
    )
    step_inputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Structured input data from previous steps, including file references, parameters, and other relevant data."
    )


def get_visual_expert_tool(tools_list: list) -> StructuredTool:
    """
    Factory function to create the visual expert tool.
    This tool encapsulates a full visual creator agent.
    """
    
    # 1. Instantiate the agent that this tool will wrap
    # We reuse the same agent factory and tools as the original graph
    visual_agent_instance = create_agent(
        agent_name="visual_expert_agent",
        agent_type="visual_creator", # Reuse the same LLM config
        tools=tools_list,
        prompt_template_name="visual_creator_prompt" # Reuse the same prompt
    )

    # 2. Define the wrapper function that the Master Agent will call
    def run_visual_expert(task_description: str, context: Optional[str] = None, step_inputs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Invokes the visual expert agent to perform a task and returns a structured result.

        Args:
            task_description: A detailed description of the visual task
            context: Optional context information (for backward compatibility)
            step_inputs: Structured input data from previous steps

        Returns:
            Dict[str, Any]: A structured output containing:
                - success: Whether the operation was successful
                - content: The main textual output
                - assets: Dictionary of generated assets (images, files, etc.)
                - metadata: Additional information about the execution
                - error: Error message if any
        """
        try:
            # 0. Stream progress update
            try:
                writer = get_stream_writer()
                writer({"type": "expert_start", "expert": "visual", "task": task_description[:100] + "..." if len(task_description) > 100 else task_description})
            except:
                pass  # Stream writer not available in this context

            # 1. Prepare enhanced context
            enhanced_context = {
                "description": task_description,
                "last_step_context": context if context else "No context provided.",
            }

            # Add structured inputs if available
            if step_inputs:
                enhanced_context["step_inputs"] = step_inputs

                # Extract available files for easier reference
                file_paths = _extract_file_paths(step_inputs)
                if file_paths:
                    enhanced_context["available_files"] = file_paths
                    enhanced_context["file_context"] = f"Available files from previous steps: {', '.join(file_paths)}"

            # 2. Prepare agent input with enhanced context
            # 构建包含上下文信息的完整消息
            full_message = task_description

            # 如果有 step_inputs，添加到消息中
            if step_inputs:
                full_message += "\n\n**可用输入数据**:\n"
                for key, value in step_inputs.items():
                    full_message += f"- {key}: {value}\n"

                # 特别标注图片编辑任务
                if any(key in step_inputs for key in ['base_image', 'image_to_edit', 'source_image']):
                    full_message += "\n🔥 **重要**: 这是一个图片编辑任务！上面提供的图片URL是需要编辑的原始图片。"

            # 如果有可用文件，添加到消息中
            if enhanced_context.get("available_files"):
                full_message += f"\n\n**可用文件**: {', '.join(enhanced_context['available_files'])}"

            agent_input = {
                "messages": [("human", full_message)],
                **enhanced_context  # 将所有上下文信息展开到 state 中
            }

            # 3. Invoke the agent
            agent_result = visual_agent_instance.invoke(agent_input)

            # 4. Extract and parse the output
            final_output = agent_result.get("messages", [])[-1].content

            # 5. Parse agent output using the unified parser
            structured_output = _parse_expert_output(final_output, "visual")

            # 6. Stream completion update
            try:
                writer = get_stream_writer()
                writer({
                    "type": "expert_complete",
                    "expert": "visual",
                    "success": structured_output.get("success", False),
                    "assets_count": len(structured_output.get("assets", {}))
                })
            except:
                pass  # Stream writer not available in this context

            return structured_output

        except Exception as e:
            # Return error information in structured format
            return {
                "success": False,
                "content": f"Error executing visual expert: {str(e)}",
                "assets": {},
                "metadata": {"error_type": type(e).__name__},
                "error": str(e)
            }

    # 3. Create the StructuredTool
    visual_expert = StructuredTool.from_function(
        func=run_visual_expert,
        name="visual_expert",
        description="A powerful visual creation expert that returns structured output. Use this tool for any tasks involving generating or editing images. Provide a very detailed description of the desired outcome. The tool returns a structured response with success status, content, generated assets, and metadata.",
        args_schema=VisualExpertInput
    )
    
    return visual_expert


def get_audio_expert_tool(tools_list: list) -> StructuredTool:
    """
    Factory function to create the audio expert tool.
    This tool encapsulates a full audio creator agent capable of music generation and text-to-speech.
    """
    
    # 1. Instantiate the agent that this tool will wrap
    audio_agent_instance = create_agent(
        agent_name="audio_expert_agent",
        agent_type="audio_creator", # Reuse the same LLM config
        tools=tools_list,
        prompt_template_name="audio_creator_prompt" # Reuse the same prompt
    )

    # 2. Define the wrapper function that the Master Agent will call
    def run_audio_expert(task_description: str, context: Optional[str] = None, step_inputs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Invokes the audio expert agent to perform a task and returns a structured result.

        Args:
            task_description: A detailed description of the audio task
            context: Optional context information (for backward compatibility)
            step_inputs: Structured input data from previous steps

        Returns:
            Dict[str, Any]: A structured output containing success, content, assets, metadata, and error info
        """
        try:
            # 1. Prepare enhanced context
            enhanced_context = {
                "description": task_description,
                "last_step_context": context if context else "No context provided.",
            }

            # Add structured inputs if available
            if step_inputs:
                enhanced_context["step_inputs"] = step_inputs

                # Extract available files for easier reference
                file_paths = _extract_file_paths(step_inputs)
                if file_paths:
                    enhanced_context["available_files"] = file_paths
                    enhanced_context["file_context"] = f"Available files from previous steps: {', '.join(file_paths)}"

            # 2. Prepare agent input with enhanced context (consistent with Visual Expert)
            # 构建包含上下文信息的完整消息
            full_message = task_description

            # 如果有 step_inputs，添加到消息中
            if step_inputs:
                full_message += "\n\n**可用输入数据**:\n"
                for key, value in step_inputs.items():
                    full_message += f"- {key}: {value}\n"

            # 如果有可用文件，添加到消息中
            if enhanced_context.get("available_files"):
                full_message += f"\n\n**可用文件**: {', '.join(enhanced_context['available_files'])}"

            agent_input = {
                "messages": [("human", full_message)],
                **enhanced_context  # 将所有上下文信息展开到 state 中
            }

            # 3. Invoke the agent
            agent_result = audio_agent_instance.invoke(agent_input)

            # 4. Extract and parse the output
            final_output = agent_result.get("messages", [])[-1].content

            # 5. Parse into structured format
            structured_output = _parse_expert_output(final_output, "audio")

            return structured_output

        except Exception as e:
            # Return error information in structured format
            return {
                "success": False,
                "content": f"Error executing audio expert: {str(e)}",
                "assets": {},
                "metadata": {"error_type": type(e).__name__},
                "error": str(e)
            }

    # 3. Create the StructuredTool
    audio_expert = StructuredTool.from_function(
        func=run_audio_expert,
        name="audio_expert",
        description="A powerful audio creation expert that returns structured output. Use this for tasks like generating music from a text description, or converting text into speech. Provide a clear and detailed description of the desired audio. The tool returns a structured response with success status, content, generated assets, and metadata.",
        args_schema=AudioExpertInput
    )
    
    return audio_expert


def get_video_expert_tool(tools_list: list) -> StructuredTool:
    """
    Factory function to create the video expert tool.
    This tool encapsulates a full video creator agent capable of text-to-video, image-to-video, and video synthesis.
    """
    
    # 1. Instantiate the agent that this tool will wrap
    video_agent_instance = create_agent(
        agent_name="video_expert_agent",
        agent_type="video_creator", # Reuse the same LLM config
        tools=tools_list,
        prompt_template_name="video_creator_prompt" # Reuse the same prompt
    )

    # 2. Define the wrapper function that the Master Agent will call
    def run_video_expert(task_description: str, context: Optional[str] = None, step_inputs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Invokes the video expert agent to perform a task and returns a structured result.

        Args:
            task_description: A detailed description of the video task
            context: Optional context information (for backward compatibility)
            step_inputs: Structured input data from previous steps

        Returns:
            Dict[str, Any]: A structured output containing success, content, assets, metadata, and error info
        """
        try:
            # 1. Prepare enhanced context
            enhanced_context = {
                "description": task_description,
                "last_step_context": context if context else "No context provided.",
            }

            # Add structured inputs if available
            if step_inputs:
                enhanced_context["step_inputs"] = step_inputs

                # Extract available files for easier reference
                file_paths = _extract_file_paths(step_inputs)
                if file_paths:
                    enhanced_context["available_files"] = file_paths
                    enhanced_context["file_context"] = f"Available files from previous steps: {', '.join(file_paths)}"

            # 2. Prepare agent input with enhanced context (consistent with Visual Expert)
            # 构建包含上下文信息的完整消息
            full_message = task_description

            # 如果有 step_inputs，添加到消息中
            if step_inputs:
                full_message += "\n\n**可用输入数据**:\n"
                for key, value in step_inputs.items():
                    full_message += f"- {key}: {value}\n"

            # 如果有可用文件，添加到消息中
            if enhanced_context.get("available_files"):
                full_message += f"\n\n**可用文件**: {', '.join(enhanced_context['available_files'])}"

            agent_input = {
                "messages": [("human", full_message)],
                **enhanced_context  # 将所有上下文信息展开到 state 中
            }

            # 3. Invoke the agent
            agent_result = video_agent_instance.invoke(agent_input)

            # 4. Extract and parse the output
            final_output = agent_result.get("messages", [])[-1].content

            # 5. Parse into structured format
            structured_output = _parse_expert_output(final_output, "video")

            return structured_output

        except Exception as e:
            # Return error information in structured format
            return {
                "success": False,
                "content": f"Error executing video expert: {str(e)}",
                "assets": {},
                "metadata": {"error_type": type(e).__name__},
                "error": str(e)
            }

    # 3. Create the StructuredTool
    video_expert = StructuredTool.from_function(
        func=run_video_expert,
        name="video_expert",
        description="A powerful video creation expert that returns structured output. Use this for tasks like creating video from text, animating existing images, or combining video clips together. Provide a clear and detailed description of the desired video. The tool returns a structured response with success status, content, generated assets, and metadata.",
        args_schema=VideoExpertInput
    )
    
    return video_expert

# A list of the high-level expert tools for the Master Agent
# We pass an empty list for the sub-tools for now, as the V2 Master Agent
# doesn't need to know about them directly. The expert agents themselves
# will be instantiated with the correct low-level tools.
expert_tools = [
    get_visual_expert_tool(tools_list=[]),
    get_audio_expert_tool(tools_list=[]),
    get_video_expert_tool(tools_list=[]),
]


# For standalone testing of the tool
if __name__ == '__main__':
    # This setup is required to run the tool standalone
    from src.config.configuration import Configuration
    config = Configuration.from_env()

    # To test standalone, we need to manually create the tool lists
    # This mirrors the logic in tools/__init__.py
    from src.tools.image.text_to_image import get_jmeng_image_generation_tool
    from src.tools.image.flux_image_edit import get_flux_image_edit_tool
    from src.tools.image.multi_image_edit import get_multi_image_flux_edit_tool
    from src.tools.audio.music_generation import get_suno_music_generation_tool
    from src.tools.audio.text_to_speech import get_text_to_speech_tool, get_voice_clone_tool, get_text_to_voice_tool
    from src.tools.video import get_image_to_video_tool, get_text_to_video_tool, get_video_synthesis_tool

    _visual_tools = [
        get_jmeng_image_generation_tool(config),
        get_flux_image_edit_tool(config),
        get_multi_image_flux_edit_tool(config)
    ]
    _audio_tools = [
        get_suno_music_generation_tool(config),
        get_text_to_speech_tool(config),
        get_voice_clone_tool(config),
        get_text_to_voice_tool(config)
    ]
    _video_tools = [
        get_image_to_video_tool(config),
        get_text_to_video_tool(config),
        get_video_synthesis_tool(config)
    ]


    print("--- Testing Visual Expert Tool ---")
    
    # Create the tool
    visual_tool = get_visual_expert_tool(tools_list=_visual_tools)

    print(f"Tool Name: {visual_tool.name}")
    print(f"Tool Description: {visual_tool.description}")
    print(f"Tool Args: {visual_tool.args_schema.schema_json(indent=2)}")
    
    # Define a test task
    test_task = "Create an oil painting of a majestic lion overlooking the savannah at sunset."
    
    # Run the tool
    try:
        result = visual_tool.invoke({
            "task_description": test_task
        })
        print("\n--- Invocation Result ---")
        print(result)
    except Exception as e:
        print(f"\n--- An error occurred ---")
        print(f"Error: {e}")
        print("\nNOTE: This might be due to missing environment variables or configurations.")
        print("Please ensure your .env file is set up correctly in the project root.")

    print("\n" + "="*50 + "\n")

    print("--- Testing Audio Expert Tool ---")
    
    # Create the tool
    audio_tool = get_audio_expert_tool(tools_list=_audio_tools)

    print(f"Tool Name: {audio_tool.name}")
    print(f"Tool Description: {audio_tool.description}")
    print(f"Tool Args: {audio_tool.args_schema.schema_json(indent=2)}")
    
    # Define a test task
    test_audio_task = "Generate a short, epic orchestral piece for a movie trailer."
    
    # Run the tool
    try:
        result = audio_tool.invoke({
            "task_description": test_audio_task
        })
        print("\n--- Invocation Result ---")
        print(result)
    except Exception as e:
        print(f"\n--- An error occurred ---")
        print(f"Error: {e}")
        print("\nNOTE: This might be due to missing environment variables or configurations.")
        print("Please ensure your .env file is set up correctly in the project root.")

    print("\n" + "="*50 + "\n")

    print("--- Testing Video Expert Tool ---")
    
    # Create the tool
    video_tool = get_video_expert_tool(tools_list=_video_tools)

    print(f"Tool Name: {video_tool.name}")
    print(f"Tool Description: {video_tool.description}")
    print(f"Tool Args: {video_tool.args_schema.schema_json(indent=2)}")
    
    # Define a test task
    test_video_task = "Create a dynamic 3-second video of a futuristic car speeding through a neon-lit city at night."
    
    # Run the tool
    try:
        result = video_tool.invoke({
            "task_description": test_video_task
        })
        print("\n--- Invocation Result ---")
        print(result)
    except Exception as e:
        print(f"\n--- An error occurred ---")
        print(f"Error: {e}")
        print("\nNOTE: This might be due to missing environment variables or configurations.")
        print("Please ensure your .env file is set up correctly in the project root.")


# ===== Helper Functions =====

def _extract_file_paths(step_inputs: Dict[str, Any]) -> List[str]:
    """
    Extract file paths and URLs from step inputs.

    Args:
        step_inputs: Dictionary containing step input data

    Returns:
        List[str]: List of file paths and URLs found in the inputs
    """
    file_paths = []

    def extract_recursive(obj):
        """Recursively extract file paths from nested structures."""
        if isinstance(obj, dict):
            for key, value in obj.items():
                # Look for common file-related keys and values that look like URLs/paths
                if key.lower() in ["image_url", "audio_url", "video_url", "file_path", "url", "path", "src", "href"]:
                    if isinstance(value, str) and (value.startswith("http") or value.startswith("/")):
                        file_paths.append(value)
                # Also check if the key name suggests it contains a file/URL
                elif any(keyword in key.lower() for keyword in ["image", "audio", "video", "file", "music", "sound"]):
                    if isinstance(value, str) and (value.startswith("http") or value.startswith("/")):
                        file_paths.append(value)
                elif isinstance(value, (dict, list)):
                    extract_recursive(value)
        elif isinstance(obj, list):
            for item in obj:
                extract_recursive(item)
        elif isinstance(obj, str):
            # Check if the string itself looks like a URL or file path
            if obj.startswith("http") or (obj.startswith("/") and "." in obj):
                file_paths.append(obj)

    extract_recursive(step_inputs)
    return list(set(file_paths))  # Remove duplicates


def _parse_expert_output(final_output: Any, agent_type: str) -> Dict[str, Any]:
    """
    Parse expert agent output - prioritize structured ASSETS blocks, fallback to intelligent extraction.

    Args:
        final_output: Raw output from the expert agent
        agent_type: Type of agent (visual, audio, video)

    Returns:
        Dict[str, Any]: Structured output dictionary
    """
    try:
        import json
        import re

        output_str = str(final_output)

        # 1. Try to extract structured ASSETS block
        assets_pattern = r'<ASSETS>\s*(.*?)\s*</ASSETS>'
        assets_match = re.search(assets_pattern, output_str, re.DOTALL)

        if assets_match:
            try:
                # Parse the JSON inside ASSETS block
                json_content = assets_match.group(1).strip()
                assets_json = json.loads(json_content)

                # Extract natural language content (everything before ASSETS block)
                content_part = output_str[:assets_match.start()].strip()

                # Build structured output
                structured_output = {
                    "success": True,
                    "content": content_part,
                    "assets": assets_json,
                    "metadata": {
                        "agent_type": agent_type,
                        "parsing_method": "structured_assets_block",
                        "timestamp": time.time()
                    },
                    "error": None
                }

                logger.info(f"Successfully parsed structured ASSETS block from {agent_type} agent")
                return structured_output

            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON in ASSETS block: {e}")
                # Fall through to other parsing methods

        # 2. Try to parse as pure JSON (legacy format)
        if isinstance(final_output, str):
            # Clean the output - remove any markdown code blocks
            clean_output = output_str.strip()
            if clean_output.startswith('```json'):
                clean_output = clean_output[7:]
            if clean_output.endswith('```'):
                clean_output = clean_output[:-3]
            clean_output = clean_output.strip()

            structured_output = json.loads(clean_output)

            # Validate required fields
            if not isinstance(structured_output, dict):
                raise ValueError("Output is not a dictionary")

            # Ensure required fields exist
            if "success" not in structured_output:
                structured_output["success"] = True
            if "content" not in structured_output:
                structured_output["content"] = "任务完成"
            if "assets" not in structured_output:
                structured_output["assets"] = {}
            if "metadata" not in structured_output:
                structured_output["metadata"] = {"agent_type": agent_type}
            if "error" not in structured_output:
                structured_output["error"] = None

            logger.info(f"Successfully parsed pure JSON from {agent_type} agent")
            return structured_output

        elif isinstance(final_output, dict):
            # If final_output is already a dict, use it directly
            return final_output

    except (json.JSONDecodeError, ValueError, TypeError) as e:
        logger.info(f"JSON parsing failed, using intelligent extraction for {agent_type} agent: {e}")

    # 3. Fallback to intelligent extraction from natural language
    logger.info(f"Using natural language extraction for {agent_type} agent output")
    return _extract_structured_from_natural_output(str(final_output), agent_type)


def _extract_structured_from_natural_output(output: str, agent_type: str) -> Dict[str, Any]:
    """
    智能提取：从子Agent的自然语言输出中提取资产管理信息
    专注于提取文件URL、名称、描述等资产管理所需的信息
    """
    import re

    # 1. 判断成功状态
    failure_indicators = ['失败', '错误', '无法', '抱歉', '问题', '不能', 'error', 'failed', 'unable']
    success = not any(indicator in output.lower() for indicator in failure_indicators)

    # 2. 提取资产信息（URL + 名称 + 描述）
    assets = {}

    # 提取图片信息
    image_urls = re.findall(r'https?://[^\s\)\]]+\.(?:jpg|jpeg|png|gif|webp)', output, re.IGNORECASE)
    if image_urls:
        assets['images'] = []
        for i, url in enumerate(image_urls):
            # 尝试提取文件名称和描述
            asset_info = {
                "url": url,
                "name": _extract_asset_name(output, url, agent_type),
                "description": _extract_asset_description(output, url, i)
            }
            assets['images'].append(asset_info)

        # 设置主要图片
        assets['primary_image'] = assets['images'][0] if assets['images'] else None

    # 提取音频信息
    audio_urls = re.findall(r'https?://[^\s\)\]]+\.(?:mp3|wav|m4a|aac)', output, re.IGNORECASE)
    if audio_urls:
        assets['audio'] = []
        for i, url in enumerate(audio_urls):
            asset_info = {
                "url": url,
                "name": _extract_asset_name(output, url, agent_type),
                "description": _extract_asset_description(output, url, i)
            }
            assets['audio'].append(asset_info)

        assets['primary_audio'] = assets['audio'][0] if assets['audio'] else None

    # 提取视频信息
    video_urls = re.findall(r'https?://[^\s\)\]]+\.(?:mp4|avi|mov|webm)', output, re.IGNORECASE)
    if video_urls:
        assets['video'] = []
        for i, url in enumerate(video_urls):
            asset_info = {
                "url": url,
                "name": _extract_asset_name(output, url, agent_type),
                "description": _extract_asset_description(output, url, i)
            }
            assets['video'].append(asset_info)

        assets['primary_video'] = assets['video'][0] if assets['video'] else None

    # 3. 提取整体内容描述
    content = _extract_main_content(output)

    # 4. 提取工具使用信息
    tool_used = _extract_tool_used(output)

    # 5. 构建结构化输出
    result = {
        "success": success,
        "content": content,
        "assets": assets,
        "metadata": {
            "agent_type": agent_type,
            "extraction_method": "asset_management_focused",
            "timestamp": time.time(),
            "tool_used": tool_used,
            "total_assets": sum(len(v) if isinstance(v, list) else (1 if v else 0)
                              for k, v in assets.items()
                              if not k.startswith('primary_'))
        },
        "error": None if success else "从输出中检测到可能的执行问题"
    }

    return result


def _extract_asset_name(output: str, url: str, agent_type: str) -> str:
    """智能提取资产名称 - 更灵活和通用"""
    import re

    # 1. 优先查找明确的文件名标记
    name_patterns = [
        r'文件名[：:]\s*([^\n\r]+)',
        r'名称[：:]\s*([^\n\r]+)',
        r'标题[：:]\s*([^\n\r]+)',
        r'作品[：:]\s*([^\n\r]+)',
        r'图片名[：:]\s*([^\n\r]+)',
        r'音频名[：:]\s*([^\n\r]+)',
        r'视频名[：:]\s*([^\n\r]+)'
    ]

    for pattern in name_patterns:
        matches = re.findall(pattern, output)
        if matches:
            name = matches[0].strip()
            # 清理名称，移除markdown格式和多余符号
            name = re.sub(r'^[*\-•\s\[\]]+|[*\-•\s\[\]]+$', '', name)
            name = re.sub(r'^\*\*|\*\*$', '', name)  # 移除粗体标记
            name = re.sub(r'^-\s*\*\*|\*\*\s*$', '', name)  # 移除列表和粗体标记
            if name and len(name) > 2 and len(name) < 50:
                return name

    # 2. 查找图片markdown语法中的alt文本
    img_pattern = r'!\[([^\]]+)\]\([^\)]*' + re.escape(url.split('/')[-1][:10]) + r'[^\)]*\)'
    img_matches = re.findall(img_pattern, output)
    if img_matches:
        alt_text = img_matches[0].strip()
        if alt_text and len(alt_text) > 2 and len(alt_text) < 50:
            return alt_text

    # 3. 从输出中提取描述性短语（更智能的方式）
    # 查找包含关键词的句子片段
    descriptive_patterns = [
        r'([^。！？\n]*(?:可爱|美丽|精美|温馨|治愈|梦幻|科幻|现代|古典|卡通|写实)[^。！？\n]*)',
        r'([^。！？\n]*(?:小狗|小猫|动物|人物|风景|建筑|logo|海报)[^。！？\n]*)',
        r'([^。！？\n]*(?:戴着|穿着|拿着|坐着|站着|飞舞|绽放)[^。！？\n]*)'
    ]

    for pattern in descriptive_patterns:
        matches = re.findall(pattern, output)
        for match in matches:
            clean_match = re.sub(r'[，。！？\s]+', '', match).strip()
            if 5 < len(clean_match) < 30 and not any(word in clean_match for word in ['成功', '完成', '生成', '创作']):
                return clean_match

    # 4. 从第一句话中提取关键信息
    first_sentence = output.split('。')[0].split('！')[0].split('？')[0]
    if len(first_sentence) > 10:
        # 移除常见的开头词
        clean_sentence = re.sub(r'^(我|已经|成功|完成|为您|给您|这是|这张|这个)', '', first_sentence)
        clean_sentence = re.sub(r'[，。！？\s]+$', '', clean_sentence).strip()
        if 5 < len(clean_sentence) < 40:
            return clean_sentence

    # 5. 基于agent类型和内容的智能默认名称
    content_keywords = {
        'visual': {
            '猫': '可爱小猫',
            '狗': '可爱小狗',
            '动物': '动物图片',
            '风景': '风景图片',
            '人物': '人物图片',
            '建筑': '建筑图片',
            'logo': 'Logo设计',
            '海报': '海报设计'
        },
        'audio': {
            '音乐': '音乐作品',
            '歌曲': '歌曲作品',
            '背景': '背景音乐',
            '配乐': '配乐作品'
        },
        'video': {
            '视频': '视频作品',
            '动画': '动画作品',
            '短片': '短片作品'
        }
    }

    if agent_type in content_keywords:
        for keyword, default_name in content_keywords[agent_type].items():
            if keyword in output:
                return default_name

    # 6. 最终默认名称
    type_names = {
        'visual': '创意图片',
        'audio': '音频创作',
        'video': '视频创作'
    }
    return type_names.get(agent_type, '创作作品')


def _extract_asset_description(output: str, url: str, index: int) -> str:
    """智能提取资产描述 - 更丰富和自然"""
    import re

    # 1. 优先查找明确的描述标记
    desc_patterns = [
        r'描述[：:]\s*([^\n\r]+)',
        r'说明[：:]\s*([^\n\r]+)',
        r'特点[：:]\s*([^\n\r]+)',
        r'内容[：:]\s*([^\n\r]+)',
        r'风格[：:]\s*([^\n\r]+)',
        r'效果[：:]\s*([^\n\r]+)'
    ]

    for pattern in desc_patterns:
        matches = re.findall(pattern, output)
        if matches and len(matches) > index:
            desc = matches[index].strip()
            # 清理描述，保留完整的句子
            desc = re.sub(r'^[*\-•\s]+|[*\-•\s]+$', '', desc)
            if desc and len(desc) > 5 and len(desc) < 200:
                return desc

    # 2. 查找包含丰富描述的完整句子
    sentences = re.split(r'[。！？]', output)
    for sentence in sentences:
        sentence = sentence.strip()
        # 查找包含视觉描述词汇的句子
        if any(word in sentence for word in ['色彩', '风格', '表情', '背景', '光线', '氛围', '质感', '细节']):
            if 10 < len(sentence) < 150 and not any(skip in sentence for skip in ['成功', '完成', '生成', '工具']):
                return sentence

    # 3. 提取URL附近的上下文描述
    lines = output.split('\n')
    for i, line in enumerate(lines):
        if url in line:
            # 查找前后几行的描述性内容
            context_lines = lines[max(0, i-3):i+4]
            for context_line in context_lines:
                if url not in context_line and '文件名' not in context_line:
                    clean_line = re.sub(r'[*\-•\[\]()]+', '', context_line).strip()
                    clean_line = re.sub(r'^(描述|说明|特点|内容)[：:]?\s*', '', clean_line)
                    if 15 < len(clean_line) < 120:
                        return clean_line

    # 4. 从整体输出中提取最有描述性的句子
    # 查找包含形容词和具体描述的句子
    descriptive_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        # 计算描述性词汇的密度
        descriptive_words = ['可爱', '美丽', '精美', '温馨', '治愈', '梦幻', '科幻', '现代', '古典',
                           '卡通', '写实', '明亮', '柔和', '鲜艳', '自然', '生动', '细腻', '清晰']
        word_count = sum(1 for word in descriptive_words if word in sentence)

        if word_count >= 2 and 20 < len(sentence) < 100:
            descriptive_sentences.append((sentence, word_count))

    if descriptive_sentences:
        # 返回描述性词汇最多的句子
        best_sentence = max(descriptive_sentences, key=lambda x: x[1])[0]
        return best_sentence

    # 5. 基于内容类型的智能默认描述
    content_descriptions = {
        '猫': '一只可爱的猫咪，表情生动自然',
        '狗': '一只可爱的小狗，神态活泼可爱',
        '动物': '动物形象生动，细节丰富',
        '风景': '风景优美，色彩和谐',
        '人物': '人物形象生动，表情自然',
        '建筑': '建筑设计精美，线条流畅',
        'logo': 'Logo设计简洁大方，识别度高',
        '海报': '海报设计吸引眼球，视觉效果佳',
        '墨镜': '墨镜设计时尚，与整体风格协调',
        '音乐': '音乐旋律优美，节奏感强',
        '视频': '视频画面流畅，视觉效果佳'
    }

    for keyword, desc in content_descriptions.items():
        if keyword in output:
            return desc

    # 6. 最终默认描述
    return "创作内容丰富，视觉效果良好"


def _extract_main_content(output: str) -> str:
    """提取主要内容描述"""
    import re

    # 移除markdown图片语法
    content = re.sub(r'!\[([^\]]*)\]\([^\)]+\)', r'\1', output)

    # 移除URL
    content = re.sub(r'https?://[^\s]+', '', content)

    # 移除多余的换行和空格
    content = re.sub(r'\n\s*\n', '\n', content)
    content = content.strip()

    # 如果内容太长，提取关键部分
    if len(content) > 200:
        # 尝试提取第一段作为主要描述
        first_paragraph = content.split('\n')[0]
        if len(first_paragraph) > 50:
            content = first_paragraph
        else:
            content = content[:200] + "..."

    return content


def _extract_tool_used(output: str) -> str:
    """提取使用的工具信息"""
    tool_keywords = {
        'jmeng': 'jmeng_image_generation',
        'flux': 'flux_image_edit',
        'suno': 'suno_music_generation',
        'text_to_speech': 'text_to_speech',
        'voice_clone': 'voice_clone'
    }

    output_lower = output.lower()
    for keyword, tool_name in tool_keywords.items():
        if keyword in output_lower:
            return tool_name

    return None


def _parse_agent_output_fallback(output: str, agent_type: str) -> Dict[str, Any]:
    """
    Parse agent output into structured format.

    Args:
        output: Raw text output from the agent
        agent_type: Type of agent (visual, audio, video)

    Returns:
        Dict[str, Any]: Structured output dictionary
    """
    # Initialize the result structure
    result = {
        "success": True,
        "content": output,
        "assets": {},
        "metadata": {
            "agent_type": agent_type,
            "timestamp": time.time(),
            "output_length": len(output)
        },
        "error": None
    }

    # Extract assets based on agent type and content
    assets = _extract_assets_from_output(output, agent_type)
    if assets:
        result["assets"] = assets

    # Check for error indicators in the output
    error_indicators = ["error", "failed", "unable", "cannot", "exception"]
    if any(indicator in output.lower() for indicator in error_indicators):
        result["success"] = False
        result["error"] = "Potential execution error detected in output"

    # Add agent-specific metadata
    if agent_type == "visual":
        result["metadata"]["expected_output_type"] = "image"
    elif agent_type == "audio":
        result["metadata"]["expected_output_type"] = "audio"
    elif agent_type == "video":
        result["metadata"]["expected_output_type"] = "video"

    return result


def _extract_assets_from_output(output: str, agent_type: str) -> Dict[str, Any]:
    """
    Extract asset information from agent output text.

    Args:
        output: Raw text output from the agent
        agent_type: Type of agent to guide extraction

    Returns:
        Dict[str, Any]: Dictionary of extracted assets
    """
    assets = {}

    # Common URL patterns
    url_patterns = {
        "images": [
            r'https?://[^\s]+\.(?:jpg|jpeg|png|gif|webp|bmp)',
            r'https?://[^\s]*(?:image|img|picture)[^\s]*',
            r'Image\s*(?:URL|url|link)[:\s]*(\S+)',
            r'Generated\s*image[:\s]*(\S+)'
        ],
        "audio": [
            r'https?://[^\s]+\.(?:mp3|wav|m4a|aac|ogg|flac)',
            r'https?://[^\s]*(?:audio|music|sound)[^\s]*',
            r'Audio\s*(?:URL|url|link)[:\s]*(\S+)',
            r'Generated\s*(?:audio|music)[:\s]*(\S+)'
        ],
        "video": [
            r'https?://[^\s]+\.(?:mp4|avi|mov|mkv|webm|flv)',
            r'https?://[^\s]*(?:video|movie|clip)[^\s]*',
            r'Video\s*(?:URL|url|link)[:\s]*(\S+)',
            r'Generated\s*video[:\s]*(\S+)'
        ]
    }

    # Extract based on agent type and general patterns
    for asset_type, patterns in url_patterns.items():
        found_assets = []
        for pattern in patterns:
            matches = re.findall(pattern, output, re.IGNORECASE)
            # Handle both capture groups and full matches
            for match in matches:
                if isinstance(match, tuple):
                    # If there are capture groups, take the first non-empty one
                    for group in match:
                        if group.strip():
                            found_assets.append(group.strip())
                            break
                elif isinstance(match, str) and match.strip():
                    found_assets.append(match.strip())

        if found_assets:
            # Remove duplicates and clean up
            unique_assets = list(set(found_assets))
            # Filter out obviously invalid URLs
            valid_assets = [asset for asset in unique_assets if asset.startswith('http') and len(asset) > 10]
            if valid_assets:
                assets[asset_type] = valid_assets

    # Agent-specific extraction logic
    if agent_type == "visual" and not assets.get("images"):
        # Look for any URLs that might be images
        general_urls = re.findall(r'https?://[^\s]+', output)
        potential_images = [url for url in general_urls if any(ext in url.lower() for ext in ['.jpg', '.png', '.gif', '.webp'])]
        if potential_images:
            assets["images"] = potential_images

    return assets