# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
enhanced_state_management.py

Enhanced state management tools that work with the new EnhancedPlan model.
These tools provide better parameter resolution, parallel execution support,
and improved error handling.
"""

from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from datetime import datetime
import json

from src.graph_v2.types import State
from src.graph_v2.enhanced_models import EnhancedPlan, EnhancedStep, StepStatus
from src.graph_v2.parameter_resolver import ParameterResolverFactory


class GetCurrentPlanInput(BaseModel):
    """Input schema for getting current plan."""
    include_context: bool = Field(default=False, description="Whether to include execution context")


class GetNextStepsInput(BaseModel):
    """Input schema for getting next executable steps."""
    max_steps: int = Field(default=3, description="Maximum number of steps to return")
    include_parallel: bool = Field(default=True, description="Whether to include parallel execution groups")


class ResolveStepInputsInput(BaseModel):
    """Input schema for resolving step inputs."""
    step_id: str = Field(..., description="ID of the step to resolve inputs for")
    validate_references: bool = Field(default=True, description="Whether to validate all references")


class UpdateStepStatusInput(BaseModel):
    """Input schema for updating step status."""
    step_id: str = Field(..., description="ID of the step to update")
    status: str = Field(..., description="New status (pending, in_progress, completed, failed)")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Step execution result")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")


class GetExecutionSummaryInput(BaseModel):
    """Input schema for getting execution summary."""
    include_logs: bool = Field(default=False, description="Whether to include execution logs")


@tool
def get_current_enhanced_plan(state: State) -> Dict[str, Any]:
    """
    Get the current enhanced plan with detailed information.

    Args:
        state: Current conversation state

    Returns:
        Dictionary containing plan information
    """
    include_context = False  # Default value
    try:
        plan = state.get("plan")
        if not plan:
            return {
                "plan": "No plan is currently available",
                "plan_type": "none",
                "message": "No plan has been created yet. Consider creating one with planner_tool or template tools."
            }
        
        # Check if it's an enhanced plan
        if isinstance(plan, EnhancedPlan):
            plan_info = {
                "plan_id": plan.plan_id,
                "original_task": plan.original_task,
                "total_steps": len(plan.steps),
                "plan_type": "enhanced",
                "is_from_template": plan.is_from_template,
                "source_template": plan.source_template,
                "execution_summary": plan.get_execution_summary()
            }
            
            # Add step details
            plan_info["steps"] = []
            for step in plan.steps:
                step_info = {
                    "step_id": step.step_id,
                    "name": step.name,
                    "description": step.description,
                    "tool_to_use": step.tool_to_use,
                    "status": step.status.value,
                    "step_type": step.step_type.value,
                    "dependencies": step.dependencies,
                    "parallel_group": step.parallel_group,
                    "estimated_duration": step.estimated_duration,
                    "retry_count": step.retry_count,
                    "max_retries": step.retry_config.max_retries
                }
                
                if step.result:
                    step_info["has_result"] = True
                    step_info["result_summary"] = {
                        "success": step.result.get("success", False),
                        "assets_count": len(step.result.get("assets", {}))
                    }
                
                plan_info["steps"].append(step_info)
            
            # Add execution context if requested
            if include_context:
                plan_info["execution_context"] = {
                    "current_step_id": plan.execution_context.current_step_id,
                    "shared_variables": plan.execution_context.shared_variables,
                    "progress_percentage": plan.execution_context.get_progress_percentage(),
                    "log_entries": len(plan.execution_context.execution_log)
                }
            
            return plan_info
        
        else:
            # Legacy plan format
            return {
                "plan": str(plan),
                "plan_type": "legacy",
                "message": "This is a legacy plan format. Consider upgrading to enhanced plan."
            }
            
    except Exception as e:
        return {
            "plan": "Error retrieving plan",
            "error": str(e),
            "plan_type": "error"
        }


@tool
def get_next_executable_steps(state: State) -> Dict[str, Any]:
    """
    Get the next steps that can be executed, with support for parallel execution.

    Args:
        state: Current conversation state

    Returns:
        Dictionary containing executable steps information
    """
    max_steps = 3  # Default value
    include_parallel = True  # Default value
    try:
        plan = state.get("plan")
        if not plan or not isinstance(plan, EnhancedPlan):
            return {
                "executable_steps": [],
                "message": "No enhanced plan available",
                "parallel_groups": {}
            }
        
        # Get next executable steps
        executable_steps = plan.get_next_executable_steps()
        
        # Limit the number of steps
        limited_steps = executable_steps[:max_steps]
        
        result = {
            "executable_steps": [],
            "total_available": len(executable_steps),
            "returned_count": len(limited_steps)
        }
        
        # Format step information
        for step in limited_steps:
            step_info = {
                "step_id": step.step_id,
                "name": step.name,
                "description": step.description,
                "tool_to_use": step.tool_to_use,
                "step_type": step.step_type.value,
                "priority": step.priority,
                "estimated_duration": step.estimated_duration,
                "parallel_group": step.parallel_group,
                "dependencies_met": True  # If it's executable, dependencies are met
            }
            result["executable_steps"].append(step_info)
        
        # Add parallel group information if requested
        if include_parallel:
            parallel_groups = plan.get_parallel_groups()
            result["parallel_groups"] = {}
            for group_id, group_steps in parallel_groups.items():
                result["parallel_groups"][group_id] = [
                    {
                        "step_id": step.step_id,
                        "name": step.name,
                        "status": step.status.value
                    }
                    for step in group_steps
                ]
        
        # Add execution recommendations
        if not limited_steps:
            if plan.is_complete():
                result["message"] = "Plan execution is complete"
            elif plan.has_failed():
                failed_steps = plan.get_failed_steps()
                retryable_steps = plan.get_retryable_steps()
                result["message"] = f"Plan has failed steps. {len(retryable_steps)} steps can be retried."
                result["failed_steps"] = [s.step_id for s in failed_steps]
                result["retryable_steps"] = [s.step_id for s in retryable_steps]
            else:
                result["message"] = "No steps are currently executable. Check dependencies."
        
        return result
        
    except Exception as e:
        return {
            "executable_steps": [],
            "error": str(e),
            "message": "Error getting executable steps"
        }


@tool("resolve_enhanced_step_inputs", args_schema=ResolveStepInputsInput)
def resolve_enhanced_step_inputs(
    state: State, 
    step_id: str, 
    validate_references: bool = True
) -> Dict[str, Any]:
    """
    Resolve all parameter references in a step's inputs using the enhanced resolver.
    
    Args:
        state: Current conversation state
        step_id: ID of the step to resolve inputs for
        validate_references: Whether to validate all references
        
    Returns:
        Dictionary containing resolved inputs and validation information
    """
    try:
        plan = state.get("plan")
        if not plan or not isinstance(plan, EnhancedPlan):
            return {
                "resolved_inputs": {},
                "error": "No enhanced plan available"
            }
        
        # Get the step
        step = plan.get_step(step_id)
        if not step:
            return {
                "resolved_inputs": {},
                "error": f"Step {step_id} not found in plan"
            }
        
        # Resolve inputs using the parameter resolver
        resolved_inputs = ParameterResolverFactory.resolve_step_inputs(plan, step_id)
        
        result = {
            "step_id": step_id,
            "resolved_inputs": resolved_inputs,
            "original_inputs": step.inputs,
            "resolution_successful": True
        }
        
        # Add validation information if requested
        if validate_references:
            validation_results = ParameterResolverFactory.validate_plan_references(plan)
            if step_id in validation_results:
                result["validation_issues"] = validation_results[step_id]
                result["has_validation_issues"] = True
            else:
                result["has_validation_issues"] = False
        
        return result
        
    except Exception as e:
        return {
            "resolved_inputs": {},
            "error": str(e),
            "resolution_successful": False
        }


@tool("update_enhanced_step_status", args_schema=UpdateStepStatusInput)
def update_enhanced_step_status(
    state: State,
    step_id: str,
    status: str,
    result: Optional[Dict[str, Any]] = None,
    error_message: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update the status of a step in the enhanced plan with better tracking.
    
    Args:
        state: Current conversation state
        step_id: ID of the step to update
        status: New status (pending, in_progress, completed, failed)
        result: Step execution result
        error_message: Error message if failed
        
    Returns:
        Dictionary containing update confirmation
    """
    try:
        plan = state.get("plan")
        if not plan or not isinstance(plan, EnhancedPlan):
            return {
                "success": False,
                "error": "No enhanced plan available"
            }
        
        # Get the step
        step = plan.get_step(step_id)
        if not step:
            return {
                "success": False,
                "error": f"Step {step_id} not found in plan"
            }
        
        # Parse status
        valid_statuses = ["pending", "in_progress", "completed", "failed", "skipped"]
        new_status = status.lower()
        if new_status not in valid_statuses:
            return {
                "success": False,
                "error": f"Invalid status: {status}. Must be one of: {valid_statuses}"
            }
        
        # Update step status
        old_status = step.status
        step.status = new_status
        
        # Handle status-specific updates
        if new_status == "in_progress":
            step.started_at = datetime.now()
            plan.execution_context.current_step_id = step_id
            plan.execution_context.add_log_entry("INFO", f"Started executing step {step_id}")

        elif new_status == "completed":
            step.completed_at = datetime.now()
            if result:
                step.result = result
                plan.execution_context.update_step_output(step_id, result)
            plan.execution_context.completed_steps += 1
            plan.execution_context.add_log_entry("INFO", f"Completed step {step_id}")

        elif new_status == "failed":
            step.completed_at = datetime.now()
            if error_message:
                step.add_error(error_message)
            plan.execution_context.failed_steps += 1
            plan.execution_context.add_log_entry("ERROR", f"Step {step_id} failed: {error_message or 'Unknown error'}")
        
        # Update plan modification timestamp
        plan.modified_at = datetime.now()
        
        return {
            "success": True,
            "step_id": step_id,
            "old_status": old_status,
            "new_status": new_status,
            "execution_duration": step.get_execution_duration(),
            "plan_progress": plan.execution_context.get_progress_percentage()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@tool("get_execution_summary", args_schema=GetExecutionSummaryInput)
def get_execution_summary(state: State, include_logs: bool = False) -> Dict[str, Any]:
    """
    Get a comprehensive summary of plan execution.
    
    Args:
        state: Current conversation state
        include_logs: Whether to include execution logs
        
    Returns:
        Dictionary containing execution summary
    """
    try:
        plan = state.get("plan")
        if not plan or not isinstance(plan, EnhancedPlan):
            return {
                "summary": "No enhanced plan available",
                "plan_type": "none"
            }
        
        summary = plan.get_execution_summary()
        
        # Add additional details
        summary["template_info"] = {
            "is_from_template": plan.is_from_template,
            "source_template": plan.source_template,
            "template_params": plan.template_params
        }
        
        summary["timing_info"] = {
            "created_at": plan.created_at.isoformat(),
            "modified_at": plan.modified_at.isoformat(),
            "execution_start": plan.execution_context.start_time.isoformat()
        }
        
        # Add step details
        summary["step_details"] = {}
        for step in plan.steps:
            summary["step_details"][step.step_id] = {
                "name": step.name,
                "status": step.status.value,
                "step_type": step.step_type.value,
                "retry_count": step.retry_count,
                "execution_duration": step.get_execution_duration(),
                "has_result": step.result is not None
            }
        
        # Add logs if requested
        if include_logs:
            summary["execution_logs"] = plan.execution_context.execution_log[-20:]  # Last 20 entries
        
        return summary
        
    except Exception as e:
        return {
            "summary": "Error generating execution summary",
            "error": str(e)
        }


# List of enhanced state management tools
enhanced_state_management_tools = [
    get_current_enhanced_plan,
    get_next_executable_steps,
    resolve_enhanced_step_inputs,
    update_enhanced_step_status,
    get_execution_summary
]
