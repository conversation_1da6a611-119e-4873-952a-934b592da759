# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_tools.py

This module provides tools for the template system that can be used by the Master Agent.
These tools allow the agent to discover, recommend, and instantiate templates.
"""

from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
import re
import uuid

from src.graph_v2.models import Plan, Step
from src.graph_v2.enhanced_models import EnhancedPlan, EnhancedStep, ExecutionContext, StepType
from src.graph_v2.template_models import (
    PlanTemplate,
    TemplateRecommendation,
    TemplateValidationResult,
    TemplateInstantiationResult
)
from src.graph_v2.types import State


# Global template registry (will be populated with built-in templates)
_template_registry: Dict[str, PlanTemplate] = {}


class RecommendTemplateInput(BaseModel):
    """Input schema for template recommendation."""
    user_input: str = Field(..., description="The user's original request or description")
    context: Optional[str] = Field(default=None, description="Additional context about the user's needs")


class GetTemplatesInput(BaseModel):
    """Input schema for getting available templates."""
    category: Optional[str] = Field(default=None, description="Filter by category (e.g., 'video_creation')")
    tags: Optional[List[str]] = Field(default=None, description="Filter by tags")
    difficulty: Optional[str] = Field(default=None, description="Filter by difficulty level")


class CreatePlanFromTemplateInput(BaseModel):
    """Input schema for creating a plan from a template."""
    template_id: str = Field(..., description="ID of the template to use")
    params: Dict[str, Any] = Field(..., description="Parameters for template instantiation")
    user_context: Optional[str] = Field(default=None, description="User's specific requirements or context")
    allow_customization: bool = Field(default=True, description="Whether to allow customization based on user context")


class ValidateTemplateParamsInput(BaseModel):
    """Input schema for validating template parameters."""
    template_id: str = Field(..., description="ID of the template")
    params: Dict[str, Any] = Field(..., description="Parameters to validate")


@tool("recommend_template", args_schema=RecommendTemplateInput)
def recommend_template(user_input: str, context: Optional[str] = None) -> Dict[str, Any]:
    """
    Analyze user input and recommend the most suitable template.
    
    This tool uses keyword matching and pattern recognition to suggest templates
    that best match the user's request.
    
    Args:
        user_input: The user's original request
        context: Additional context about the user's needs
        
    Returns:
        Dictionary containing recommendation details
    """
    try:
        # Simple keyword-based recommendation logic
        # In a production system, this could use ML models or more sophisticated matching
        
        user_text = (user_input + " " + (context or "")).lower()
        
        recommendations = []
        
        # Check for video creation keywords
        video_keywords = ["视频", "video", "鬼畜", "parody", "宣传", "promo", "广告", "ad", "mv", "短片"]
        if any(keyword in user_text for keyword in video_keywords):
            if any(word in user_text for word in ["鬼畜", "parody", "搞笑", "funny"]):
                recommendations.append({
                    "template_id": "ai_parody_video",
                    "confidence": 0.9,
                    "reason": "检测到鬼畜视频制作需求"
                })
            elif any(word in user_text for word in ["产品", "product", "宣传", "promo", "广告"]):
                recommendations.append({
                    "template_id": "product_promo_video", 
                    "confidence": 0.85,
                    "reason": "检测到产品宣传视频需求"
                })
        
        # Check for image series keywords
        image_keywords = ["海报", "poster", "系列", "series", "批量", "batch", "城市", "city"]
        if any(keyword in user_text for keyword in image_keywords):
            recommendations.append({
                "template_id": "city_poster_series",
                "confidence": 0.8,
                "reason": "检测到图片系列制作需求"
            })
        
        if not recommendations:
            return {
                "recommended_template": None,
                "confidence": 0.0,
                "reason": "未找到匹配的模板，建议使用自定义规划",
                "suggested_params": {},
                "alternatives": list(_template_registry.keys())
            }
        
        # Return the highest confidence recommendation
        best_rec = max(recommendations, key=lambda x: x["confidence"])
        
        # Try to extract suggested parameters
        suggested_params = _extract_suggested_params(user_text, best_rec["template_id"])
        
        return {
            "recommended_template": best_rec["template_id"],
            "confidence": best_rec["confidence"],
            "reason": best_rec["reason"],
            "suggested_params": suggested_params,
            "alternatives": [r["template_id"] for r in recommendations if r["template_id"] != best_rec["template_id"]]
        }
        
    except Exception as e:
        return {
            "recommended_template": None,
            "confidence": 0.0,
            "reason": f"推荐过程中出现错误: {str(e)}",
            "suggested_params": {},
            "alternatives": []
        }


@tool("get_available_templates", args_schema=GetTemplatesInput)
def get_available_templates(
    category: Optional[str] = None, 
    tags: Optional[List[str]] = None,
    difficulty: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get a list of available templates, optionally filtered by criteria.
    
    Args:
        category: Filter by template category
        tags: Filter by template tags
        difficulty: Filter by difficulty level
        
    Returns:
        List of template information dictionaries
    """
    try:
        templates = []
        
        for template_id, template in _template_registry.items():
            # Apply filters
            if category and template.category != category:
                continue
            if tags and not any(tag in template.tags for tag in tags):
                continue
            if difficulty and template.difficulty_level != difficulty:
                continue
            
            templates.append({
                "template_id": template.template_id,
                "name": template.name,
                "description": template.description,
                "category": template.category,
                "tags": template.tags,
                "difficulty_level": template.difficulty_level,
                "estimated_duration": template.estimated_duration,
                "usage_count": template.usage_count,
                "parameters": {
                    name: {
                        "type": schema.type.value,
                        "required": schema.required,
                        "default": schema.default,
                        "description": schema.description,
                        "options": schema.options
                    }
                    for name, schema in template.parameters.items()
                }
            })
        
        # Sort by usage count and success rate
        templates.sort(key=lambda x: x["usage_count"], reverse=True)
        
        return templates
        
    except Exception as e:
        return [{"error": f"获取模板列表时出现错误: {str(e)}"}]


@tool("create_plan_from_template", args_schema=CreatePlanFromTemplateInput)
def create_plan_from_template(
    template_id: str,
    params: Dict[str, Any],
    user_context: Optional[str] = None,
    allow_customization: bool = True
) -> Dict[str, Any]:
    """
    Create a concrete execution plan from a template.
    
    Args:
        template_id: ID of the template to use
        params: Parameters for template instantiation
        user_context: User's specific requirements
        allow_customization: Whether to allow customization based on context
        
    Returns:
        Dictionary containing the created plan or error information
    """
    try:
        # Get the template
        template = _template_registry.get(template_id)
        if not template:
            return {
                "success": False,
                "error": f"Template '{template_id}' not found",
                "plan": None
            }
        
        # Validate parameters
        try:
            validated_params = template.validate_parameters(params)
        except ValueError as e:
            return {
                "success": False,
                "error": f"Parameter validation failed: {str(e)}",
                "plan": None
            }
        
        # Create enhanced steps from template
        steps = []

        for step_template in template.step_templates:
            # Render description with parameters
            description = _render_template_string(step_template.description_template, validated_params)

            # Render input template with parameters
            rendered_inputs = _render_template_dict(step_template.input_template, validated_params)

            # Convert template dependencies to step IDs (use semantic IDs)
            dependencies = []
            for dep_template_id in step_template.dependencies:
                dependencies.append(dep_template_id)  # Keep semantic IDs

            # Map step type from template or infer from tool
            step_type = StepType.CONTENT_CREATION  # Default
            if "collect" in step_template.name.lower() or "search" in step_template.name.lower():
                step_type = StepType.DATA_COLLECTION
            elif "edit" in step_template.name.lower() or "modify" in step_template.name.lower():
                step_type = StepType.CONTENT_EDITING
            elif "review" in step_template.name.lower() or "check" in step_template.name.lower():
                step_type = StepType.QUALITY_REVIEW
            elif "synthesis" in step_template.name.lower() or "combine" in step_template.name.lower():
                step_type = StepType.INTEGRATION
            elif "final" in step_template.name.lower():
                step_type = StepType.FINALIZATION

            step = EnhancedStep(
                step_id=step_template.template_step_id,  # Use semantic ID
                name=step_template.name,
                description=description,
                tool_to_use=step_template.tool_to_use,
                inputs=rendered_inputs,
                dependencies=dependencies,
                step_type=step_type,
                parallel_group=step_template.parallel_group,
                estimated_duration=step_template.estimated_duration,
                timeout=step_template.timeout,
                template_step_id=step_template.template_step_id,
                template_params=validated_params
            )

            # Set retry configuration
            if step_template.max_retries:
                step.retry_config.max_retries = step_template.max_retries

            steps.append(step)

        # Create execution context
        execution_context = ExecutionContext(
            plan_id=f"{template_id}_{uuid.uuid4().hex[:8]}",
            template_id=template_id,
            template_params=validated_params,
            total_steps=len(steps)
        )

        # Create the enhanced plan
        plan = EnhancedPlan(
            plan_id=execution_context.plan_id,
            original_task=user_context or f"使用模板 {template.name}",
            steps=steps,
            execution_context=execution_context,
            source_template=template_id,
            template_params=validated_params,
            is_from_template=True
        )
        
        # Update template usage count
        template.usage_count += 1
        
        return {
            "success": True,
            "plan": plan,
            "error": None,
            "warnings": [],
            "metadata": {
                "template_id": template_id,
                "template_name": template.name,
                "parameters_used": validated_params,
                "total_steps": len(steps),
                "estimated_duration": template.estimated_duration
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"创建计划时出现错误: {str(e)}",
            "plan": None
        }


@tool("validate_template_params", args_schema=ValidateTemplateParamsInput)
def validate_template_params(template_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate parameters for a specific template.
    
    Args:
        template_id: ID of the template
        params: Parameters to validate
        
    Returns:
        Validation result with errors and suggestions
    """
    try:
        template = _template_registry.get(template_id)
        if not template:
            return {
                "valid": False,
                "errors": [f"Template '{template_id}' not found"],
                "warnings": [],
                "suggestions": {}
            }
        
        try:
            validated_params = template.validate_parameters(params)
            return {
                "valid": True,
                "errors": [],
                "warnings": [],
                "suggestions": validated_params
            }
        except ValueError as e:
            return {
                "valid": False,
                "errors": [str(e)],
                "warnings": [],
                "suggestions": {}
            }
            
    except Exception as e:
        return {
            "valid": False,
            "errors": [f"验证过程中出现错误: {str(e)}"],
            "warnings": [],
            "suggestions": {}
        }


def _extract_suggested_params(user_text: str, template_id: str) -> Dict[str, Any]:
    """Extract suggested parameter values from user input."""
    suggested = {}
    
    if template_id == "ai_parody_video":
        # Try to extract character name
        character_patterns = [
            r"(哪吒|孙悟空|猪八戒|唐僧|白娘子|嫦娥)",
            r"([A-Za-z]+)\s*(?:鬼畜|parody|视频)"
        ]
        for pattern in character_patterns:
            match = re.search(pattern, user_text)
            if match:
                suggested["character"] = match.group(1)
                break
    
    elif template_id == "product_promo_video":
        # Try to extract product name
        product_patterns = [
            r"(产品|product)[:：]\s*([^，。,.\s]+)",
            r"([^，。,.\s]+)\s*(?:产品|product|宣传)"
        ]
        for pattern in product_patterns:
            match = re.search(pattern, user_text)
            if match:
                suggested["product_name"] = match.group(2) if len(match.groups()) > 1 else match.group(1)
                break
    
    return suggested


def _render_template_string(template_str: str, params: Dict[str, Any]) -> str:
    """Render a template string with parameter substitution."""
    result = template_str
    for key, value in params.items():
        placeholder = f"{{{key}}}"
        result = result.replace(placeholder, str(value))
    return result


def _render_template_dict(template_dict: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
    """Render a template dictionary with parameter substitution."""
    result = {}
    for key, value in template_dict.items():
        if isinstance(value, str):
            result[key] = _render_template_string(value, params)
        elif isinstance(value, dict):
            result[key] = _render_template_dict(value, params)
        elif isinstance(value, list):
            result[key] = [_render_template_string(str(item), params) if isinstance(item, str) else item for item in value]
        else:
            result[key] = value
    return result


def register_template(template: PlanTemplate):
    """Register a template in the global registry."""
    _template_registry[template.template_id] = template


def get_template_registry() -> Dict[str, PlanTemplate]:
    """Get the current template registry."""
    return _template_registry.copy()


# List of all template tools for easy import
all_template_tools = [
    recommend_template,
    get_available_templates,
    create_plan_from_template,
    validate_template_params
]
