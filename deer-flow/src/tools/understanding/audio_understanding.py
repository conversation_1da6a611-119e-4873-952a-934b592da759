# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
音频理解工具 - 重构版

这个文件现在作为多模态理解工具的简化接口，专门用于音频分析。
实际功能由video_understanding.py中的多模态工具提供。
"""

import logging
from typing import Optional

from langchain_core.tools import StructuredTool
from pydantic.v1 import BaseModel, Field

# DeerFlow Imports
from src.config.configuration import Configuration

logger = logging.getLogger(__name__)

# === 简化的音频理解输入模型 ===

class SimpleAudioAnalysisInput(BaseModel):
    """音频理解工具输入参数"""

    audio_url: str = Field(
        ...,
        description="Audio file URL to analyze (supports mp3, wav, m4a, flac, ogg formats)"
    )

    question: str = Field(
        default="Please analyze this audio content including transcription and key insights",
        description="Specific analysis question. Be as specific as possible about what you want to analyze"
    )

# === 工具实现 ===

def get_audio_understanding_tool(config: Configuration) -> StructuredTool:
    """
    创建音频理解工具 - DeerFlow规范版本

    这是一个简化的接口，内部使用多模态理解工具来实现音频分析功能。
    专门针对音频分析场景进行了参数优化。

    Args:
        config: DeerFlow配置对象

    Returns:
        配置好的StructuredTool实例
    """
    # 导入多模态理解工具
    from .video_understanding import get_multimodal_understanding_tool

    # 获取多模态工具实例
    multimodal_tool = get_multimodal_understanding_tool(config)

    def audio_analysis_wrapper(**kwargs) -> str:
        """
        音频分析包装器函数

        将音频URL转换为多模态工具可以处理的格式
        """
        try:
            # 解析输入参数
            args = SimpleAudioAnalysisInput(**kwargs)

            # 调用多模态工具，将audio_url作为media_url传递
            result = multimodal_tool.func(
                media_url=args.audio_url,
                question=args.question
            )

            return result

        except Exception as e:
            logger.error(f"Audio analysis wrapper error: {e}", exc_info=True)
            return f"❌ Audio analysis failed: {str(e)}"

    async def audio_analysis_wrapper_async(**kwargs) -> str:
        """
        音频分析异步包装器函数
        """
        try:
            # 解析输入参数
            args = SimpleAudioAnalysisInput(**kwargs)

            # 调用多模态工具的异步版本
            result = await multimodal_tool.coroutine(
                media_url=args.audio_url,
                question=args.question
            )

            return result

        except Exception as e:
            logger.error(f"Audio analysis async wrapper error: {e}", exc_info=True)
            return f"❌ Audio analysis failed: {str(e)}"

    return StructuredTool.from_function(
        func=audio_analysis_wrapper,
        coroutine=audio_analysis_wrapper_async,
        name="audio_understanding",
        description=(
            "Audio content understanding tool for analyzing audio files. "
            "Provides speech transcription, content analysis, emotion detection, and scene recognition. "
            "Supports multiple audio formats (mp3, wav, m4a, flac, ogg). "
            "Uses advanced AI to understand audio content and provide detailed insights."
        ),
        args_schema=SimpleAudioAnalysisInput
    )