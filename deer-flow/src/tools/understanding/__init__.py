# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
理解工具模块

提供多模态内容理解能力，包括图片分析、视频分析、音频分析等功能。
所有工具都遵循DeerFlow的开发规范，提供Agent友好的接口。
"""

from .video_understanding import (
    get_multimodal_understanding_tool,
    get_video_understanding_tool
)
from .image_understanding import get_image_understanding_tool
from .audio_understanding import get_audio_understanding_tool

# 为了向后兼容，保留原有的导入（但标记为已弃用）
try:
    from .video_analyzer import ChatAPIVideoAnalyzerTool
    from .image_understanding import ChatAPIImageAnalyzerTool
    _legacy_tools_available = True
except ImportError:
    _legacy_tools_available = False

__all__ = [
    "get_multimodal_understanding_tool",
    "get_image_understanding_tool",
    "get_video_understanding_tool",
    "get_audio_understanding_tool"
]

# 如果需要向后兼容，可以添加legacy工具到__all__
if _legacy_tools_available:
    __all__.extend(["ChatAPIVideoAnalyzerTool", "ChatAPIImageAnalyzerTool"])