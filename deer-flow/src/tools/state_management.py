# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
state_management.py

This module provides tools that interact directly with the graph's state (`State`).
These are "internal" tools that allow the agent to read and modify its own
structured plan, following the FR-2.3 requirements.

Unlike other tools, they don't call external APIs. Instead, they receive the
current state, perform a mutation on the `plan` object, and return a dictionary
with the updated `plan`. LangGraph then merges this back into the main state.
"""
from typing import Dict, Any, List, Tuple, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
from datetime import datetime

from src.graph_v2.types import State
from src.graph_v2.enhanced_models import EnhancedPlan
from src.graph_v2.plan_converter import ensure_enhanced_plan


@tool
def get_current_plan(state: State) -> str:
    """
    Retrieves the current plan from the state and returns it in a summary format.
    The agent can use this to review the overall plan and see the status of each step.
    Only supports EnhancedPlan models (legacy plans are automatically converted).
    """
    plan = state.get("plan")
    if not plan:
        return "No plan is currently available. You should probably create one first using the planner_tool."

    try:
        # Ensure we have an EnhancedPlan
        enhanced_plan = ensure_enhanced_plan(plan)

        # Provide detailed summary
        summary = {
            "plan_id": enhanced_plan.plan_id,
            "original_task": enhanced_plan.original_task,
            "total_steps": len(enhanced_plan.steps),
            "is_from_template": enhanced_plan.is_from_template,
            "source_template": enhanced_plan.source_template,
            "steps": [
                {
                    "step_id": step.step_id,
                    "name": step.name,
                    "description": step.description,
                    "tool_to_use": step.tool_to_use,
                    "status": step.status,
                    "step_type": step.step_type.value,
                    "dependencies": step.dependencies,
                    "parallel_group": step.parallel_group,
                    "user_friendly_status": step.get_user_friendly_status()
                }
                for step in enhanced_plan.steps
            ],
            "execution_summary": enhanced_plan.get_execution_summary(),
            "user_friendly_progress": enhanced_plan.execution_context.get_user_friendly_progress(),
            "current_activity": enhanced_plan.execution_context.get_current_activity(),
            "execution_summary_text": enhanced_plan.execution_context.get_execution_summary_text()
        }
        return json.dumps(summary, indent=2, ensure_ascii=False)

    except Exception as e:
        return f"Error retrieving plan: {str(e)}"


@tool
def get_next_pending_step(state: State) -> str:
    """
    Finds the next step in the plan that is ready to be executed.
    A step is ready if its status is 'pending' and all its dependencies are 'completed'.
    Returns the step as a JSON string or a message if no steps are ready.
    Only supports EnhancedPlan models (legacy plans are automatically converted).
    """
    plan = state.get("plan")
    if not plan:
        return "No plan available. Cannot get a next step."

    try:
        # Ensure we have an EnhancedPlan
        enhanced_plan = ensure_enhanced_plan(plan)

        # Use the enhanced method
        executable_steps = enhanced_plan.get_next_executable_steps()

        if not executable_steps:
            if enhanced_plan.is_complete():
                return "Plan execution is complete. All steps have been finished."
            elif enhanced_plan.has_failed():
                failed_steps = enhanced_plan.get_failed_steps()
                retryable_steps = enhanced_plan.get_retryable_steps()
                return f"Plan has failed steps. {len(failed_steps)} failed, {len(retryable_steps)} can be retried."
            else:
                return "No steps are currently ready to execute. Check dependencies or step statuses."

        # Return the first executable step with enhanced information
        next_step = executable_steps[0]
        step_info = {
            "step_id": next_step.step_id,
            "name": next_step.name,
            "description": next_step.description,
            "tool_to_use": next_step.tool_to_use,
            "inputs": next_step.inputs,
            "status": next_step.status,
            "user_friendly_status": next_step.get_user_friendly_status(),
            "step_type": next_step.step_type.value,
            "dependencies": next_step.dependencies,
            "parallel_group": next_step.parallel_group,
            "estimated_duration": next_step.estimated_duration,
            "retry_count": next_step.retry_count,
            "max_retries": next_step.retry_config.max_retries
        }
        return json.dumps(step_info, indent=2, ensure_ascii=False)

    except Exception as e:
        return f"Error getting next step: {str(e)}"


class UpdateStepInput(BaseModel):
    step_id: str = Field(description="The unique ID of the step to update (string).")
    status: str = Field(description="The new status for the step.")
    result: Dict[str, Any] = Field(default_factory=dict, description="The output or result of the step's execution. This can be a success message, a file path, or an error description.")

@tool
def update_step_status(state: State, step_id: str, status: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Updates the status and result of a specific step in the plan.
    This is a critical tool for the agent to mark its progress after executing a step.
    It returns the updated plan object to be merged back into the state.
    Only supports EnhancedPlan models (legacy plans are automatically converted).
    """
    plan = state.get("plan")
    if not plan:
        return {"plan": None}

    try:
        # Ensure we have an EnhancedPlan
        enhanced_plan = ensure_enhanced_plan(plan)

        # Find and update the step
        step = enhanced_plan.get_step(step_id)
        if not step:
            return {"plan": enhanced_plan, "error": f"Step {step_id} not found"}

        # Update step status and result
        old_status = step.status
        step.status = status

        if result:
            step.result = result
            # Update execution context
            enhanced_plan.execution_context.update_step_output(step.step_id, result)

        # Update execution context counters
        if old_status != "completed" and status == "completed":
            enhanced_plan.execution_context.completed_steps += 1
        elif old_status != "failed" and status == "failed":
            enhanced_plan.execution_context.failed_steps += 1
        elif old_status == "completed" and status != "completed":
            enhanced_plan.execution_context.completed_steps = max(0, enhanced_plan.execution_context.completed_steps - 1)
        elif old_status == "failed" and status != "failed":
            enhanced_plan.execution_context.failed_steps = max(0, enhanced_plan.execution_context.failed_steps - 1)

        # Update timestamps
        if status == "in_progress" and not step.started_at:
            step.started_at = datetime.now()
        elif status in ["completed", "failed", "skipped"] and not step.completed_at:
            step.completed_at = datetime.now()

        return {"plan": enhanced_plan}

    except Exception as e:
        return {"plan": plan, "error": f"Error updating step status: {str(e)}"}


@tool
def check_execution_status(state: State) -> str:
    """
    检查当前执行状态，确定是否需要继续执行。
    这是Master Agent的执行完整性检查工具。

    Returns:
        执行状态报告，包括下一步行动建议
    """
    plan = state.get("plan")
    if not plan:
        return "❌ 无执行计划。建议：创建新计划或使用模板。"

    if isinstance(plan, EnhancedPlan):
        # 使用增强计划的状态检查
        if plan.is_complete():
            completed_steps = len([s for s in plan.steps if s.status == "completed"])
            return f"✅ 计划执行完成！所有 {completed_steps} 个步骤都已完成。建议：向用户展示最终结果。"

        if plan.has_failed():
            failed_steps = plan.get_failed_steps()
            retryable_steps = plan.get_retryable_steps()

            if retryable_steps:
                return f"⚠️ 有 {len(failed_steps)} 个步骤失败，其中 {len(retryable_steps)} 个可以重试。建议：继续执行或重试失败步骤。"
            else:
                return f"❌ 有 {len(failed_steps)} 个步骤失败且无法重试。建议：重新规划或修改计划。"

        # 检查下一个可执行步骤
        executable_steps = plan.get_next_executable_steps()
        if executable_steps:
            next_step = executable_steps[0]
            pending_count = len([s for s in plan.steps if s.status == "pending"])
            completed_count = len([s for s in plan.steps if s.status == "completed"])

            return f"🔄 执行进行中：已完成 {completed_count}/{len(plan.steps)} 步骤。下一步：{next_step.step_id} - {next_step.name}。建议：立即继续执行。"
        else:
            # 没有可执行步骤，检查是否有依赖问题
            pending_steps = [s for s in plan.steps if s.status == "pending"]
            if pending_steps:
                return f"⏸️ 有 {len(pending_steps)} 个步骤等待中，但无法执行（可能有依赖问题）。建议：检查依赖关系或重新规划。"
            else:
                return "🤔 计划状态异常：没有待执行步骤但也未完成。建议：检查计划状态。"
    else:
        # 处理传统Plan模型
        completed_steps = len([s for s in plan.steps if s.status == "completed"])
        total_steps = len(plan.steps)

        if completed_steps == total_steps:
            return f"✅ 计划执行完成！所有 {total_steps} 个步骤都已完成。建议：向用户展示最终结果。"

        pending_steps = [s for s in plan.steps if s.status == "pending"]
        if pending_steps:
            return f"🔄 执行进行中：已完成 {completed_steps}/{total_steps} 步骤。建议：继续执行下一个步骤。"
        else:
            return f"⏸️ 计划状态异常：{completed_steps}/{total_steps} 步骤完成，但没有待执行步骤。建议：检查计划状态。"


@tool
def force_continue_execution(state: State) -> str:
    """
    强制检查并提醒继续执行。
    当Master Agent可能要停止时，使用此工具确保执行继续。

    Returns:
        强制继续执行的指令
    """
    status_check = check_execution_status.invoke({"state": state})

    if "✅ 计划执行完成" in status_check:
        return "计划已完成，可以停止执行。"
    elif "🔄 执行进行中" in status_check or "⚠️" in status_check:
        return f"⚠️ 执行尚未完成！{status_check}\n\n🚨 强制指令：立即调用 get_next_pending_step 继续执行，不要停止！"
    else:
        return f"需要处理执行问题：{status_check}"


@tool
def resolve_step_inputs(state: State, step_id: str) -> Dict[str, Any]:
    """
    Resolve step inputs by processing references to previous steps' outputs.

    This tool processes the inputs for a given step and resolves any references
    in the format {{step_X.field.path}} to actual values from completed steps.
    Only supports EnhancedPlan models (legacy plans are automatically converted).

    Args:
        state: The current graph state
        step_id: The ID of the step whose inputs should be resolved (string)

    Returns:
        Dict containing:
        - resolved_inputs: Dictionary of resolved input values
        - resolution_log: List of resolution operations performed
        - unresolved_references: List of references that couldn't be resolved
        - success: Boolean indicating if all references were resolved
    """
    plan = state.get("plan")
    if not plan:
        return {
            "resolved_inputs": {},
            "resolution_log": [],
            "unresolved_references": [],
            "success": False,
            "error": "No plan found in state"
        }

    try:
        # Ensure we have an EnhancedPlan
        enhanced_plan = ensure_enhanced_plan(plan)

        # Use the enhanced parameter resolver
        from src.graph_v2.parameter_resolver import ParameterResolverFactory

        resolved_inputs = ParameterResolverFactory.resolve_step_inputs(enhanced_plan, step_id)
        validation_results = ParameterResolverFactory.validate_plan_references(enhanced_plan)

        step_issues = validation_results.get(step_id, [])

        return {
            "resolved_inputs": resolved_inputs,
            "resolution_log": [f"Used enhanced parameter resolver for step {step_id}"],
            "unresolved_references": step_issues,
            "success": len(step_issues) == 0
        }

    except Exception as e:
        return {
            "resolved_inputs": {},
            "resolution_log": [],
            "unresolved_references": [str(e)],
            "success": False,
            "error": f"Parameter resolution failed: {str(e)}"
        }


@tool
def get_step_context(state: State, step_id: str) -> Dict[str, Any]:
    """
    Get comprehensive context information for a specific step.

    This tool provides complete context for a step, including its basic information,
    resolved inputs, available outputs from previous steps, and dependency analysis.

    Args:
        state: The current graph state
        step_id: The ID of the step to get context for

    Returns:
        Dict containing comprehensive step context information
    """
    plan = state.get("plan")
    if not plan:
        return {"error": "No plan found in state"}

    try:
        # Ensure we have an EnhancedPlan
        enhanced_plan = ensure_enhanced_plan(plan)

        # Find the target step
        target_step = enhanced_plan.get_step(step_id)
        if not target_step:
            return {"error": f"Step {step_id} not found in plan"}

    except Exception as e:
        return {"error": f"Error processing plan: {str(e)}"}

    # Get resolved inputs
    resolution_result = resolve_step_inputs.invoke({"state": state, "step_id": step_id})

    # Get available outputs from completed steps
    available_outputs = {}
    for step in enhanced_plan.steps:
        if step.status == "completed" and step.result:
            available_outputs[step.step_id] = step.result

    # Analyze dependencies (use the step's dependencies directly)
    dependencies = target_step.dependencies

    return {
        "step_info": {
            "step_id": target_step.step_id,
            "description": target_step.description,
            "tool_to_use": target_step.tool_to_use,
            "status": target_step.status,
            "inputs": target_step.inputs
        },
        "resolved_inputs": resolution_result.get("resolved_inputs", {}),
        "resolution_log": resolution_result.get("resolution_log", []),
        "unresolved_references": resolution_result.get("unresolved_references", []),
        "available_outputs": available_outputs,
        "dependencies": list(set(dependencies)),
        "is_ready": len(resolution_result.get("unresolved_references", [])) == 0
    }


# Legacy function removed - using enhanced parameter resolver instead
def _resolve_reference_legacy(reference: str, plan) -> Tuple[Any, Dict[str, Any]]:
    """
    Resolve a single reference to a previous step's output.

    Args:
        reference: Reference string like "{{step_1.assets.images.0}}"
        plan: The current plan containing all steps

    Returns:
        Tuple of (resolved_value, log_entry)
    """
    # Initialize log entry
    log_entry = {
        "original": reference,
        "resolved": None,
        "source_step": None,
        "path": [],
        "error": None
    }

    try:
        # Remove {{ }} and split into parts
        ref_content = reference[2:-2]
        parts = ref_content.split(".")
        log_entry["path"] = parts

        # First part must be step_X format
        if not parts[0].startswith("step_"):
            log_entry["error"] = f"Invalid reference format: {reference}"
            return None, log_entry

        # Extract step ID
        try:
            step_id = int(parts[0].split("_")[1])
            log_entry["source_step"] = step_id
        except (ValueError, IndexError):
            log_entry["error"] = f"Invalid step ID in reference: {reference}"
            return None, log_entry

        # Find the source step
        source_step = None
        for step in plan.steps:
            if step.step_id == step_id:
                source_step = step
                break

        if not source_step:
            log_entry["error"] = f"Source step {step_id} not found"
            return None, log_entry

        if not source_step.result:
            log_entry["error"] = f"Source step {step_id} has no result"
            return None, log_entry

        # Navigate through the nested path
        current_value = source_step.result
        for i, part in enumerate(parts[1:], 1):
            if isinstance(current_value, dict):
                if part in current_value:
                    current_value = current_value[part]
                else:
                    log_entry["error"] = f"Key '{part}' not found in step {step_id} result at path {'.'.join(parts[:i+1])}"
                    return None, log_entry
            elif isinstance(current_value, list):
                try:
                    index = int(part)
                    if 0 <= index < len(current_value):
                        current_value = current_value[index]
                    else:
                        log_entry["error"] = f"Index {index} out of range for list in step {step_id} (length: {len(current_value)})"
                        return None, log_entry
                except ValueError:
                    log_entry["error"] = f"Invalid list index '{part}' for step {step_id}"
                    return None, log_entry
            else:
                log_entry["error"] = f"Cannot access '{part}' on {type(current_value).__name__} in step {step_id}"
                return None, log_entry

        log_entry["resolved"] = current_value
        return current_value, log_entry

    except Exception as e:
        log_entry["error"] = f"Unexpected error resolving {reference}: {str(e)}"
        return None, log_entry


# A list of all state management tools for easy import
state_management_tools = [
    get_current_plan,
    get_next_pending_step,
    update_step_status,
    resolve_step_inputs,
    get_step_context,
    check_execution_status,
    force_continue_execution
]