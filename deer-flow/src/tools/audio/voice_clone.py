"""
MiniMax Voice Cloning Tool

快速复刻音色工具，支持基于音频文件快速克隆人声音色
"""

import os
import requests
import logging
import tempfile
import uuid
import json
from typing import Type, List, Optional, Dict, Any
from functools import partial
import base64
import mimetypes
from pathlib import Path
import io
import asyncio
import re

from langchain_core.tools import StructuredTool
from pydantic.v1 import BaseModel, Field, validator, ValidationError

from src.config.configuration import Configuration

logger = logging.getLogger(__name__)

# 可选的COS上传功能
try:
    from src.utils.cos_uploader import get_cos_client, upload_to_cos
    COS_AVAILABLE = True
except ImportError:
    COS_AVAILABLE = False
    logger.warning("COS uploader not available. Demo audio will not be uploaded to COS.")

# --- Input Schema ---

class VoiceCloneInput(BaseModel):
    """音色克隆工具输入参数"""
    
    audio_file_path: str = Field(
        description=(
            "**音频文件URL或路径** - 需要克隆音色的参考音频文件URL地址或本地路径。\n"
            "**要求**：\n"
            "- 支持HTTP/HTTPS URL或本地文件路径\n"
            "- 文件格式：mp3、m4a、wav\n"
            "- 时长：10秒-5分钟\n"
            "- 文件大小：≤20MB\n"
            "- 建议使用清晰、无杂音的单人语音录音"
        )
    )
    
    voice_id: Optional[str] = Field(
        None,
        description=(
            "**自定义音色ID** - 为克隆的音色指定一个唯一标识符。\n"
            "**规则**：\n"
            "- 长度：8-256字符\n"
            "- 首字符必须是英文字母\n"
            "- 允许：数字、字母、连字符(-)、下划线(_)\n"
            "- 末位不能是连字符或下划线\n"
            "- 示例：Voice001、MyVoice_Clone、CustomVoice-2024\n"
            "如不提供，系统将自动生成"
        )
    )
    
    test_text: Optional[str] = Field(
        None,
        description=(
            "**试听文本** - 用于测试克隆音色效果的文本内容。\n"
            "- 限制：2000字符以内\n"
            "- 建议：选择有代表性的句子来评估音色效果\n"
            "- 如提供此参数，将返回试听音频链接"
        )
    )
    
    model: str = Field(
        "speech-02-hd",
        description="**试听模型** - 固定使用高质量模型 speech-02-hd"
    )
    
    prompt_audio_path: Optional[str] = Field(
        None,
        description=(
            "**提示音频URL或路径** - 用于增强音色相似度的示例音频（<8秒）。\n"
            "- 支持HTTP/HTTPS URL或本地文件路径\n"
            "- 格式：mp3、m4a、wav\n"
            "- 时长：建议<8秒\n"
            "- 作用：提供更准确的音色参考，提升克隆效果"
        )
    )
    
    prompt_text: Optional[str] = Field(
        None,
        description=(
            "**提示文本** - 与prompt_audio_path对应的文本内容。\n"
            "- 当提供prompt_audio_path时必须提供此参数\n"
            "- 应与提示音频的内容完全一致"
        )
    )
    
    enable_noise_reduction: bool = Field(
        True,
        description="**降噪处理** - 是否对输入音频进行降噪处理，建议开启"
    )
    
    enable_volume_normalization: bool = Field(
        True,
        description="**音量归一化** - 是否对输入音频进行音量归一化，建议开启"
    )

    @validator('voice_id')
    def validate_voice_id(cls, v):
        if v is None:
            return v
        
        # 长度检查
        if not (8 <= len(v) <= 256):
            raise ValueError("voice_id长度必须在8-256字符之间")
        
        # 首字符必须是字母
        if not v[0].isalpha():
            raise ValueError("voice_id首字符必须是英文字母")
        
        # 末位字符不能是-或_
        if v[-1] in ['-', '_']:
            raise ValueError("voice_id末位字符不能是连字符或下划线")
        
        # 只允许字母、数字、-、_
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_-]*[a-zA-Z0-9]$', v):
            raise ValueError("voice_id只能包含字母、数字、连字符和下划线")
        
        return v

    @validator('test_text')
    def validate_test_text(cls, v):
        if v is not None and len(v) > 2000:
            raise ValueError("试听文本不能超过2000字符")
        return v

    @validator('prompt_text')
    def validate_prompt_text_with_audio(cls, v, values):
        prompt_audio = values.get('prompt_audio_path')
        if prompt_audio and not v:
            raise ValueError("提供prompt_audio_path时必须提供对应的prompt_text")
        return v


# --- Core Execution Functions ---

def _download_audio_from_url(url: str) -> bytes:
    """从URL下载音频文件"""
    try:
        logger.info(f"开始下载音频: {url}")
        response = requests.get(url, timeout=60)
        response.raise_for_status()

        # 检查文件大小
        content_length = response.headers.get('content-length')
        if content_length:
            file_size = int(content_length)
            if file_size > 20 * 1024 * 1024:  # 20MB
                raise ValueError(f"文件太大: {file_size / 1024 / 1024:.1f}MB，最大支持20MB")

        audio_data = response.content
        logger.info(f"音频下载成功，大小: {len(audio_data) / 1024 / 1024:.1f}MB")
        return audio_data

    except requests.exceptions.RequestException as e:
        raise Exception(f"音频下载失败: {str(e)}")


def _upload_audio_to_minimax(config: Configuration, audio_data: bytes, filename: str, purpose: str) -> str:
    """上传音频数据到MiniMax平台"""

    # 检查文件格式（从文件名推断）
    file_ext = Path(filename).suffix.lower()
    if file_ext not in ['.mp3', '.m4a', '.wav']:
        raise ValueError(f"不支持的文件格式: {file_ext}，仅支持 mp3、m4a、wav")

    # 检查文件大小
    if len(audio_data) > 20 * 1024 * 1024:  # 20MB
        raise ValueError(f"文件太大: {len(audio_data) / 1024 / 1024:.1f}MB，最大支持20MB")

    upload_url = f"https://api.minimaxi.com/v1/files/upload?GroupId={config.minimax_group_id}"

    headers = {
        'Authorization': f'Bearer {config.minimax_api_key}',
        'authority': 'api.minimaxi.com'
    }

    data = {'purpose': purpose}

    # 使用BytesIO来模拟文件对象
    files = {'file': (filename, io.BytesIO(audio_data), mimetypes.guess_type(filename)[0])}

    response = requests.post(upload_url, headers=headers, data=data, files=files)

    if response.status_code != 200:
        raise Exception(f"文件上传失败: HTTP {response.status_code}, {response.text}")

    result = response.json()
    if result.get('base_resp', {}).get('status_code') != 0:
        raise Exception(f"文件上传失败: {result.get('base_resp', {}).get('status_msg', 'Unknown error')}")

    file_id = result.get('file', {}).get('file_id')
    if not file_id:
        raise Exception("文件上传成功但未返回file_id")

    logger.info(f"音频上传成功: {filename} -> file_id: {file_id}")
    return file_id


def _upload_file_to_minimax(config: Configuration, file_path_or_url: str, purpose: str) -> str:
    """上传文件到MiniMax平台（支持URL和本地文件）"""

    # 判断是URL还是本地文件路径
    if file_path_or_url.startswith(('http://', 'https://')):
        # 处理URL
        audio_data = _download_audio_from_url(file_path_or_url)
        # 从URL推断文件名
        filename = Path(file_path_or_url.split('?')[0]).name  # 去掉查询参数
        if not filename or '.' not in filename:
            filename = f"audio_{uuid.uuid4().hex[:8]}.mp3"  # 默认文件名

        return _upload_audio_to_minimax(config, audio_data, filename, purpose)
    else:
        # 处理本地文件
        if not os.path.exists(file_path_or_url):
            raise FileNotFoundError(f"音频文件不存在: {file_path_or_url}")

        with open(file_path_or_url, 'rb') as f:
            audio_data = f.read()

        filename = Path(file_path_or_url).name
        return _upload_audio_to_minimax(config, audio_data, filename, purpose)


def _clone_voice(config: Configuration, **kwargs) -> Dict[str, Any]:
    """执行音色克隆"""
    
    try:
        # 参数提取
        audio_file_path = kwargs.get('audio_file_path')
        voice_id = kwargs.get('voice_id')
        test_text = kwargs.get('test_text')
        model = kwargs.get('model', 'speech-02-hd')
        prompt_audio_path = kwargs.get('prompt_audio_path')
        prompt_text = kwargs.get('prompt_text')
        enable_noise_reduction = kwargs.get('enable_noise_reduction', True)
        enable_volume_normalization = kwargs.get('enable_volume_normalization', True)
        
        # 生成默认voice_id
        if not voice_id:
            voice_id = f"VoiceClone_{uuid.uuid4().hex[:8]}"
            logger.info(f"自动生成voice_id: {voice_id}")
        
        # 1. 上传主要音频文件
        logger.info(f"开始上传音频文件: {audio_file_path}")
        main_file_id = _upload_file_to_minimax(config, audio_file_path, "voice_clone")
        
        # 2. 上传提示音频文件（如果提供）
        prompt_file_id = None
        if prompt_audio_path:
            logger.info(f"开始上传提示音频: {prompt_audio_path}")
            prompt_file_id = _upload_file_to_minimax(config, prompt_audio_path, "prompt_audio")
        
        # 3. 构建克隆请求
        clone_url = f"https://api.minimaxi.com/v1/voice_clone?GroupId={config.minimax_group_id}"
        
        headers = {
            'Authorization': f'Bearer {config.minimax_api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "file_id": main_file_id,
            "voice_id": voice_id,
            "need_noise_reduction": enable_noise_reduction,
            "need_volume_normalization": enable_volume_normalization
        }
        
        # 添加提示音频信息
        if prompt_file_id and prompt_text:
            payload["clone_prompt"] = {
                "file_id": prompt_file_id,
                "text": prompt_text
            }
        
        # 添加试听参数
        if test_text:
            payload["text"] = test_text
            payload["model"] = model
        
        # 4. 执行音色克隆
        logger.info(f"开始音色克隆: voice_id={voice_id}")
        response = requests.post(clone_url, headers=headers, data=json.dumps(payload))
        
        if response.status_code != 200:
            raise Exception(f"音色克隆失败: HTTP {response.status_code}, {response.text}")
        
        result = response.json()
        
        # 检查响应状态
        base_resp = result.get('base_resp', {})
        if base_resp.get('status_code') != 0:
            raise Exception(f"音色克隆失败: {base_resp.get('status_msg', 'Unknown error')}")
        
        # 5. 处理返回结果
        response_data = {
            "success": True,
            "voice_id": voice_id,
            "main_file_id": main_file_id,
            "input_sensitive": result.get('input_sensitive', False),
            "message": f"✅ 音色克隆成功！新音色ID: {voice_id}"
        }
        
        # 处理试听音频
        demo_audio_url = result.get('demo_audio')
        if demo_audio_url and test_text:
            try:
                # 下载试听音频
                logger.info("开始下载试听音频")
                audio_response = requests.get(demo_audio_url)
                audio_response.raise_for_status()
                
                # 生成临时文件名
                temp_filename = f"voice_clone_demo_{voice_id}_{uuid.uuid4().hex[:8]}.mp3"
                
                # 上传到COS（如果可用）
                if COS_AVAILABLE:
                    cos_client = get_cos_client(config)
                    cos_url = upload_to_cos(
                        cos_client=cos_client,
                        file_content=audio_response.content,
                        filename=temp_filename,
                        bucket=config.cos_bucket,
                        region=config.cos_region
                    )

                    response_data["demo_audio_url"] = cos_url
                    response_data["demo_audio_text"] = test_text
                    response_data["message"] += f"\n🎵 试听音频已生成: {cos_url}"

                    logger.info(f"试听音频上传成功: {cos_url}")
                else:
                    # COS不可用时，返回原始URL
                    response_data["demo_audio_url"] = demo_audio_url
                    response_data["demo_audio_text"] = test_text
                    response_data["message"] += f"\n🎵 试听音频已生成: {demo_audio_url}"

                    logger.info(f"试听音频生成成功（COS不可用）: {demo_audio_url}")
                
            except Exception as e:
                logger.warning(f"试听音频处理失败: {str(e)}")
                response_data["demo_audio_error"] = str(e)
        
        # 6. 添加使用说明
        response_data["usage_info"] = {
            "voice_id": voice_id,
            "valid_period": "168小时（7天）",
            "usage_note": "请在7天内使用此音色进行语音合成，否则将被删除",
            "compatible_apis": ["同步语音合成", "异步长文本语音合成"]
        }
        
        return response_data
        
    except Exception as e:
        error_msg = f"❌ 音色克隆失败: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "voice_id": kwargs.get('voice_id'),
        }


async def _aclone_voice(config: Configuration, **kwargs) -> Dict[str, Any]:
    """音色克隆的异步版本"""
    return await asyncio.to_thread(_clone_voice, config, **kwargs)


# --- Tool Factory ---

def get_voice_clone_tool(config: Configuration) -> Optional[StructuredTool]:
    """创建音色克隆工具"""
    
    if not config.minimax_api_key:
        logger.warning("Voice Clone tool disabled: MINIMAX_API_KEY not in config.")
        return None
    
    if not config.minimax_group_id:
        logger.warning("Voice Clone tool disabled: MINIMAX_GROUP_ID not in config.")
        return None
    
    # 同步包装器
    def _sync_execute_voice_clone(**kwargs):
        try:
            return _clone_voice(config, **kwargs)
        except Exception as e:
            return f"❌ 音色克隆同步执行错误: {str(e)}"
    
    return StructuredTool.from_function(
        func=_sync_execute_voice_clone,
        coroutine=partial(_aclone_voice, config),
        name="clone_voice_from_audio",
        description=(
            "**🎭 音色克隆工具**\n"
            "基于音频URL或文件快速克隆人声音色，为AI创作提供个性化语音能力。\n\n"
            "**核心功能：**\n"
            "- 🌐 支持音频URL：直接使用网络音频资源\n"
            "- 🎯 快速克隆：10秒-5分钟音频即可生成音色\n"
            "- 🎵 自动优化：内置降噪和音量归一化\n"
            "- 🔧 增强模式：可选提示音频提升相似度\n"
            "- 📝 即时试听：自动生成试听音频验证效果\n\n"
            "**使用说明：**\n"
            "1. 提供音频URL（推荐）或本地文件路径\n"
            "2. 指定唯一的voice_id（8-256字符，字母开头）\n"
            "3. 可选：提供test_text生成试听音频\n"
            "4. 返回的voice_id可用于后续语音合成\n\n"
            "**音频要求：** mp3/m4a/wav格式，10秒-5分钟，≤20MB"
        ),
        args_schema=VoiceCloneInput,
    )


# --- Independent Testing Support ---

if __name__ == "__main__":
    import sys
    from pathlib import Path
    
    # Add project root to path
    project_root = Path(__file__).parents[3]
    sys.path.insert(0, str(project_root))
    
    from src.config.configuration import Configuration
    
    def test_voice_clone():
        """独立测试音色克隆工具"""
        
        # 创建测试配置
        config = Configuration()
        
        # 检查必要配置
        if not config.minimax_api_key:
            print("❌ 缺少 MINIMAX_API_KEY 配置")
            return
        
        if not config.minimax_group_id:
            print("❌ 缺少 MINIMAX_GROUP_ID 配置")
            return
        
        # 创建工具
        tool = get_voice_clone_tool(config)
        if not tool:
            print("❌ 工具创建失败")
            return
        
        # 测试参数（请根据实际情况修改）
        test_params = {
            "audio_file_path": "test_audio.mp3",  # 请提供实际的测试音频文件
            "voice_id": "TestVoice123",
            "test_text": "这是一个音色克隆测试，请听听效果如何。",
            "model": "speech-02-hd",
            "enable_noise_reduction": True,
            "enable_volume_normalization": True
        }
        
        print("🧪 开始测试音色克隆工具...")
        print(f"📋 测试参数: {test_params}")
        
        try:
            # 同步调用测试
            result = tool.func(**test_params)
            print("✅ 同步调用成功")
            print(f"📊 结果: {result}")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    test_voice_clone()