# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import json
import logging
import requests
import soundfile as sf
from typing import Optional, List, Dict, Any
from pydantic.v1 import BaseModel, Field
from langchain_core.tools import StructuredTool
import asyncio
import aiohttp
from functools import partial
import tempfile

# DeerFlow Imports
from src.config.configuration import (
    Configuration,
    MINIMAX_SYSTEM_VOICES,
    MINIMAX_VOICE_PAIRS
)
from src.utils.cos_uploader import get_cos_client, upload_to_cos

logger = logging.getLogger(__name__)

class DialogueItem(BaseModel):
    """单句对话项 - 定义一个说话人的一句话及其语音参数"""

    speaker: str = Field(
        description="说话人名称，如'主持人'、'嘉宾'、'旁白'等，用于区分不同角色",
    )

    text: str = Field(  
        description="要转换为语音的文本内容，支持中英文，最大10000字符",
        max_length=10000
    )

    emotion: Optional[str] = Field(
        default="calm",
        description="Voice emotion. Options: calm, happy, sad, angry, fearful, disgusted, surprised. Default is calm (neutral)."
    )

    speed: Optional[float] = Field(
        default=1.0,
        description="Speech speed multiplier, range 0.5-2.0. 1.0 is normal speed.",
        ge=0.5,
        le=2.0
    )

    vol: Optional[float] = Field(
        default=1.0,
        description="Volume level, range 0.1-10.0. 1.0 is normal volume.",
        gt=0.0,
        le=10.0
    )

    pitch: Optional[int] = Field(
        default=0,
        description="Pitch adjustment, range -12 to 12. 0 is original pitch.",
        ge=-12,
        le=12
    )

class MultiSpeakerTTSInput(BaseModel):
    """多人TTS工具输入参数 - 定义完整的对话音频生成任务"""

    dialogue_script: List[DialogueItem] = Field(
        ...,
        description=(
            "对话脚本列表，按顺序包含所有要生成的语音。每个对话项指定说话人和文本内容。\n"
            "简单示例：[{'speaker': '主持人', 'text': '欢迎大家收听节目'}, {'speaker': '嘉宾', 'text': '很高兴来到这里'}]\n"
            "完整示例：[{'speaker': '主持人', 'text': '今天我们聊AI', 'emotion': 'happy', 'speed': 1.2}, "
            "{'speaker': '专家', 'text': '这是个很有趣的话题', 'emotion': 'calm', 'vol': 1.5}]"
        )
    )

    voice_mapping: Optional[Dict[str, str]] = Field(
        default=None,
        description=(
            "可选：为每个说话人指定具体的声音ID。如果不提供，系统会自动分配合适的男女声。\n\n"
            "🎭 推荐声音ID：\n"
            "• 专业场景：presenter_male(男主持), presenter_female(女主持)\n"
            "• 青年对话：male-qn-jingying(精英男), female-yujie(御姐女)\n"
            "• 有声书：audiobook_male_1(男朗读), audiobook_female_1(女朗读)\n"
            "• 角色扮演：junlang_nanyou(俊朗男友), tianxin_xiaoling(甜心女)\n"
            "• 高质量：male-qn-jingying-jingpin(精品男), female-yujie-jingpin(精品女)\n\n"
            "示例：{'主持人': 'presenter_female', '嘉宾': 'presenter_male', '专家': 'male-qn-jingying'}\n"
            "不提供时自动分配：第1、3、5...个说话人用女声，第2、4、6...个说话人用男声"
        )
    )

    generate_timestamps: Optional[bool] = Field(
        default=True,
        description="是否生成时间戳文件。开启后会生成包含每句话精确时间的JSON文件，用于视频制作、字幕同步等"
    )

    enable_concurrent: Optional[bool] = Field(
        default=True,
        description="是否启用并发生成模式。开启后多句话同时处理，速度提升3-5倍，强烈推荐保持开启"
    )

    output_format: Optional[str] = Field(
        default="mp3",
        description="音频输出格式。支持mp3(推荐)、wav等格式，mp3文件更小便于传输"
    )

    return_individual_audios: Optional[bool] = Field(
        default=True,
        description="是否返回每句话的独立音频URL。开启后可获得每句话的单独音频文件，便于后期编辑"
    )

async def _generate_single_audio_async(
    session: aiohttp.ClientSession,
    dialogue: DialogueItem,
    voice_id: str,
    group_id: str,
    output_format: str,
    idx: int
) -> Dict[str, Any]:
    """
    异步生成单句音频

    Args:
        session: aiohttp会话
        dialogue: 对话项
        voice_id: 声音ID
        group_id: Minimax组ID
        output_format: 输出格式
        idx: 序号

    Returns:
        包含音频数据和元信息的字典
    """
    api_url = f"https://api.minimaxi.com/v1/t2a_v2?GroupId={group_id}"

    payload = {
        "model": "speech-02-hd",  # 使用配置中的默认模型
        "text": dialogue.text,
        "voice_setting": {
            "voice_id": voice_id,
            "speed": dialogue.speed,
            "vol": dialogue.vol,
            "pitch": dialogue.pitch,
            "emotion": dialogue.emotion
        },
        "audio_setting": {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1
        },
        "stream": False,
        "output_format": "hex"  # 使用hex格式便于处理
    }

    try:
        logger.info(f"🎤 并发生成第{idx+1}句音频: {dialogue.speaker} - {dialogue.text[:20]}...")

        async with session.post(api_url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                return {
                    "success": False,
                    "error": f"API调用失败: HTTP {response.status} - {error_text}",
                    "idx": idx,
                    "dialogue": dialogue
                }

            response_data = await response.json()

            # 检查API响应状态
            base_resp = response_data.get("base_resp", {})
            if base_resp.get("status_code") != 0:
                error_msg = base_resp.get("status_msg", "Unknown API error")
                return {
                    "success": False,
                    "error": f"API错误 (code {base_resp.get('status_code')}): {error_msg}",
                    "idx": idx,
                    "dialogue": dialogue
                }

            # 处理API响应，获取音频数据
            data = response_data.get("data", {})
            audio_url = data.get("audio_url")
            audio_hex = data.get("audio")  # hex格式的音频数据

            if audio_url:
                # 如果返回URL，下载音频
                async with session.get(audio_url) as audio_response:
                    if audio_response.status == 200:
                        audio_bytes = await audio_response.read()
                    else:
                        return {
                            "success": False,
                            "error": f"音频下载失败: HTTP {audio_response.status}",
                            "idx": idx,
                            "dialogue": dialogue
                        }
            elif audio_hex:
                # 如果返回hex数据，解码
                try:
                    audio_bytes = bytes.fromhex(audio_hex)
                except (ValueError, TypeError) as e:
                    return {
                        "success": False,
                        "error": f"音频hex解码失败: {e}",
                        "idx": idx,
                        "dialogue": dialogue
                    }
            else:
                return {
                    "success": False,
                    "error": "API响应中缺少音频数据",
                    "idx": idx,
                    "dialogue": dialogue
                }

            return {
                "success": True,
                "audio_bytes": audio_bytes,
                "idx": idx,
                "dialogue": dialogue,
                "speaker": dialogue.speaker,
                "text": dialogue.text
            }

    except Exception as e:
        logger.error(f"生成第{idx+1}句音频时发生错误: {e}")
        return {
            "success": False,
            "error": f"生成音频时发生错误: {str(e)}",
            "idx": idx,
            "dialogue": dialogue
        }

def _calculate_audio_duration(audio_bytes: bytes, output_format: str) -> float:
    """
    计算音频时长

    Args:
        audio_bytes: 音频字节数据
        output_format: 音频格式

    Returns:
        音频时长（秒）
    """
    try:
        # 创建临时文件计算时长
        with tempfile.NamedTemporaryFile(suffix=f".{output_format}", delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_path = temp_file.name

        # 使用soundfile计算精确时长
        audio_data, sample_rate = sf.read(temp_path)
        duration = len(audio_data) / sample_rate

        # 清理临时文件
        os.unlink(temp_path)

        return duration

    except Exception as e:
        logger.warning(f"无法计算音频时长: {e}，使用估算值")
        # 粗略估算：基于音频文件大小
        estimated_duration = len(audio_bytes) / 16000  # 估算秒数
        return max(1.0, estimated_duration)  # 至少1秒

async def _generate_audios_concurrently(
    dialogue_script: List[DialogueItem],
    voice_mapping: Optional[Dict[str, str]],
    group_id: str,
    output_format: str,
    config: Configuration
) -> List[Dict[str, Any]]:
    """
    并发生成所有音频，最大并发数限制为15

    Args:
        dialogue_script: 对话脚本
        voice_mapping: 声音映射
        group_id: Minimax组ID
        output_format: 输出格式

    Returns:
        音频生成结果列表
    """
    # 检查API密钥
    api_key = os.getenv("MINIMAX_API_KEY")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 设置最大并发数为15
    MAX_CONCURRENT = 15
    semaphore = asyncio.Semaphore(MAX_CONCURRENT)

    async def _generate_with_semaphore(idx: int, dialogue: DialogueItem) -> Dict[str, Any]:
        """带信号量控制的音频生成"""
        async with semaphore:
            # 获取对应的声音ID
            voice_id = config.minimax_default_female_voice  # 默认使用女声
            if voice_mapping and dialogue.speaker in voice_mapping:
                voice_id = voice_mapping[dialogue.speaker]
            elif not voice_mapping:
                # 如果没有声音映射，根据说话人索引自动分配
                if idx % 2 == 0:
                    voice_id = config.minimax_default_female_voice
                else:
                    voice_id = config.minimax_default_male_voice

            return await _generate_single_audio_async(
                session, dialogue, voice_id, group_id, output_format, idx
            )

    async with aiohttp.ClientSession(headers=headers, timeout=aiohttp.ClientTimeout(total=120)) as session:
        tasks = []

        for idx, dialogue in enumerate(dialogue_script):
            # 创建带信号量控制的异步任务
            task = _generate_with_semaphore(idx, dialogue)
            tasks.append(task)

        # 并发执行所有任务，最大并发数为15
        logger.info(f"⚡ 并发执行{len(tasks)}个音频生成任务（最大并发数：{MAX_CONCURRENT}）...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": f"任务执行异常: {str(result)}",
                    "idx": i,
                    "dialogue": dialogue_script[i]
                })
            else:
                processed_results.append(result)

        return processed_results

async def _generate_audios_sequentially(
    dialogue_script: List[DialogueItem],
    voice_mapping: Optional[Dict[str, str]],
    group_id: str,
    output_format: str,
    headers: Dict[str, str],
    config: Configuration
) -> List[Dict[str, Any]]:
    """
    串行生成音频（备用方案）

    Args:
        dialogue_script: 对话脚本
        voice_mapping: 声音映射
        group_id: Minimax组ID
        output_format: 输出格式
        headers: 请求头

    Returns:
        音频生成结果列表
    """
    api_url = f"https://api.minimaxi.com/v1/t2a_v2?GroupId={group_id}"
    results = []

    for idx, dialogue in enumerate(dialogue_script):
        # 获取对应的声音ID
        voice_id = config.minimax_default_female_voice  # 默认使用女声
        if voice_mapping and dialogue.speaker in voice_mapping:
            voice_id = voice_mapping[dialogue.speaker]
        elif not voice_mapping:
            # 如果没有声音映射，根据说话人索引自动分配
            if idx % 2 == 0:
                voice_id = config.minimax_default_female_voice
            else:
                voice_id = config.minimax_default_male_voice

        # 构建API请求 - 使用正确的Minimax API格式
        payload = {
            "model": config.minimax_default_model,
            "text": dialogue.text,
            "voice_setting": {
                "voice_id": voice_id,
                "speed": dialogue.speed,
                "vol": dialogue.vol,
                "pitch": dialogue.pitch,
                "emotion": dialogue.emotion
            },
            "audio_setting": {
                "sample_rate": 32000,
                "bitrate": 128000,
                "format": "mp3",
                "channel": 1
            },
            "stream": False,
            "output_format": "hex"
        }

        logger.info(f"🎤 生成第{idx+1}句音频: {dialogue.speaker} - {dialogue.text[:20]}...")

        try:
            # 调用Minimax TTS API
            response = requests.post(api_url, headers=headers, json=payload, timeout=60)

            if response.status_code != 200:
                error_msg = f"Minimax API调用失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json().get("error", {}).get("message", "未知错误")
                    error_msg += f" - {error_detail}"
                except:
                    pass

                results.append({
                    "success": False,
                    "error": error_msg,
                    "idx": idx,
                    "dialogue": dialogue
                })
                continue

            # 处理API响应
            response_data = response.json()

            # 检查API响应状态
            base_resp = response_data.get("base_resp", {})
            if base_resp.get("status_code") != 0:
                error_msg = base_resp.get("status_msg", "Unknown API error")
                results.append({
                    "success": False,
                    "error": f"API错误 (code {base_resp.get('status_code')}): {error_msg}",
                    "idx": idx,
                    "dialogue": dialogue
                })
                continue

            # 获取音频数据
            data = response_data.get("data", {})
            audio_url = data.get("audio_url")
            audio_hex = data.get("audio")

            if audio_url:
                audio_response = requests.get(audio_url, timeout=30)
                audio_response.raise_for_status()
                audio_bytes = audio_response.content
            elif audio_hex:
                try:
                    audio_bytes = bytes.fromhex(audio_hex)
                except (ValueError, TypeError) as e:
                    results.append({
                        "success": False,
                        "error": f"音频hex解码失败: {e}",
                        "idx": idx,
                        "dialogue": dialogue
                    })
                    continue
            else:
                results.append({
                    "success": False,
                    "error": "API响应中缺少音频数据",
                    "idx": idx,
                    "dialogue": dialogue
                })
                continue

            results.append({
                "success": True,
                "audio_bytes": audio_bytes,
                "idx": idx,
                "dialogue": dialogue,
                "speaker": dialogue.speaker,
                "text": dialogue.text
            })

        except Exception as e:
            logger.error(f"生成第{idx+1}句音频时发生错误: {e}")
            results.append({
                "success": False,
                "error": f"生成音频时发生错误: {str(e)}",
                "idx": idx,
                "dialogue": dialogue
            })

    return results

async def _execute_multi_speaker_tts(
    config: Configuration,
    **kwargs: Any
) -> Dict[str, Any]:
    """
    执行多人TTS生成的核心逻辑
    
    Args:
        config: DeerFlow配置对象
        **kwargs: 工具输入参数
        
    Returns:
        包含生成结果的字典
    """
    try:
        # 解析输入参数
        args = MultiSpeakerTTSInput(**kwargs)
    except Exception as e:
        return f"❌ 输入参数错误: {str(e)}"

    # 检查Minimax API配置
    api_key = config.minimax_api_key or os.getenv("MINIMAX_API_KEY")
    group_id = config.minimax_group_id or os.getenv("MINIMAX_GROUP_ID")

    if not api_key or not group_id:
        return "❌ Minimax API配置缺失。请设置环境变量：MINIMAX_API_KEY 和 MINIMAX_GROUP_ID"

    # 初始化COS客户端
    try:
        cos_client = get_cos_client(config)
    except ValueError as e:
        logger.error(f"Failed to initialize COS client: {e}")
        return f"❌ 存储客户端初始化失败: {e}"

    # Minimax TTS API配置
    api_url = f"https://api.minimaxi.com/v1/t2a_v2?GroupId={group_id}"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 生成音频片段和时间戳
    try:
        if args.enable_concurrent:
            # 并发生成所有音频
            logger.info(f"🚀 启用并发模式，同时生成{len(args.dialogue_script)}句音频...")
            audio_results = await _generate_audios_concurrently(
                args.dialogue_script,
                args.voice_mapping,
                group_id,
                args.output_format,
                config
            )
        else:
            # 串行生成音频
            if args.enable_concurrent and not AIOHTTP_AVAILABLE:
                logger.warning("aiohttp库不可用，自动切换到串行模式")
            logger.info("🔄 使用串行模式生成音频...")
            audio_results = await _generate_audios_sequentially(
                args.dialogue_script,
                args.voice_mapping,
                group_id,
                args.output_format,
                headers,
                config
            )

        # 检查是否有失败的音频生成
        failed_results = [r for r in audio_results if not r["success"]]
        if failed_results:
            error_msgs = [f"第{r['idx']+1}句: {r['error']}" for r in failed_results]
            return f"❌ 音频生成失败:\n" + "\n".join(error_msgs)

        # 按原始顺序排序
        audio_results.sort(key=lambda x: x["idx"])

        # 计算时长并生成时间戳
        audio_segments = []
        timestamps_data = []
        individual_audios = []
        current_time = 0.0

        for result in audio_results:
            # 计算音频时长
            duration = _calculate_audio_duration(result["audio_bytes"], args.output_format)

            # 上传单独的音频文件（如果需要）
            individual_audio_url = None
            if args.return_individual_audios:
                individual_audio_url = upload_to_cos(
                    client=cos_client,
                    file_bytes=result["audio_bytes"],
                    bucket=config.cos_bucket,
                    region=config.cos_region,
                    file_extension=args.output_format
                )

            # 保存音频片段信息
            audio_segments.append({
                "audio_bytes": result["audio_bytes"],
                "duration": duration,
                "speaker": result["speaker"],
                "text": result["text"]
            })

            # 计算时间戳
            end_time = current_time + duration
            timestamp_item = {
                "id": result["idx"] + 1,
                "timestamp": round(end_time, 3),
                "content": f"[{result['speaker']}] {result['text']}",
                "speaker": result["speaker"],
                "start_time": round(current_time, 3),
                "end_time": round(end_time, 3),
                "duration": round(duration, 3)
            }

            # 如果有独立音频URL，添加到时间戳中
            if individual_audio_url:
                timestamp_item["audio_url"] = individual_audio_url

            timestamps_data.append(timestamp_item)

            # 保存独立音频信息
            if individual_audio_url:
                individual_audios.append({
                    "id": result["idx"] + 1,
                    "speaker": result["speaker"],
                    "text": result["text"],
                    "audio_url": individual_audio_url,
                    "duration": round(duration, 3),
                    "start_time": round(current_time, 3),
                    "end_time": round(end_time, 3)
                })

            current_time = end_time
            
        # 合并音频片段
        logger.info("合并音频片段...")
        merged_audio_bytes = _merge_audio_segments(audio_segments, args.output_format)
        
        # 上传到COS
        logger.info("上传音频到存储...")
        audio_cos_url = upload_to_cos(
            client=cos_client,
            file_bytes=merged_audio_bytes,
            bucket=config.cos_bucket,
            region=config.cos_region,
            file_extension=args.output_format
        )
        
        # 生成时间戳文件
        timestamps_result = None
        if args.generate_timestamps:
            timestamps_json = {
                "dialogue_data": {
                    "total_duration": round(current_time, 3),
                    "speaker_count": len(set(item["speaker"] for item in timestamps_data)),
                    "sentence_count": len(timestamps_data),
                    "chunks": timestamps_data
                }
            }
            
            # 上传时间戳文件
            timestamps_bytes = json.dumps(timestamps_json, ensure_ascii=False, indent=2).encode('utf-8')
            timestamps_cos_url = upload_to_cos(
                client=cos_client,
                file_bytes=timestamps_bytes,
                bucket=config.cos_bucket,
                region=config.cos_region,
                file_extension="json"
            )
            
            timestamps_result = {
                "url": timestamps_cos_url,
                "data": timestamps_json
            }
        
        # 构建简洁的返回结果
        result_message = (
            f"✅ 多人对话音频生成成功！\n"
            f"🎵 完整音频：{audio_cos_url}\n"
            f"⏱️ 总时长：{round(current_time, 3)}秒\n"
            f"👥 说话人数：{len(set(item['speaker'] for item in timestamps_data))}人\n"
            f"📝 对话句数：{len(timestamps_data)}句\n"
            f"🚀 生成模式：{'并发' if args.enable_concurrent else '串行'}"
        )

        if timestamps_result:
            result_message += f"\n📊 时间戳文件：{timestamps_result['url']}"

        if individual_audios:
            result_message += f"\n📁 独立音频：{len(individual_audios)}个文件已生成"

        return result_message
        
    except requests.exceptions.RequestException as e:
        logger.error(f"API请求失败: {e}")
        return f"❌ API请求失败: {str(e)}"
    except Exception as e:
        logger.error(f"多人TTS生成过程中发生错误: {e}", exc_info=True)
        return f"❌ 生成过程中发生错误: {str(e)}"

def _merge_audio_segments(audio_segments: List[Dict], output_format: str) -> bytes:
    """
    合并音频片段为单个音频文件

    Args:
        audio_segments: 音频片段列表
        output_format: 输出格式

    Returns:
        合并后的音频字节数据
    """
    try:
        # 尝试使用pydub进行高质量合并
        from pydub import AudioSegment

        # 创建空音频
        merged_audio = AudioSegment.silent(duration=0)

        for segment in audio_segments:
            # 从字节数据创建AudioSegment
            import io
            audio_io = io.BytesIO(segment["audio_bytes"])
            audio_segment = AudioSegment.from_file(audio_io, format=output_format)

            # 直接拼接，无静音间隔
            merged_audio += audio_segment

        # 导出为字节数据
        output_io = io.BytesIO()
        merged_audio.export(output_io, format=output_format)
        return output_io.getvalue()

    except ImportError:
        logger.warning("pydub库不可用，使用简单的字节拼接")
        # 简单的字节拼接作为备选方案
        return _simple_audio_concat(audio_segments)
    except Exception as e:
        logger.warning(f"pydub合并失败: {e}，使用简单的字节拼接")
        return _simple_audio_concat(audio_segments)

def _simple_audio_concat(audio_segments: List[Dict]) -> bytes:
    """
    简单的音频字节拼接（备选方案）

    Args:
        audio_segments: 音频片段列表

    Returns:
        拼接后的音频字节数据
    """
    logger.info("使用简单字节拼接模式合并音频")

    # 简单地将所有音频字节拼接在一起
    merged_bytes = b""
    for segment in audio_segments:
        merged_bytes += segment["audio_bytes"]

    return merged_bytes

async def _arun_execute_multi_speaker_tts(
    config: Configuration,
    **kwargs: Any
) -> Dict[str, Any]:
    """异步执行多人TTS生成"""
    try:
        return await _execute_multi_speaker_tts(config, **kwargs)
    except Exception as e:
        logger.error(f"异步多人TTS生成错误: {e}", exc_info=True)
        return f"❌ 异步执行错误: {str(e)}"

def _sync_execute_multi_speaker_tts(
    config: Configuration,
    **kwargs: Any
) -> Dict[str, Any]:
    """同步执行多人TTS生成的包装函数"""
    try:
        return asyncio.run(_execute_multi_speaker_tts(config, **kwargs))
    except Exception as e:
        logger.error(f"同步多人TTS生成错误: {e}", exc_info=True)
        return f"❌ 同步执行错误: {str(e)}"

# 移除了_generate_complete_voice_description函数，改为静态内容以避免动态生成导致的JSON解析问题

def get_multi_speaker_tts_tool(config: Configuration) -> Optional[StructuredTool]:
    """
    创建多人TTS工具实例

    Args:
        config: DeerFlow配置对象

    Returns:
        配置好的StructuredTool实例，如果配置不完整则返回None
    """
    # 检查必要的配置
    if not config.minimax_api_key or not config.minimax_group_id:
        logger.warning("多人TTS工具已禁用: 缺少MINIMAX_API_KEY或MINIMAX_GROUP_ID配置")
        return None

    # 绑定配置对象到执行函数
    sync_func_with_config = partial(_sync_execute_multi_speaker_tts, config)
    async_func_with_config = partial(_arun_execute_multi_speaker_tts, config)
    
    return StructuredTool.from_function(
        func=sync_func_with_config,
        coroutine=async_func_with_config,
        name="multi_speaker_tts",
        description=(
            "Professional multi-speaker TTS tool for generating dialogue audio with multiple voices. "
            "Supports podcasts, audiobooks, educational content, and character conversations using Minimax TTS. "
            "Features 46 voice options (23 male, 21 female, 2 neutral) including youth, professional, audiobook, premium, children, and character voices. "
            "Automatic voice assignment or custom voice mapping per speaker. "
            "Concurrent audio generation for faster processing. "
            "Emotion control (happy/sad/angry/calm), speed (0.5-2.0x), volume, and pitch adjustment. "
            "Complete dialogue audio + individual clips + timestamp file output. "
            "Usage: Provide dialogue_script with speaker and text fields. "
            "Recommended voice combinations: professional (presenter_male + presenter_female), youth (male-qn-jingying + female-yujie), audiobook, character, and premium pairs available."
        ),
        args_schema=MultiSpeakerTTSInput
    )

# 测试代码
if __name__ == "__main__":
    import asyncio
    from dotenv import load_dotenv
    
    logging.basicConfig(level=logging.INFO)
    load_dotenv(dotenv_path='../../../.env')
    
    # 创建测试配置
    class TestConfig(Configuration):
        def __init__(self):
            super().__init__()
            self.tencent_secret_id = os.getenv("TENCENT_SECRET_ID")
            self.tencent_secret_key = os.getenv("TENCENT_SECRET_KEY")
            self.cos_region = os.getenv("COS_REGION")
            self.cos_bucket = os.getenv("COS_BUCKET")
    
    test_config = TestConfig()
    
    # 检查必要的环境变量
    required_vars = ["MINIMAX_API_KEY", "MINIMAX_GROUP_ID"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"缺少环境变量: {missing_vars}")
    else:
        # 创建工具实例
        tts_tool = get_multi_speaker_tts_tool(config=test_config)
        
        async def test_multi_speaker_tts():
            logger.info("🎭 测试多人TTS工具...")
            
            # 测试对话脚本
            test_dialogue = [
                {
                    "speaker": "张三",
                    "text": "各位观众朋友们，大家好！今天我们来聊聊人工智能。",
                    "emotion": "happy",
                    "speed": 1.0
                },
                {
                    "speaker": "李四", 
                    "text": "人工智能？这个话题很有意思啊！",
                    "emotion": "surprised",
                    "speed": 0.9
                },
                {
                    "speaker": "张三",
                    "text": "是的，AI现在发展得特别快，就像我们现在用的这个语音合成技术。",
                    "emotion": "neutral",
                    "speed": 1.1
                },
                {
                    "speaker": "李四",
                    "text": "哇，听起来真的很神奇！",
                    "emotion": "happy",
                    "speed": 1.0
                }
            ]
            
            test_input = {
                "dialogue_script": test_dialogue,
                "voice_mapping": {
                    "张三": "voice_001",
                    "李四": "voice_002"
                },
                "output_format": "wav",
                "generate_timestamps": True,
                "enable_concurrent": True,  # 启用并发生成
                "return_individual_audios": True  # 返回独立音频URL
            }
            
            try:
                result = await tts_tool.arun(tool_input=test_input)
                logger.info(f"✅ 测试结果: {result}")
            except Exception as e:
                logger.error(f"❌ 测试失败: {e}", exc_info=True)
        
        # 运行测试
        asyncio.run(test_multi_speaker_tts())
