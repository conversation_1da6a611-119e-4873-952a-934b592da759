# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
builtin_templates.py

This module defines the built-in templates that come with DeerFlow.
These templates cover common video creation scenarios.
"""

from src.graph_v2.template_models import PlanTemplate, StepTemplate, ParameterSchema, ParameterType
from src.graph_v2.enhanced_models import StepType
from src.tools.template_tools import register_template


def create_ai_parody_video_template() -> PlanTemplate:
    """Create the AI parody video template."""
    return PlanTemplate(
        template_id="ai_parody_video",
        name="AI鬼畜视频制作",
        description="制作具有鬼畜效果的AI视频，包含素材收集、特效制作、音频处理和视频合成",
        category="video_creation",
        tags=["ai", "parody", "video", "entertainment", "鬼畜"],
        
        parameters={
            "character": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="主角名称（如：哪吒、孙悟空等）"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="modern",
                options=["modern", "traditional", "cartoon", "realistic"],
                description="视觉风格"
            ),
            "duration": ParameterSchema(
                type=ParameterType.INTEGER,
                default=30,
                min_value=10,
                max_value=120,
                description="视频时长（秒）"
            ),
            "effect_intensity": ParameterSchema(
                type=ParameterType.STRING,
                default="medium",
                options=["low", "medium", "high", "extreme"],
                description="鬼畜效果强度"
            ),
            "background_music": ParameterSchema(
                type=ParameterType.BOOLEAN,
                default=True,
                description="是否添加背景音乐"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="collect_materials",
                name="素材收集",
                description_template="收集{character}相关的图片和视频素材，风格为{style}",
                tool_to_use="visual_expert",
                step_type=StepType.DATA_COLLECTION,
                input_template={
                    "task_description": "收集{character}的高质量图片素材，要求{style}风格，数量10-15张",
                    "search_query": "{character}",
                    "style_preference": "{style}",
                    "material_count": 12,
                    "quality_requirements": "high_resolution"
                },
                estimated_duration=60
            ),
            
            StepTemplate(
                template_step_id="create_base_character",
                name="角色形象设计",
                description_template="基于收集的素材，设计{character}的标准形象",
                tool_to_use="visual_expert",
                step_type=StepType.CONTENT_CREATION,
                dependencies=["collect_materials"],
                input_template={
                    "task_description": "基于提供的素材，创建{character}的标准角色形象，风格为{style}，适合用于鬼畜视频",
                    "source_materials": "{{collect_materials.assets.images}}",
                    "character_name": "{character}",
                    "style": "{style}",
                    "output_format": "character_sheet"
                },
                estimated_duration=90
            ),
            
            StepTemplate(
                template_step_id="create_parody_effects",
                name="鬼畜特效制作",
                description_template="为{character}创建{effect_intensity}强度的鬼畜特效",
                tool_to_use="visual_expert",
                step_type=StepType.CONTENT_EDITING,
                dependencies=["create_base_character"],
                input_template={
                    "task_description": "为角色创建鬼畜特效，包括表情变化、动作夸张、色彩变化等，强度为{effect_intensity}",
                    "source_images": "{{create_base_character.assets.images}}",
                    "effect_type": "parody",
                    "intensity": "{effect_intensity}",
                    "duration": "{duration}",
                    "frame_count": 30
                },
                estimated_duration=120
            ),
            
            StepTemplate(
                template_step_id="create_audio",
                name="音频制作",
                description_template="创建配套的音频内容，包括配音和背景音乐",
                tool_to_use="audio_expert",
                step_type=StepType.CONTENT_CREATION,
                input_template={
                    "task_description": "为{character}鬼畜视频创建音频内容，包括夸张的配音效果和背景音乐",
                    "character_name": "{character}",
                    "style": "parody",
                    "duration": "{duration}",
                    "include_background_music": "{background_music}",
                    "voice_effects": ["pitch_shift", "speed_change", "echo"]
                },
                parallel_group="audio_video",
                estimated_duration=100
            ),
            
            StepTemplate(
                template_step_id="video_synthesis",
                name="视频合成",
                description_template="将视觉特效和音频合成为最终的{character}鬼畜视频",
                tool_to_use="video_expert",
                step_type=StepType.FINALIZATION,
                dependencies=["create_parody_effects", "create_audio"],
                input_template={
                    "task_description": "合成最终的鬼畜视频，同步视觉特效和音频",
                    "visual_effects": "{{create_parody_effects.assets.images}}",
                    "audio_track": "{{create_audio.assets.audio}}",
                    "duration": "{duration}",
                    "output_format": "mp4",
                    "quality": "high",
                    "sync_mode": "auto"
                },
                estimated_duration=80
            )
        ],
        
        estimated_duration=450,
        difficulty_level="medium"
    )


def create_product_promo_template() -> PlanTemplate:
    """Create the product promotion video template."""
    return PlanTemplate(
        template_id="product_promo_video",
        name="产品宣传视频",
        description="制作专业的产品宣传视频，包含产品展示、特性介绍和行动号召",
        category="video_creation",
        tags=["product", "marketing", "promotion", "business"],
        
        parameters={
            "product_name": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                description="产品名称"
            ),
            "product_type": ParameterSchema(
                type=ParameterType.STRING,
                required=True,
                options=["software", "hardware", "service", "app", "other"],
                description="产品类型"
            ),
            "target_audience": ParameterSchema(
                type=ParameterType.STRING,
                default="general",
                options=["general", "business", "consumer", "developer", "student"],
                description="目标受众"
            ),
            "video_style": ParameterSchema(
                type=ParameterType.STRING,
                default="professional",
                options=["professional", "casual", "modern", "minimalist", "dynamic"],
                description="视频风格"
            ),
            "duration": ParameterSchema(
                type=ParameterType.INTEGER,
                default=60,
                min_value=30,
                max_value=180,
                description="视频时长（秒）"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="product_showcase",
                name="产品展示设计",
                description_template="设计{product_name}的产品展示画面，风格为{video_style}",
                tool_to_use="visual_expert",
                input_template={
                    "task_description": "创建{product_name}的产品展示图像，突出产品特色，风格为{video_style}，适合{target_audience}受众",
                    "product_name": "{product_name}",
                    "product_type": "{product_type}",
                    "style": "{video_style}",
                    "target_audience": "{target_audience}",
                    "layout": "hero_shot"
                },
                estimated_duration=90
            ),
            
            StepTemplate(
                template_step_id="feature_highlights",
                name="特性亮点设计",
                description_template="设计{product_name}的核心特性展示",
                tool_to_use="visual_expert",
                dependencies=["product_showcase"],
                input_template={
                    "task_description": "创建产品核心特性的可视化展示，包括功能图标、特性说明等",
                    "product_context": "{{product_showcase.result}}",
                    "style": "{video_style}",
                    "feature_count": 3,
                    "layout": "feature_grid"
                },
                estimated_duration=75
            ),
            
            StepTemplate(
                template_step_id="script_and_voiceover",
                name="脚本和配音",
                description_template="创建宣传脚本并生成专业配音",
                tool_to_use="audio_expert",
                input_template={
                    "task_description": "为{product_name}创建宣传脚本并生成配音，时长{duration}秒，适合{target_audience}",
                    "product_name": "{product_name}",
                    "product_type": "{product_type}",
                    "target_audience": "{target_audience}",
                    "duration": "{duration}",
                    "tone": "professional",
                    "include_cta": True
                },
                parallel_group="content_creation",
                estimated_duration=100
            ),
            
            StepTemplate(
                template_step_id="final_video_assembly",
                name="视频最终合成",
                description_template="将所有元素合成为完整的{product_name}宣传视频",
                tool_to_use="video_expert",
                dependencies=["product_showcase", "feature_highlights", "script_and_voiceover"],
                input_template={
                    "task_description": "合成完整的产品宣传视频，包含产品展示、特性介绍和配音",
                    "product_showcase": "{{product_showcase.assets.images}}",
                    "feature_highlights": "{{feature_highlights.assets.images}}",
                    "voiceover": "{{script_and_voiceover.assets.audio}}",
                    "duration": "{duration}",
                    "style": "{video_style}",
                    "output_quality": "high"
                },
                estimated_duration=90
            )
        ],
        
        estimated_duration=355,
        difficulty_level="easy"
    )


def create_city_poster_series_template() -> PlanTemplate:
    """Create the city poster series template."""
    return PlanTemplate(
        template_id="city_poster_series",
        name="城市主题海报系列",
        description="制作一系列风格一致的城市主题海报，适合旅游宣传或城市品牌推广",
        category="image_series",
        tags=["poster", "city", "series", "tourism", "branding"],
        
        parameters={
            "cities": ParameterSchema(
                type=ParameterType.LIST,
                required=True,
                description="城市列表（如：['北京', '上海', '广州']）"
            ),
            "theme": ParameterSchema(
                type=ParameterType.STRING,
                default="tourism",
                options=["tourism", "food", "culture", "modern", "traditional"],
                description="海报主题"
            ),
            "style": ParameterSchema(
                type=ParameterType.STRING,
                default="modern",
                options=["modern", "vintage", "minimalist", "artistic", "photographic"],
                description="设计风格"
            ),
            "color_scheme": ParameterSchema(
                type=ParameterType.STRING,
                default="vibrant",
                options=["vibrant", "pastel", "monochrome", "warm", "cool"],
                description="色彩方案"
            )
        },
        
        step_templates=[
            StepTemplate(
                template_step_id="establish_style_guide",
                name="建立风格指南",
                description_template="为{theme}主题的城市海报系列建立统一的{style}风格指南",
                tool_to_use="visual_expert",
                input_template={
                    "task_description": "创建城市海报系列的风格指南，主题为{theme}，风格为{style}，色彩方案为{color_scheme}",
                    "theme": "{theme}",
                    "style": "{style}",
                    "color_scheme": "{color_scheme}",
                    "output_type": "style_guide",
                    "include_typography": True,
                    "include_color_palette": True
                },
                estimated_duration=120
            ),
            
            StepTemplate(
                template_step_id="create_poster_series",
                name="批量生成海报",
                description_template="基于风格指南为每个城市创建海报",
                tool_to_use="visual_expert",
                dependencies=["establish_style_guide"],
                input_template={
                    "task_description": "为城市列表中的每个城市创建海报，保持风格一致性",
                    "cities": "{cities}",
                    "style_guide": "{{establish_style_guide.assets.style_guide}}",
                    "theme": "{theme}",
                    "batch_mode": True,
                    "consistency_check": True
                },
                estimated_duration=180
            ),
            
            StepTemplate(
                template_step_id="quality_review",
                name="质量检查和优化",
                description_template="检查海报系列的一致性和质量，进行必要的调整",
                tool_to_use="visual_expert",
                dependencies=["create_poster_series"],
                input_template={
                    "task_description": "检查海报系列的视觉一致性，调整不符合风格指南的元素",
                    "poster_series": "{{create_poster_series.assets.images}}",
                    "style_guide": "{{establish_style_guide.assets.style_guide}}",
                    "check_consistency": True,
                    "auto_adjust": True
                },
                estimated_duration=90
            )
        ],
        
        estimated_duration=390,
        difficulty_level="easy"
    )


def load_builtin_templates():
    """Load and register all built-in templates."""
    templates = [
        create_ai_parody_video_template(),
        create_product_promo_template(),
        create_city_poster_series_template()
    ]
    
    for template in templates:
        register_template(template)
    
    print(f"Loaded {len(templates)} built-in templates")
    return templates
