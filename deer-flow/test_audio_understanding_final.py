#!/usr/bin/env python3
"""
最终测试音频理解工具
"""

import logging
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_audio_understanding_final():
    """最终测试音频理解工具"""
    logger.info("🎤 最终测试音频理解工具...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.understanding.audio_understanding import get_audio_understanding_tool
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 创建工具
        tool = get_audio_understanding_tool(config)
        logger.info("✅ 音频理解工具创建成功")
        logger.info(f"📋 工具名称: {tool.name}")
        logger.info(f"📋 工具类型: {type(tool).__name__}")
        logger.info(f"📋 描述长度: {len(tool.description)} 字符")
        
        # 检查工具属性
        attrs = ['name', 'description', 'func', 'coroutine', 'args_schema']
        for attr in attrs:
            if hasattr(tool, attr):
                logger.info(f"✅ 有属性: {attr}")
            else:
                logger.warning(f"⚠️ 缺少属性: {attr}")
        
        # 测试args_schema
        if hasattr(tool, 'args_schema'):
            schema = tool.args_schema
            logger.info(f"✅ args_schema: {schema.__name__}")
            
            # 创建测试实例
            test_instance = schema(
                audio_url="https://example.com/test.mp3",
                question="Please analyze this audio"
            )
            logger.info("✅ args_schema实例创建成功")
            logger.info(f"📋 实例数据: {test_instance.dict()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 最终测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_check():
    """测试集成检查"""
    logger.info("🔗 测试集成检查...")
    
    try:
        # 检查文件内容
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/understanding/audio_understanding.py', 'r') as f:
            content = f.read()
        
        # 检查关键内容
        checks = {
            "包含SimpleAudioAnalysisInput": "class SimpleAudioAnalysisInput" in content,
            "包含get_audio_understanding_tool": "def get_audio_understanding_tool" in content,
            "使用多模态工具": "get_multimodal_understanding_tool" in content,
            "包含包装器函数": "audio_analysis_wrapper" in content,
            "返回StructuredTool": "return StructuredTool.from_function" in content,
            "文件长度合理": len(content) > 2000 and len(content) < 5000,
            "没有旧代码残留": "_analyze_audio_with_gemini" not in content
        }
        
        logger.info("🔍 集成检查:")
        all_good = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                all_good = False
        
        logger.info(f"📏 文件长度: {len(content)} 字符")
        
        return all_good
        
    except Exception as e:
        logger.error(f"❌ 集成检查失败: {e}")
        return False

def test_understanding_module():
    """测试understanding模块"""
    logger.info("📦 测试understanding模块...")
    
    try:
        # 检查__init__.py
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/understanding/__init__.py', 'r') as f:
            init_content = f.read()
        
        # 检查导入和导出
        init_checks = {
            "导入audio_understanding": "from .audio_understanding import get_audio_understanding_tool" in init_content,
            "包含在__all__中": "get_audio_understanding_tool" in init_content and "__all__" in init_content
        }
        
        logger.info("🔍 __init__.py检查:")
        init_good = True
        for check_name, passed in init_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                init_good = False
        
        # 检查V2 nodes.py
        with open('/Users/<USER>/openArt-1/deer-flow/src/graph_v2/nodes.py', 'r') as f:
            nodes_content = f.read()
        
        nodes_checks = {
            "导入audio_understanding": "get_audio_understanding_tool" in nodes_content,
            "添加到understanding_tools": "get_audio_understanding_tool(app_config)" in nodes_content
        }
        
        logger.info("🔍 nodes.py检查:")
        nodes_good = True
        for check_name, passed in nodes_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                nodes_good = False
        
        return init_good and nodes_good
        
    except Exception as e:
        logger.error(f"❌ understanding模块测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始音频理解工具最终测试...")
    logger.info("目标: 验证音频理解工具的完整实现和集成")
    
    tests = [
        ("集成检查", test_integration_check),
        ("understanding模块", test_understanding_module),
        ("音频理解工具", test_audio_understanding_final)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 音频理解工具最终测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("\n🎉 音频理解工具开发完全成功！")
        logger.info("")
        logger.info("🚀 **实现特点:**")
        logger.info("   ✅ 简洁的接口设计（仿照image_understanding.py）")
        logger.info("   ✅ 内部使用多模态理解工具")
        logger.info("   ✅ 支持音频URL输入")
        logger.info("   ✅ 自定义问题分析")
        logger.info("   ✅ 同步和异步支持")
        logger.info("   ✅ 完整的错误处理")
        logger.info("")
        logger.info("🔧 **技术架构:**")
        logger.info("   • 使用video_understanding.py的多模态工具")
        logger.info("   • 将audio_url转换为media_url")
        logger.info("   • 保持与其他理解工具一致的接口")
        logger.info("   • 基于Gemini模型进行音频分析")
        logger.info("")
        logger.info("📁 **文件结构:**")
        logger.info("   • src/tools/understanding/audio_understanding.py - 简化接口")
        logger.info("   • src/tools/understanding/__init__.py - 模块导出")
        logger.info("   • src/graph_v2/nodes.py - V2架构集成")
        logger.info("")
        logger.info("🎭 **使用方式:**")
        logger.info("   1. 在interactive_chat.py中:")
        logger.info("      '请分析这个音频文件的内容'")
        logger.info("      '帮我转录这段语音'")
        logger.info("      '分析这个音频的情感色彩'")
        logger.info("")
        logger.info("   2. 直接调用:")
        logger.info("      tool.invoke({")
        logger.info("          'audio_url': 'https://example.com/audio.mp3',")
        logger.info("          'question': 'Please analyze this audio content'")
        logger.info("      })")
        logger.info("")
        logger.info("🌟 **与现有工具的协同:**")
        logger.info("   • 与image_understanding保持一致的接口")
        logger.info("   • 与video_understanding共享底层实现")
        logger.info("   • 与multi_speaker_tts形成完整的音频处理闭环")
        logger.info("   • 为V2架构提供完整的多模态理解能力")
        logger.info("")
        logger.info("🎤 **音频理解工具已准备就绪！**")
        logger.info("现在您的系统拥有了完整的多模态理解能力：")
        logger.info("   🖼️ 图片理解 + 🎬 视频理解 + 🎤 音频理解")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
