# 🎭 语音克隆工具使用指南

## 📋 概述

语音克隆工具 (`clone_voice_from_audio`) 是 DeerFlow 系统中的一个专业音频处理工具，专为 AI Agent 设计，支持从音频 URL 或本地文件快速克隆人声音色。

## 🚀 核心特性

### ✨ 主要功能
- **🌐 URL 支持**：直接使用网络音频资源，无需下载
- **🎯 快速克隆**：10秒-5分钟音频即可生成音色
- **🎵 自动优化**：内置降噪和音量归一化
- **🔧 增强模式**：可选提示音频提升相似度
- **📝 即时试听**：自动生成试听音频验证效果

### 🎛️ 技术规格
- **支持格式**：mp3、m4a、wav
- **文件大小**：≤20MB
- **音频时长**：10秒-5分钟
- **音色保留**：7天有效期
- **合成模型**：固定使用 speech-02-hd（高质量）

## 📖 使用方法

### 基本用法

```python
# 最简单的使用方式（推荐给AI Agent）
result = clone_voice_from_audio(
    audio_file_path="https://example.com/voice_sample.mp3",
    voice_id="MyCustomVoice2024"
)
```

### 带试听的用法

```python
# 生成试听音频验证效果
result = clone_voice_from_audio(
    audio_file_path="https://example.com/voice_sample.mp3",
    voice_id="TestVoice123",
    test_text="这是一个音色克隆测试，请听听效果如何。"
)
```

### 高质量模式

```python
# 使用提示音频提升克隆质量
result = clone_voice_from_audio(
    audio_file_path="https://example.com/main_audio.mp3",
    voice_id="HighQualityVoice",
    prompt_audio_path="https://example.com/short_sample.mp3",
    prompt_text="这是提示音频的文本内容",
    test_text="测试克隆效果如何"
)
```

## 📋 参数说明

### 必需参数

| 参数 | 类型 | 说明 |
|------|------|------|
| `audio_file_path` | str | 音频文件URL或本地路径 |

### 可选参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `voice_id` | str | 自动生成 | 音色ID（8-256字符，字母开头） |
| `test_text` | str | None | 试听文本（≤2000字符） |
| `prompt_audio_path` | str | None | 提示音频URL或路径（<8秒） |
| `prompt_text` | str | None | 提示音频对应文本 |
| `enable_noise_reduction` | bool | True | 是否启用降噪 |
| `enable_volume_normalization` | bool | True | 是否启用音量归一化 |

### voice_id 规则

- **长度**：8-256字符
- **开头**：必须是英文字母
- **字符**：只能包含字母、数字、连字符(-)、下划线(_)
- **结尾**：不能是连字符或下划线
- **示例**：`MyVoice2024`、`Character_Voice_01`、`Custom-Voice-Test`

## 📊 返回格式

### 成功返回

```json
{
    "success": true,
    "voice_id": "MyCustomVoice2024",
    "main_file_id": "file_12345",
    "input_sensitive": false,
    "message": "✅ 音色克隆成功！新音色ID: MyCustomVoice2024",
    "demo_audio_url": "https://cos.example.com/demo_audio.mp3",
    "demo_audio_text": "这是试听文本",
    "usage_info": {
        "voice_id": "MyCustomVoice2024",
        "valid_period": "168小时（7天）",
        "usage_note": "请在7天内使用此音色进行语音合成，否则将被删除",
        "compatible_apis": ["同步语音合成", "异步长文本语音合成"]
    }
}
```

### 失败返回

```json
{
    "success": false,
    "error": "❌ 音色克隆失败: 文件格式不支持",
    "voice_id": "MyCustomVoice2024"
}
```

## 🎯 使用场景

### AI Agent 典型用法

1. **角色配音**：为AI生成的角色创建专属音色
2. **内容个性化**：为不同用户提供个性化语音体验
3. **音频制作**：在视频、播客制作中使用特定音色
4. **语音助手**：为AI助手配置独特的声音特征

### 最佳实践

1. **音频质量**：
   - 使用清晰、无杂音的录音
   - 避免背景音乐和噪声
   - 确保单人语音

2. **参数设置**：
   - 启用降噪和音量归一化（默认开启）
   - 使用有意义的 voice_id 便于管理
   - 提供试听文本验证效果

3. **增强模式**：
   - 提供短小精悍的提示音频（<8秒）
   - 确保提示文本与音频内容完全一致
   - 选择最具代表性的音频片段

## ⚠️ 注意事项

### 技术限制

- 音频文件大小不能超过 20MB
- 音频时长必须在 10秒-5分钟之间
- 仅支持 mp3、m4a、wav 格式
- 克隆的音色有效期为 7天

### 使用建议

- 优先使用 HTTPS URL，确保安全性
- 定期清理不再使用的音色ID
- 在正式使用前先进行试听验证
- 遵守相关法律法规和平台政策

## 🔧 故障排除

### 常见问题

1. **文件下载失败**
   - 检查URL是否可访问
   - 确认网络连接正常
   - 验证文件格式是否支持

2. **音色克隆失败**
   - 检查API密钥配置
   - 确认文件大小和时长符合要求
   - 验证voice_id格式是否正确

3. **试听音频无法生成**
   - 检查COS配置是否正确
   - 确认试听文本长度不超过2000字符
   - 验证网络连接稳定性

### 调试方法

```python
# 启用详细日志
import logging
logging.getLogger("src.tools.audio.voice_clone").setLevel(logging.DEBUG)

# 检查工具状态
from src.tools import voice_clone_tool
if voice_clone_tool:
    print("✅ 工具已正确加载")
else:
    print("❌ 工具加载失败，请检查配置")
```

## 📚 相关文档

- [DeerFlow 系统架构](./架构.md)
- [Audio Expert 详解](./audio_expert.md)
- [工具开发指南](./tool_development.md)
- [API 配置说明](./api_configuration.md)

---

*最后更新：2025-01-24*
