# DeerFlow 内部开发者文档

## 1. 项目概述

**DeerFlow** 是一个基于 LangGraph 构建的、先进的智能多模态创作工作流平台。其核心设计目标是创建一个**高度可观测、可扩展、可调试**的 Agent 系统，能够根据用户需求的复杂性动态选择最高效的执行路径。

- **对于简单任务** (如 "画一只猫")，系统会走"快车道"，由 `Coordinator` 直接委派给最合适的专家 Agent (如 `visual_agent`)。
- **对于复杂任务** (如 "制作一个关于哪吒的MV")，系统会激活 `Planner`，将任务拆解为多步骤计划，并按计划调度多个专家 Agent 协作完成。

本文档旨在为内部开发者提供全面的架构解析、工作流程说明和调试指南。

---

## 2. 核心架构 (The Big Picture)

DeerFlow 采用"协调员-规划师-专家" (Coordinator-Planner-Specialist) 的分层架构，并通过一个中央状态机 (`State`) 来驱动。

```mermaid
graph TD
    subgraph "用户交互层"
        A[用户输入 via main.py]
    end

    subgraph "工作流核心 (LangGraph)"
        B(State 对象)
        C(Coordinator Agent)
        D(Planner Agent)
        E(Specialist Agents <br/> visual_agent, audio_agent, ...)
        F(Graph an d Nodes)
    end

    subgraph "支撑模块"
        G[LLM 服务]
        H[工具集 Tools <br/> 图像生成, 音频API...]
        I[配置 & 提示词]
    end

    A --> |更新State| B
    B -- "读取状态" --> C
    C -- "更新决策" --> B
    C -- "路由" --> D
    C -- "路由" --> E

    D -- "读取状态" --> G
    D -- "生成Plan, 更新State" --> B

    E -- "读取任务, 调用工具" --> H
    H -- "返回结果" --> E
    E -- "记录成果, 更新State" --> B
  
    F -- "定义流程" --> C
    F -- "定义流程" --> D
    F -- "定义流程" --> E
  
    I -- "配置" --> G
    I -- "提示词" --> C
    I -- "提示词" --> D
    I -- "提示词" --> E
```

### 2.1. 关键组件解析

- **`State` (`src/graph/types.py`)**: **整个工作流的唯一事实来源 (Single Source of Truth)**。它是一个 Pydantic `dataclass`，继承自 LangGraph 的 `MessagesState`。

  - **重要**: 所有需要在节点间传递和累积的数据都**必须**在此类中显式定义。
  - **关键实践**: 对于列表类型的累积数据（如 `tasks_completed`），**必须**使用 `Annotated[list, operator.add]` 来注解，以确保 LangGraph 在合并图分支状态时执行的是**追加 (append)** 而不是**替换 (replace)** 操作。这是避免状态丢失的**核心**。
- **`Coordinator` (`src/agents/agents.py`)**: 工作流的**智能调度中心**。它不执行具体任务，只做决策。它通过分析用户输入和历史消息，决定下一步是 `direct_reply`, `delegate_to_agent`,还是 `planner`。
- **`Planner` (`src/agents/agents.py`)**: 复杂任务的**项目经理**。当被 `Coordinator` 激活时，它会调用 LLM 将用户的宏大目标分解为一个结构化的、多步骤的 `Plan` 对象。
- **`Specialist Agents` (`src/agents/agents.py`)**: **任务执行者**。每个专家（如 `visual_agent`, `audio_agent`）都是一个独立的 LangChain Agent，拥有自己的提示词和一套专属工具 (`src/tools`)，负责完成一个具体的任务。
- **`Graph` 和 `Nodes` (`src/graph/builder.py`, `nodes.py`)**: 系统的**骨架**。`nodes.py` 中定义了每个节点的具体逻辑（调用哪个 Agent、如何处理 State），`builder.py` 则负责将这些节点和它们之间的条件路由关系（`add_conditional_edges`）连接起来，构成完整的 LangGraph 计算图。
- **`Tools` (`src/tools/`)**: **原子能力**的集合。每个工具都是一个独立的函数，封装了对底层 API（如生图、TTS）的调用。Agent 通过调用这些工具来完成任务。

---

## 3. 执行流程深度解析

### 3.1. 路径一：简单任务委派 (Fast-Path)

**场景**: 用户输入 "画一只可爱的猫"。

1. **`main.py`** -> **`run_agent_workflow_async`**: 接收输入，初始化 `State` 对象。
2. **`coordinator_node`**:
   - 接收到 `State`。
   - 调用 `coordinator_agent`，其 LLM 根据提示词 (`prompts/coordinator.md`) 和用户输入，判断出这是一个单步图像任务。
   - LLM 调用 `Decision` 工具，返回决策对象: `Decision(next_step='delegate_to_agent', target_agent='visual_agent', task_description='画一只可爱的猫')`。
   - 此决策被存入 `state.coordinator_decision`。
3. **条件路由 (`route_from_coordinator`)**:
   - 读取 `state.coordinator_decision.next_step`，返回字符串 "prepare_delegated_task"。
   - 图路由到 `prepare_delegated_task` 节点。
4. **`prepare_delegated_task`**:
   - 读取 `state.coordinator_decision`。
   - 将 `task_description` 包装成一个对 Agent 更友好的格式。
   - 更新 `State`: `state.current_task_instruction` 和 `state.delegated_agent_name` 被赋值。
5. **条件路由 (`route_to_delegated_agent`)**:
   - 读取 `state.delegated_agent_name` (`visual_agent`)，返回字符串 "visual_agent"。
   - 图路由到 `visual_agent_node`。
6. **`visual_agent_node`** -> **`_execute_agent_step`**:
   - 调用 `visual_agent` 这个 LangChain Agent。
   - `visual_agent` 根据其提示词 (`prompts/visual_creator_prompt.md`) 和 `current_task_instruction`，决定调用 `jmeng_image_generator` 工具。
   - 工具执行，返回图片 URL。
   - `_execute_agent_step` **捕获完整的执行过程**，更新 `state.tasks_completed` 列表，其中包含 `output` (最终回复) 和 `tool_calls` (详细的工具调用日志)。
7. **`route_after_agent`**:
   - 检查到 `state.plan` 为空，判定这是一个委派任务。
   - 返回一个路由决策，指引工作流到 `final_responder_node`。
8. **`final_responder_node`**:
   - **从 `state.tasks_completed` 中读取到完整的执行结果**。
   - 构建最终摘要，调用 `responder_llm` 生成对用户友好的回复。
   - 将最终回复更新到 `state.messages` 中并返回。

### 3.2. 路径二：复杂任务规划

**场景**: 用户输入 "做一个哪吒MV"。

1. **`coordinator_node`**:
   - 同上，但这次 LLM 决策为: `Decision(next_step='planner')`。
2. **条件路由 (`route_from_coordinator`)**: 返回 "planner"，图路由到 `planner_node`。
3. **`planner_node`**:
   - 调用 `planner` Agent（使用 `prompts/art_planner_prompt_zh.md`）。
   - LLM 生成一个多步骤的 `Plan` 对象。
   - `Plan` 对象被存入 `state.plan`。
4. **(可选) `human_feedback_node`**: 暂停执行，等待用户确认计划。
5. **`prepare_for_agent_execution`**:
   - 执行循环的入口。读取 `state.plan` 和 `state.current_task_index`。
   - 提取出当前步骤的指令，存入 `state.current_task_instruction`。
6. **条件路由 (`route_tasks`)**:
   - 根据当前步骤的 `step_type` (e.g., `VISUAL`, `AUDIO`)，返回对应的 Agent 节点名 (e.g., "visual_agent")。
7. **`visual_agent_node` / `audio_agent_node` ...**:
   - 执行步骤，逻辑同 **3.1.6**。
   - 执行完成后，`current_task_index` 加一。
8. **返回循环**: 流程重新回到 `prepare_for_agent_execution` (步骤5)，形成循环，直到所有计划步骤完成。
9. **`reporter_node` & `final_responder_node`**:
   - 所有步骤完成后，路由到最终的响应节点，生成最终回复。

---

## 4. 如何运行与调试

### 4.1. 环境设置

1. **克隆项目**
2. **创建虚拟环境**: `python -m venv .venv && source .venv/bin/activate`
3. **安装依赖**: `pip install -e .` (这会安装 `pyproject.toml` 中定义的所有依赖)
4. **配置环境变量**:
   - 复制 `env.example` 为 `.env`。
   - 填入所有必需的 API Key.
   - **强烈建议**配置 `LANGCHAIN_API_KEY`，启用 [LangSmith](https://smith.langchain.com/)  tracing，这将为调试提供极大的便利。

### 4.2. 运行

- **交互模式**:

  ```bash
  python main.py --interactive
  ```

  启动后会显示欢迎语，并进入问答循环。
- **单次执行**:

  ```bash
  python main.py "你的问题"
  ```

### 4.3. 调试技巧

1. **LangSmith**: 如果已配置，这是**首选的调试工具**。你可以清晰地看到每个节点的输入输出、LLM 的思考过程、工具的调用详情和耗时。
2. **控制台日志**: `main.py` 中默认开启了 `set_debug(True)` 和 `set_verbose(True)`，控制台会打印详细的日志流。
3. **关键日志解读**:
   - `Coordinator decided on next step: ...`: 查看 Coordinator 的决策是否符合预期。
   - `Routing to ...`: 确认图的路由是否正确。
   - `INFO - Step X completed by Y`: 确认步骤是否完成。
   - `Final report text passed to responder: ...`: **这是最重要的调试信息之一**。检查这里的内容，可以判断出最终节点拿到的状态是否完整、正确。

---

## 5. 关键文件结构

```
deer-flow/
├── main.py           # 应用主入口，处理用户交互。
├── README.md         # (本文件) 开发者文档。
├── FRAMEWORK.md      # 框架扩展指南。
├── pyproject.toml    # 项目依赖与配置。
├── .env              # 你的本地环境变量 (从 env.example 复制)。
└── src/
    ├── agents/
    │   └── agents.py # 定义 Coordinator, Planner, Specialist Agents 的核心逻辑。
    ├── graph/
    │   ├── builder.py# **核心**：构建和连接 LangGraph 的节点与边。
    │   ├── nodes.py  # **核心**：定义图中各节点的具体业务逻辑。
    │   └── types.py  # **核心**：定义中央 State 数据结构。
    ├── llm/
    │   └── llm.py    # LLM 客户端的封装与获取。
    ├── prompts/      # 存放所有 Prompt .md 模板。
    ├── tools/
    │   └── __init__.py # 注册和初始化所有工具。
    └── workflow.py     # 封装了 `run_agent_workflow_async` 核心工作流函数。
```
