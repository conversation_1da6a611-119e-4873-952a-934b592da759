#!/usr/bin/env python3
"""
测试JSON解析问题修复
"""

import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tool_description_json_safety():
    """测试工具描述的JSON安全性"""
    logger.info("🔍 测试工具描述的JSON安全性...")
    
    try:
        # 读取修复后的文件
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/audio/multi_speaker_tts.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取工具描述部分
        desc_start = content.find('description=(')
        if desc_start == -1:
            logger.error("❌ 找不到工具描述")
            return False
        
        desc_end = content.find('),', desc_start)
        if desc_end == -1:
            logger.error("❌ 找不到工具描述结束")
            return False
        
        description_section = content[desc_start:desc_end]
        logger.info(f"📏 工具描述长度: {len(description_section)} 字符")
        
        # 检查潜在的JSON解析问题
        json_safety_checks = {
            "不包含JSON示例": "{speaker:" not in description_section and "text:" not in description_section,
            "不包含单引号JSON": "'{" not in description_section and "}'" not in description_section,
            "不包含混合引号": not ("'{" in description_section or "}'" in description_section),
            "不包含裸露的大括号": description_section.count('{') == description_section.count('}') or ('{' not in description_section and '}' not in description_section),
            "不包含dialogue_script示例": "dialogue_script: [" not in description_section,
            "不包含JSON数组示例": ": [" not in description_section or "]" not in description_section
        }
        
        logger.info("🔍 JSON安全性检查:")
        all_safe = True
        for check_name, is_safe in json_safety_checks.items():
            status = "✅" if is_safe else "❌"
            logger.info(f"   {status} {check_name}")
            if not is_safe:
                all_safe = False
        
        # 检查描述内容是否仍然完整
        content_checks = {
            "包含音色库信息": "🎭 **完整音色库" in description_section,
            "包含推荐组合": "💡 **推荐声音组合：**" in description_section,
            "包含使用示例": "📝 **简单使用示例：**" in description_section,
            "包含高级控制": "🔧 **高级控制：**" in description_section,
            "包含输出内容": "📤 **输出内容：**" in description_section
        }
        
        logger.info("\n🔍 内容完整性检查:")
        for check_name, has_content in content_checks.items():
            status = "✅" if has_content else "❌"
            logger.info(f"   {status} {check_name}")
            if not has_content:
                all_safe = False
        
        return all_safe
        
    except Exception as e:
        logger.error(f"❌ JSON安全性测试失败: {e}")
        return False

def test_tool_creation():
    """测试工具创建是否成功"""
    logger.info("🔧 测试工具创建...")
    
    try:
        from src.config.configuration import Configuration
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        
        # 创建配置
        config = Configuration.from_runnable_config()
        
        # 检查配置
        has_api_key = bool(config.minimax_api_key)
        has_group_id = bool(config.minimax_group_id)
        
        logger.info(f"📋 MINIMAX_API_KEY: {'有' if has_api_key else '无'}")
        logger.info(f"📋 MINIMAX_GROUP_ID: {'有' if has_group_id else '无'}")
        
        # 尝试创建工具
        tool = get_multi_speaker_tts_tool(config)
        
        if tool is None:
            if not has_api_key or not has_group_id:
                logger.info("✅ 工具正确返回None（缺少配置）")
                return True
            else:
                logger.error("❌ 工具创建失败（配置完整但返回None）")
                return False
        else:
            logger.info("✅ 工具创建成功")
            logger.info(f"📋 工具名称: {tool.name}")
            logger.info(f"📋 工具类型: {type(tool).__name__}")
            
            # 检查工具描述长度
            desc_length = len(tool.description) if hasattr(tool, 'description') else 0
            logger.info(f"📏 工具描述长度: {desc_length} 字符")
            
            return True
        
    except Exception as e:
        logger.error(f"❌ 工具创建测试失败: {e}")
        return False

def test_audio_tools_integration():
    """测试audio_tools集成"""
    logger.info("🔗 测试audio_tools集成...")
    
    try:
        # 避免循环导入，直接检查工具实例化
        from src.config.configuration import Configuration
        from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
        from src.tools.audio.text_to_speech import get_text_to_speech_tool, get_voice_clone_tool, get_text_to_voice_tool
        from src.tools.audio.music_generation import get_suno_music_generation_tool
        
        config = Configuration.from_runnable_config()
        
        # 模拟__init__.py中的工具创建过程
        suno_tool = get_suno_music_generation_tool(config=config)
        tts_tool = get_text_to_speech_tool(config=config)
        voice_clone_tool = get_voice_clone_tool(config=config)
        text_to_voice_tool = get_text_to_voice_tool(config=config)
        multi_speaker_tts_tool = get_multi_speaker_tts_tool(config=config)
        
        # 创建audio_tools列表
        audio_tools = [suno_tool, tts_tool, voice_clone_tool, text_to_voice_tool, multi_speaker_tts_tool]
        audio_tools = [tool for tool in audio_tools if tool is not None]
        
        logger.info(f"📋 audio_tools总数: {len(audio_tools)}")
        
        # 检查每个工具
        for i, tool in enumerate(audio_tools):
            tool_name = getattr(tool, 'name', f'Tool_{i}')
            logger.info(f"   {i+1}. {tool_name}")
        
        # 检查multi_speaker_tts是否在列表中
        multi_tts_in_list = any(
            getattr(tool, 'name', '') == 'multi_speaker_tts' 
            for tool in audio_tools
        )
        
        if multi_tts_in_list:
            logger.info("✅ multi_speaker_tts工具已正确集成到audio_tools")
        else:
            logger.warning("⚠️ multi_speaker_tts工具未在audio_tools中（可能因配置缺失）")
        
        return len(audio_tools) > 0
        
    except Exception as e:
        logger.error(f"❌ audio_tools集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🎯 开始JSON解析问题修复测试...")
    
    # 测试步骤
    tests = [
        ("工具描述JSON安全性", test_tool_description_json_safety),
        ("工具创建", test_tool_creation),
        ("audio_tools集成", test_audio_tools_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 开始测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 测试通过")
            else:
                logger.error(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("📊 JSON解析问题修复测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("🎉 JSON解析问题修复完全成功！")
        logger.info("")
        logger.info("🚀 **修复效果:**")
        logger.info("   ✅ 移除了工具描述中的JSON示例")
        logger.info("   ✅ 消除了潜在的JSON解析冲突")
        logger.info("   ✅ 保持了工具描述的完整性")
        logger.info("   ✅ 工具正确集成到audio_tools")
        logger.info("")
        logger.info("🎭 现在应该不会再出现JSON解析错误了！")
        logger.info("   建议重新测试相声生成功能")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
